<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-parent</artifactId>
    <version>2.1.7.RELEASE</version>
  </parent>
  <groupId>databridge-server</groupId>
  <artifactId>databridge-server</artifactId>
  <version>0.0.1-SNAPSHOT</version>
  <packaging>jar</packaging>

  <name>databridge-server</name>
  <url>http://maven.apache.org</url>

  <properties>
      <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
      <hutool.version>5.7.8</hutool.version>
      <log4j.version>1.2.17</log4j.version>
      <log4j2.version>2.17.0</log4j2.version>
  </properties>

  <dependencies>
      <dependency>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-starter-web</artifactId>
      </dependency>
      <dependency>
          <groupId>mysql</groupId>
          <artifactId>mysql-connector-java</artifactId>
      </dependency>
      <!--引入druid数据源-->
      <dependency>
          <groupId>com.alibaba</groupId>
          <artifactId>druid-spring-boot-starter</artifactId>
          <version>1.2.9</version>
      </dependency>
      <!-- mybatis-plus -->
      <dependency>
          <groupId>org.mybatis.spring.boot</groupId>
          <artifactId>mybatis-spring-boot-autoconfigure</artifactId>
          <version>2.1.1</version>
      </dependency>
      <dependency>
          <groupId>com.baomidou</groupId>
          <artifactId>mybatis-plus-boot-starter</artifactId>
          <version>3.4.3</version>
          <exclusions>
              <exclusion>
                  <groupId>com.github.jsqlparser</groupId>
                  <artifactId>jsqlparser</artifactId>
              </exclusion>
          </exclusions>
      </dependency>
      <!--lombok-->
      <dependency>
          <groupId>org.projectlombok</groupId>
          <artifactId>lombok</artifactId>
          <scope>provided</scope>
      </dependency>
      <dependency>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-starter-test</artifactId>
          <scope>test</scope>
      </dependency>
      <!--fastjson依赖-->
      <dependency>
          <groupId>com.alibaba</groupId>
          <artifactId>fastjson</artifactId>
          <version>1.2.83</version>
      </dependency>
      <!-- 工具包 -->
      <dependency>
          <groupId>cn.hutool</groupId>
          <artifactId>hutool-poi</artifactId>
          <version>${hutool.version}</version>
      </dependency>
      <dependency>
          <groupId>cn.hutool</groupId>
          <artifactId>hutool-core</artifactId>
          <version>${hutool.version}</version>
      </dependency>
      <dependency>
          <groupId>cn.hutool</groupId>
          <artifactId>hutool-core</artifactId>
          <version>${hutool.version}</version>
      </dependency>
      <dependency>
          <groupId>org.apache.poi</groupId>
          <artifactId>poi</artifactId>
          <version>4.0.0</version>
      </dependency>
      <dependency>
          <groupId>org.apache.poi</groupId>
          <artifactId>poi-ooxml</artifactId>
          <version>4.0.0</version>
      </dependency>
      <dependency>
          <groupId>commons-beanutils</groupId>
          <artifactId>commons-beanutils</artifactId>
          <version>1.9.4</version>
      </dependency>
      <!-- http依赖-->
      <dependency>
          <groupId>org.apache.httpcomponents</groupId>
          <artifactId>fluent-hc</artifactId>
          <version>4.5.9</version>
      </dependency>
      <!-- 校验依赖 -->
      <dependency>
          <groupId>commons-validator</groupId>
          <artifactId>commons-validator</artifactId>
          <version>1.7</version>
      </dependency>
      <!-- 切面依赖 -->
      <dependency>
          <groupId>org.aspectj</groupId>
          <artifactId>aspectjweaver</artifactId>
      </dependency>
      <!-- 阿波罗 -->
      <dependency>
          <groupId>com.ctrip.framework.apollo</groupId>
          <artifactId>apollo-client</artifactId>
          <version>2.0.1</version>
      </dependency>
      <dependency>
          <groupId>org.springframework.cloud</groupId>
          <artifactId>spring-cloud-starter-config</artifactId>
          <version>2.0.0.RELEASE</version>
      </dependency>
      <!-- 分页依赖 -->
      <dependency>
          <groupId>com.github.pagehelper</groupId>
          <artifactId>pagehelper-spring-boot-starter</artifactId>
          <version>1.4.5</version>
          <exclusions>
              <exclusion>
                  <groupId>org.mybatis.spring.boot</groupId>
                  <artifactId>mybatis-spring-boot-starter</artifactId>
              </exclusion>
          </exclusions>
      </dependency>
      <!-- 加解密 begin -->
      <dependency>
          <groupId>com.welab</groupId>
          <artifactId>welab-privacy-api</artifactId>
          <version>1.0.8-RELEASE</version>
      </dependency>
      <dependency>
          <groupId>com.welab</groupId>
          <artifactId>welab-privacy-lib</artifactId>
          <version>1.0.1-RELEASE</version>
      </dependency>
      <!-- 加解密 end -->
      <!-- dubbo 依赖 -->
      <dependency>
          <groupId>com.alibaba.spring.boot</groupId>
          <artifactId>dubbo-spring-boot-starter</artifactId>
          <version>2.0.0</version>
          <exclusions>
              <exclusion>
                  <groupId>com.alibaba</groupId>
                  <artifactId>dubbo</artifactId>
              </exclusion>
          </exclusions>
      </dependency>
      <dependency>
          <groupId>com.alibaba</groupId>
          <artifactId>dubbo</artifactId>
          <version>2.8.4</version>
          <exclusions>
              <exclusion>
                  <groupId>com.google.guava</groupId>
                  <artifactId>guava</artifactId>
              </exclusion>
          </exclusions>
      </dependency>
      <!--zookeeper-->
      <dependency>
          <groupId>org.apache.zookeeper</groupId>
          <artifactId>zookeeper</artifactId>
          <version>3.4.13</version>
          <exclusions>
              <exclusion>
                  <groupId>org.slf4j</groupId>
                  <artifactId>slf4j-log4j12</artifactId>
              </exclusion>
          </exclusions>
      </dependency>
      <!--zookeeper 底层依赖curator-->
      <dependency>
          <groupId>org.apache.curator</groupId>
          <artifactId>curator-framework</artifactId>
          <version>2.10.0</version>
      </dependency>
      <dependency>
          <groupId>org.apache.curator</groupId>
          <artifactId>curator-recipes</artifactId>
          <version>2.10.0</version>
      </dependency>
      <dependency>
          <groupId>com.101tec</groupId>
          <artifactId>zkclient</artifactId>
          <version>0.11</version>
      </dependency>
      <!-- user-center dubbo api -->
      <dependency>
          <groupId>com.welab</groupId>
          <artifactId>user-center-api</artifactId>
          <version>2.8.3-RELEASE</version>
      </dependency>
      <!-- document dubbo api -->
      <dependency>
          <groupId>com.welab</groupId>
          <artifactId>document-api</artifactId>
          <version>1.3.26-SNAPSHOT</version>
          <exclusions>
              <exclusion>
                  <groupId>javax.mail</groupId>
                  <artifactId>mailapi</artifactId>
              </exclusion>
          </exclusions>
      </dependency>
      <dependency>
          <groupId>com.welab</groupId>
          <artifactId>welab-anti-fraud-api</artifactId>
          <version>1.1.3-SNAPSHOT</version>
      </dependency>
      <dependency>
          <groupId>com.welab</groupId>
          <artifactId>approval-center-api</artifactId>
          <version>1.4.7-RELEASE</version>
          <exclusions>
              <exclusion>
                  <groupId>com.welab</groupId>
                  <artifactId>welab-springboot-actuator</artifactId>
              </exclusion>
              <exclusion>
                  <groupId>com.welab</groupId>
                  <artifactId>welab-springboot-web</artifactId>
              </exclusion>
          </exclusions>
      </dependency>
      <!-- 阿里云oss -->
      <dependency>
          <groupId>com.aliyun.oss</groupId>
          <artifactId>aliyun-sdk-oss</artifactId>
          <version>3.10.2</version>
      </dependency>
      <dependency>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-starter-mail</artifactId>
      </dependency>

      <!--java封装的 FFmpeg 工具，用于录音处理-->
      <dependency>
          <groupId>ws.schild</groupId>
          <artifactId>jave-all-deps</artifactId>
          <version>3.3.1</version>
      </dependency>
      <dependency>
          <groupId>com.welab</groupId>
          <artifactId>welab-logback</artifactId>
          <version>1.0-RELEASE</version>
      </dependency>
  </dependencies>
  	<build>
		<finalName>databridge-server</finalName>
        <plugins>
            <plugin>
            	<groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.2</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
	</build>
</project>
