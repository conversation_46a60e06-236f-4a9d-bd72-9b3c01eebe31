#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1048576 bytes for AllocateHeap
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (memory/allocation.inline.hpp:61), pid=26016, tid=0x0000000000005c18
#
# JRE version:  (8.0_301-b09) (build )
# Java VM: Java HotSpot(TM) 64-Bit Server VM (25.301-b09 mixed mode windows-amd64 compressed oops)
# Failed to write core dump. Minidumps are not enabled by default on client versions of Windows
#

---------------  T H R E A D  ---------------

Current thread (0x000001f528105800):  JavaThread "Unknown thread" [_thread_in_vm, id=23576, stack(0x000000b93aa00000,0x000000b93ab00000)]

Stack: [0x000000b93aa00000,0x000000b93ab00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x32ed79]
V  [jvm.dll+0x275722]
V  [jvm.dll+0x2763cd]
V  [jvm.dll+0x26ce05]
V  [jvm.dll+0x509d]
V  [jvm.dll+0x3d3423]
V  [jvm.dll+0x3d3e38]
V  [jvm.dll+0xfcdcf]
V  [jvm.dll+0x20a0bb]
V  [jvm.dll+0x238ae7]
V  [jvm.dll+0x162876]
C  [java.exe+0x2312]
C  [java.exe+0x15f54]
C  [KERNEL32.DLL+0x17374]
C  [ntdll.dll+0x4cc91]


---------------  P R O C E S S  ---------------

Java Threads: ( => current thread )

Other Threads:

=>0x000001f528105800 (exited) JavaThread "Unknown thread" [_thread_in_vm, id=23576, stack(0x000000b93aa00000,0x000000b93ab00000)]

VM state:not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

heap address: 0x00000006c3200000, size: 4046 MB, Compressed Oops mode: Zero based, Oop shift amount: 3
Narrow klass base: 0x0000000000000000, Narrow klass shift: 3
Compressed class space size: 1073741824 Address: 0x00000007c0000000

Heap:
 PSYoungGen      total 75776K, used 1300K [0x000000076bb80000, 0x0000000771000000, 0x00000007c0000000)
  eden space 65024K, 2% used [0x000000076bb80000,0x000000076bcc51f8,0x000000076fb00000)
  from space 10752K, 0% used [0x0000000770580000,0x0000000770580000,0x0000000771000000)
  to   space 10752K, 0% used [0x000000076fb00000,0x000000076fb00000,0x0000000770580000)
 ParOldGen       total 173568K, used 0K [0x00000006c3200000, 0x00000006cdb80000, 0x000000076bb80000)
  object space 173568K, 0% used [0x00000006c3200000,0x00000006c3200000,0x00000006cdb80000)
 Metaspace       used 788K, capacity 4480K, committed 4480K, reserved 1056768K
  class space    used 76K, capacity 384K, committed 384K, reserved 1048576K

Card table byte_map: [0x000001f539110000,0x000001f539900000] byte_map_base: 0x000001f535af7000

Marking Bits: (ParMarkBitMap*) 0x0000000055c97fe0
 Begin Bits: [0x000001f539e50000, 0x000001f53dd88000)
 End Bits:   [0x000001f53dd88000, 0x000001f541cc0000)

Polling page: 0x000001f529d20000

CodeCache: size=245760Kb used=328Kb max_used=328Kb free=245431Kb
 bounds [0x000001f529d50000, 0x000001f529fc0000, 0x000001f538d50000]
 total_blobs=58 nmethods=0 adapters=38
 compilation: enabled

Compilation events (0 events):
No events

GC Heap History (0 events):
No events

Deoptimization events (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

Events (10 events):
Event: 0.026 loading class java/lang/Short
Event: 0.026 loading class java/lang/Short done
Event: 0.026 loading class java/lang/Integer
Event: 0.026 loading class java/lang/Integer done
Event: 0.026 loading class java/lang/Long
Event: 0.026 loading class java/lang/Long done
Event: 0.027 loading class java/lang/NullPointerException
Event: 0.027 loading class java/lang/NullPointerException done
Event: 0.027 loading class java/lang/ArithmeticException
Event: 0.027 loading class java/lang/ArithmeticException done


Dynamic libraries:
0x00007ff7b9c30000 - 0x00007ff7b9c77000 	D:\software\jdk\bin\java.exe
0x00007fff7af30000 - 0x00007fff7b128000 	C:\windows\SYSTEM32\ntdll.dll
0x00007fff7adc0000 - 0x00007fff7ae82000 	C:\windows\System32\KERNEL32.DLL
0x00007fff78940000 - 0x00007fff78c36000 	C:\windows\System32\KERNELBASE.dll
0x00007fff78ff0000 - 0x00007fff790a1000 	C:\windows\System32\ADVAPI32.dll
0x00007fff78f50000 - 0x00007fff78fee000 	C:\windows\System32\msvcrt.dll
0x00007fff790b0000 - 0x00007fff7914f000 	C:\windows\System32\sechost.dll
0x00007fff7a620000 - 0x00007fff7a743000 	C:\windows\System32\RPCRT4.dll
0x00007fff78ef0000 - 0x00007fff78f17000 	C:\windows\System32\bcrypt.dll
0x00007fff792c0000 - 0x00007fff7945d000 	C:\windows\System32\USER32.dll
0x00007fff78f20000 - 0x00007fff78f42000 	C:\windows\System32\win32u.dll
0x00007fff7a4c0000 - 0x00007fff7a4eb000 	C:\windows\System32\GDI32.dll
0x00007fff785c0000 - 0x00007fff786d9000 	C:\windows\System32\gdi32full.dll
0x00007fff786e0000 - 0x00007fff7877d000 	C:\windows\System32\msvcp_win.dll
0x00007fff78df0000 - 0x00007fff78ef0000 	C:\windows\System32\ucrtbase.dll
0x00007fff5d890000 - 0x00007fff5db2a000 	C:\windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7\COMCTL32.dll
0x00007fff79460000 - 0x00007fff7948f000 	C:\windows\System32\IMM32.DLL
0x00007fff38db0000 - 0x00007fff38e61000 	C:\Program Files\McAfee\Endpoint Security\Threat Prevention\Ips\EpMPApi.dll
0x00007fff78810000 - 0x00007fff78883000 	C:\windows\System32\WINTRUST.dll
0x00007fff78c90000 - 0x00007fff78ded000 	C:\windows\System32\CRYPT32.dll
0x00007fff78180000 - 0x00007fff78192000 	C:\windows\SYSTEM32\MSASN1.dll
0x0000000055d30000 - 0x0000000055d3c000 	C:\Program Files\McAfee\Endpoint Security\Threat Prevention\Ips\EpMPThe.dll
0x00007fff38d50000 - 0x00007fff38dab000 	C:\Program Files\McAfee\Endpoint Security\Threat Prevention\Ips\HIPHandlers64.dll
0x00007fff799e0000 - 0x00007fff7a14e000 	C:\windows\System32\SHELL32.dll
0x00007fff79490000 - 0x00007fff794eb000 	C:\windows\System32\SHLWAPI.dll
0x00007fff740d0000 - 0x00007fff742d1000 	C:\windows\SYSTEM32\dbghelp.dll
0x00007fff779a0000 - 0x00007fff779db000 	C:\windows\SYSTEM32\IPHLPAPI.DLL
0x00007fff5d770000 - 0x00007fff5d785000 	D:\software\jdk\jre\bin\vcruntime140.dll
0x00007fff3c780000 - 0x00007fff3c81b000 	D:\software\jdk\jre\bin\msvcp140.dll
0x00000000554b0000 - 0x0000000055d10000 	D:\software\jdk\jre\bin\server\jvm.dll
0x00007fff79200000 - 0x00007fff79208000 	C:\windows\System32\PSAPI.DLL
0x00007fff5dba0000 - 0x00007fff5dba9000 	C:\windows\SYSTEM32\WSOCK32.dll
0x00007fff709e0000 - 0x00007fff709ea000 	C:\windows\SYSTEM32\VERSION.dll
0x00007fff794f0000 - 0x00007fff7955b000 	C:\windows\System32\WS2_32.dll
0x00007fff521a0000 - 0x00007fff521c7000 	C:\windows\SYSTEM32\WINMM.dll
0x00007fff76200000 - 0x00007fff76212000 	C:\windows\SYSTEM32\kernel.appcore.dll
0x00007fff5d760000 - 0x00007fff5d770000 	D:\software\jdk\jre\bin\verify.dll
0x00007fff53170000 - 0x00007fff5319b000 	D:\software\jdk\jre\bin\java.dll
0x00007fff4b660000 - 0x00007fff4b696000 	D:\software\jdk\jre\bin\jdwp.dll
0x00007fff6e650000 - 0x00007fff6e659000 	D:\software\jdk\jre\bin\npt.dll
0x00007fff54170000 - 0x00007fff541a0000 	D:\software\jdk\jre\bin\instrument.dll
0x00007fff50180000 - 0x00007fff50198000 	D:\software\jdk\jre\bin\zip.dll
0x00007fff78780000 - 0x00007fff78802000 	C:\windows\System32\bcryptPrimitives.dll

VM Arguments:
jvm_args: -agentlib:jdwp=transport=dt_socket,address=127.0.0.1:7631,suspend=y,server=n -javaagent:D:\software\idea\ideaIU-2024.1.4.win\plugins\java\lib\rt\debugger-agent.jar=file:///C:/Users/<USER>/AppData/Local/Temp/capture942941622711050927.props -ea -Didea.test.cyclic.buffer.size=1048576 -Dkotlinx.coroutines.debug.enable.creation.stack.trace=false -Ddebugger.agent.enable.coroutines=true -Dkotlinx.coroutines.debug.enable.flows.stack.trace=true -Dkotlinx.coroutines.debug.enable.mutable.state.flows.stack.trace=true -Dfile.encoding=UTF-8 
java_command: com.intellij.rt.junit.JUnitStarter -ideVersion5 -junit4 com.welab.databridge.help.AgentAudioHelperTest,testParseAgentFileName
java_class_path (initial): C:\Users\<USER>\AppData\Local\Temp\classpath1090365892.jar;D:\software\idea\ideaIU-2024.1.4.win\plugins\java\lib\rt\debugger-agent.jar
Launcher Type: SUN_STANDARD

Environment Variables:
JAVA_HOME=D:\software\jdk
PATH=D:\software\shadowbot;C:\Python312\Scripts\;C:\Python312\;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\windows\system32;C:\windows;C:\windows\System32\Wbem;C:\windows\System32\WindowsPowerShell\v1.0\;C:\windows\System32\OpenSSH\;C:\Program Files\TortoiseGit\bin;D:\software\maven\apache-maven-3.8.6-bin\apache-maven-3.8.6\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310;D:\download\ffmpeg-master-latest-win64-gpl-shared\ffmpeg-master-latest-win64-gpl-shared\bin;C:\Program Files\dotnet\;D:\software\Git\cmd;D:\software\nodejs\;D:\software\pandoc\;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;D:\nodejs\node_global\;D:\software\shadowbot;C:\Users\<USER>\AppData\Local\UniGetUI\Chocolatey\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\download\OneCommander3.23.0.0;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe;C:\Users\<USER>\AppData\Roaming\npm;
USERNAME=leon.li
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 140 Stepping 1, GenuineIntel



---------------  S Y S T E M  ---------------

OS: Windows 10.0 , 64 bit Build 19041 (10.0.19041.5915)

CPU:total 8 (initial active 8) (4 cores per cpu, 2 threads per core) family 6 model 140 stepping 1, cmov, cx8, fxsr, mmx, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, avx, avx2, aes, clmul, erms, 3dnowpref, lzcnt, ht, tsc, tscinvbit, bmi1, bmi2, adx

Memory: 4k page, physical 16571704k(1168024k free), swap 27057464k(3176k free)

vm_info: Java HotSpot(TM) 64-Bit Server VM (25.301-b09) for windows-amd64 JRE (1.8.0_301-b09), built on Jun  9 2021 06:46:21 by "java_re" with MS VC++ 15.9 (VS2017)

time: Tue Aug  5 17:34:29 2025
timezone: �й���׼ʱ��
elapsed time: 0.043570 seconds (0d 0h 0m 0s)

