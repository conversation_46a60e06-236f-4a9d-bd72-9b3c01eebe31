package com.welab.databridge.common;

/**
 * <AUTHOR>
 * @Description 响应编码
 * @Date 2022/3/15 下午4:31
 */
public enum ResultCode {
    // 业务系统代码 90xx业务系统自定义错误码
    // http状态返回代码 91xx（临时响应）表示临时响应并需要请求者继续执行操作的状态代码。
    // http状态返回代码 92xx （成功）表示成功处理了请求的状态代码。
    // http状态返回代码 93xx （重定向）表示要完成请求，需要进一步操作。 通常，这些状态代码用来重定向。
    // http状态返回代码 94xx（请求错误）这些状态代码表示请求可能出错，妨碍了服务器的处理。
    // http状态返回代码 95xx（服务器错误）这些状态代码表示服务器在尝试处理请求时发生内部错误。 这些错误可能是服务器本身的错误，而不是请求出错。
    AUTH_FAIL(9401, "登录过期,请重新登陆!"),
    DATA_NOT_EXIST(9404, "数据不存在"),
    DATA_DELETED(9405, "数据已删除"),
    DATA_IN_REVIEW(9406, "数据数据审核中，无法编辑"),
    PARAM_ERROR(9412, "参数错误"),
    NOT_ALLOW_UPLOAD_TYPE(9415, "不允许的文件类型!"),

    SYSTEM_EXCEPTION(9500, "系统异常,请稍后再试!"),
    SYSTEM_SERVICE_EXCEPTION(9501, "系统繁忙,请稍后再试!"),
    SYSTEM_DB_EXCEPTION(9502, "系统错误,请稍后再试!"),
    SYSTEM_ERROR(9503, "系统出错,请稍后再试!"),
    SYSTEM_BUSY(9504, "请求过于频繁,请稍后再试!"),
    SYSTEM_TIME_OUT(9505, "处理超时,请稍后再试!"),

    SUCCESS(9200, "处理成功"),
    FAILED(9001, "处理失败"),
    DATA_EXIST(9002, "数据已存在"),
    DATA_NULL(9004, "数据为空"),
    REQUEST_REPEAT(9006, "频繁操作"),
    DATA_ONLINE(9016, "已上线，无法操作"),
    IN_USE(9020, "使用中，无法操作"),
    IN_AUDIT(9021, "审核中，无法操作"),
    AUDIT_SUCCESS(0, "成功"),
    AUDIT_FAILED(-1, "业务系统异常信息"),
    AUDIT_INVALID_CODE(-1, "审核编码异常"),
    INTERCEPT_REQUEST_TIME_START(9021, "合作方推单开始时间不能为空"),
    INTERCEPT_REQUEST_TIME_END(9022, "合作方推单结束时间不能为空"),
    PRODUCT_NOT_EXIST(9023, "产品不存在或未上架或正在审核中"),
    EXPORT_FAIL(9024, "导出失败，请重试"),
    EXPORT_LIMIT(9026, "最多支持导出1万条数据，请重新筛选"),
    EXPORT_NULL(9028, "导出数据为空，请重新筛选"),
    OPERATOR_CANNOT_DELETE(9030, "仅上传人有权删除"),
    DEPARTMENT_ERROR(9032, "请指定正确的组织名称"),
    FILE_TYPE_LIMIT(9033, "文件类型有误"),
    FILE_IS_EMPTY(9034, "上传文件不能为空"),
    FILE_NAME_ERROR(9035, "文件名需包含手机号和接听时间"),
    FILE_UPLOAD_FAIL(9036, "文件上传失败"),
    FILE_ANALYSIS_FAIL(9037, "文件解析异常"),
    PARENT_DEPARTMENT_ERROR(9038, "上级部门不能为下级部门，请重新选择"),
    AUDIO_DOWNLOAD_LIMIT(9039, "用户单日下载语音数量超限"),

    PHONE_EMPTY(9040, "手机号为空"),
    PHONE_ERROR(9040, "手机号有误"),
    SEND_TIME_EMPTY(9041, "发送时间为空"),
    SEND_TIME_ERROR(9041, "发送时间格式有误"),
    SEND_TIME_LATER(9041, "发送时间不能晚于上传时间"),
    SEND_CONTENT_EMPTY(9042, "发送内容为空"),
    SEND_CONTENT_ERROR(9042, "发送内容长度超限"),
    STATUS_EMPTY(9043, "发送状态为空"),
    STATUS_UNKNOWN(9043, "发送状态未识别"),
    DEPARTMENT_EMPTY(9044, "组织为空"),
    SUPPLIER_EMPTY(9045, "供应商为空"),
    AUDIO_IS_ARCHIVE(9046, "三年前的语音文件已归档，正在解冻请稍后三分钟"),
    ;

    private int code;
    private String message;

    ResultCode(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    @Override
    public String toString() {
        return "code:"+code+" message:"+message;
    }


//    http状态返回代码 代码   说明
//100   （继续） 请求者应当继续提出请求。 服务器返回此代码表示已收到请求的第一部分，正在等待其余部分。
//            101   （切换协议） 请求者已要求服务器切换协议，服务器已确认并准备切换。
//
//    http状态返回代码 2xx （成功）
//    表示成功处理了请求的状态代码。
//
//    http状态返回代码 代码   说明
//200   （成功）  服务器已成功处理了请求。 通常，这表示服务器提供了请求的网页。
//            201   （已创建）  请求成功并且服务器创建了新的资源。
//            202   （已接受）  服务器已接受请求，但尚未处理。
//            203   （非授权信息）  服务器已成功处理了请求，但返回的信息可能来自另一来源。
//            204   （无内容）  服务器成功处理了请求，但没有返回任何内容。
//            205   （重置内容） 服务器成功处理了请求，但没有返回任何内容。
//            206   （部分内容）  服务器成功处理了部分 GET 请求。
//
//    http状态返回代码 3xx （重定向）
//    表示要完成请求，需要进一步操作。 通常，这些状态代码用来重定向。
//
//    http状态返回代码 代码   说明
//300   （多种选择）  针对请求，服务器可执行多种操作。 服务器可根据请求者 (user agent) 选择一项操作，或提供操作列表供请求者选择。
//            301   （永久移动）  请求的网页已永久移动到新位置。 服务器返回此响应（对 GET 或 HEAD 请求的响应）时，会自动将请求者转到新位置。
//            302   （临时移动）  服务器目前从不同位置的网页响应请求，但请求者应继续使用原有位置来进行以后的请求。
//            303   （查看其他位置） 请求者应当对不同的位置使用单独的 GET 请求来检索响应时，服务器返回此代码。
//
//            304   （未修改） 自从上次请求后，请求的网页未修改过。 服务器返回此响应时，不会返回网页内容。
//            305   （使用代理） 请求者只能使用代理访问请求的网页。 如果服务器返回此响应，还表示请求者应使用代理。
//            307   （临时重定向）  服务器目前从不同位置的网页响应请求，但请求者应继续使用原有位置来进行以后的请求。
//    http状态返回代码 4xx（请求错误）
//    这些状态代码表示请求可能出错，妨碍了服务器的处理。
//
//    http状态返回代码 代码   说明
//400   （错误请求） 服务器不理解请求的语法。
//            401   （未授权） 请求要求身份验证。 对于需要登录的网页，服务器可能返回此响应。
//            403   （禁止） 服务器拒绝请求。
//            404   （未找到） 服务器找不到请求的网页。
//            405   （方法禁用） 禁用请求中指定的方法。
//            406   （不接受） 无法使用请求的内容特性响应请求的网页。
//            407   （需要代理授权） 此状态代码与 401（未授权）类似，但指定请求者应当授权使用代理。
//            408   （请求超时）  服务器等候请求时发生超时。
//            409   （冲突）  服务器在完成请求时发生冲突。 服务器必须在响应中包含有关冲突的信息。
//            410   （已删除）  如果请求的资源已永久删除，服务器就会返回此响应。
//            411   （需要有效长度） 服务器不接受不含有效内容长度标头字段的请求。
//            412   （未满足前提条件） 服务器未满足请求者在请求中设置的其中一个前提条件。
//            413   （请求实体过大） 服务器无法处理请求，因为请求实体过大，超出服务器的处理能力。
//            414   （请求的 URI 过长） 请求的 URI（通常为网址）过长，服务器无法处理。
//            415   （不支持的媒体类型） 请求的格式不受请求页面的支持。
//            416   （请求范围不符合要求） 如果页面无法提供请求的范围，则服务器会返回此状态代码。
//            417   （未满足期望值） 服务器未满足"期望"请求标头字段的要求。
//
//    http状态返回代码 5xx（服务器错误）
//    这些状态代码表示服务器在尝试处理请求时发生内部错误。 这些错误可能是服务器本身的错误，而不是请求出错。
//    http状态返回代码 代码   说明
//500   （服务器内部错误）  服务器遇到错误，无法完成请求。
//            501   （尚未实施） 服务器不具备完成请求的功能。 例如，服务器无法识别请求方法时可能会返回此代码。
//            502   （错误网关） 服务器作为网关或代理，从上游服务器收到无效响应。
//            503   （服务不可用） 服务器目前无法使用（由于超载或停机维护）。 通常，这只是暂时状态。
//            504   （网关超时）  服务器作为网关或代理，但是没有及时从上游服务器收到请求。
//            505   （
//    org.apache.http.protocol.HTTP 版本不受支持） 服务器不支持请求中所用的 HTTP 协议版本。
}
