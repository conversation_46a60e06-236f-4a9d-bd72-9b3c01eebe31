package com.welab.databridge.common;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description 分页显示
 * @date 2021/8/21 上午9:34
 */
public class PageVo<T> {
    /**
     * 当前页
     */
    private Integer pageNum = 1;
    /**
     * 每页的数量
     */
    private Integer pageSize = 20;
    /**
     * 当前页记录数
     */
    private int size = 0;
    /**
     * 总条数
     */
    private Integer total = 0;
    /**
     * 是否为第一页
     */
    private boolean isFirstPage = false;
    /**
     * 是否为最后一页
     */
    private boolean isLastPage = false;
    /**
     * 是否有前一页
     */
    private boolean hasPreviousPage = false;
    /**
     * 是否有下一页
     */
    private boolean hasNextPage = false;
    /**
     * 总页数
     */
    private Integer pages = 0;
    /**
     * 开始索引
     */
    private Integer startIndex = 0;
    /**
     * 分页结果
     */
    private List<T> list;

    private PageVo() {
    }

    /**
     * 物理分页
     * @param pageNum
     * @param pageSize
     * @param total
     * @param list
     */
    public PageVo(Integer pageNum, Integer pageSize, Integer total, List<T> list) {
        this(pageNum, pageSize);
        this.total = total;
        this.pages = (this.total + this.pageSize - 1) / this.pageSize;
        this.isFirstPage = (pageNum+"").equals(1+"");
        this.hasNextPage = this.pageNum < this.pages;
        this.isLastPage = (pageNum+"").equals(pages+"") || (pages+"").equals(0+"");
        this.list = list;
        this.size = null != list ? list.size() : 0;
    }

    /**
     * 内存分页
     * @param pageNum
     * @param pageSize
     * @param list
     */
    public PageVo(Integer pageNum, Integer pageSize, List<T> list) {
        this(pageNum, pageSize);
        if (null == list) {
            this.total = 0;
        } else {
            this.total = list.size();

            int toIndex = (pageNum-1)*pageSize+pageSize;
            if(list.size() >startIndex  && pageNum*pageSize>list.size()){
                toIndex = list.size();
            }else if(pageNum*pageSize > list.size()){
                this.list = new ArrayList<>();
            }
            if(!list.isEmpty()) {
                this.list = new ArrayList<>(list.subList(startIndex, toIndex));
                this.size = this.list.size();
            }
        }
        this.pages = (this.total + this.pageSize - 1) / this.pageSize;
        this.isFirstPage = (pageNum+"").equals(1+"");
        this.hasNextPage = this.pageNum < this.pages;
        this.isLastPage = (pageNum+"").equals(pages+"")  || (pages+"").equals(0+"");
    }

    private PageVo(Integer pageNum, Integer pageSize) {
        this.pageNum = (null == pageNum || pageNum < 1) ? 1 : pageNum;
        this.pageSize = (null == pageSize || pageSize < 1) ? 20 : pageSize;
        this.startIndex = (this.pageNum - 1) * this.pageSize;
        this.hasPreviousPage = !(pageNum+"").equals(1+"");
    }

    public boolean isIsFirstPage() {
        return isFirstPage;
    }

    public void setIsFirstPage(boolean isFirstPage) {
        this.isFirstPage = isFirstPage;
    }

    public boolean isIsLastPage() {
        return isLastPage;
    }

    public void setIsLastPage(boolean isLastPage) {
        this.isLastPage = isLastPage;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
        this.pages = (this.total + this.pageSize - 1) / this.pageSize;
        this.startIndex = (this.pageNum - 1) * this.pageSize;
        this.hasNextPage = this.pageNum < this.pages;
    }


    public boolean isHasNextPage() {
        return hasNextPage;
    }

    public void setHasNextPage(boolean hasNextPage) {
        this.hasNextPage = hasNextPage;
    }

    public Integer getPages() {
        return pages;
    }

    public void setPages(Integer pages) {
        this.pages = pages;
    }

    public Integer getStartIndex() {
        return startIndex;
    }

    public void setStartIndex(Integer startIndex) {
        this.startIndex = startIndex;
    }

    public List<T> getList() {
        return list;
    }

    public void setList(List<T> list) {
        this.list = list;
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }

    public boolean isHasPreviousPage() {
        return hasPreviousPage;
    }

    public void setHasPreviousPage(boolean hasPreviousPage) {
        this.hasPreviousPage = hasPreviousPage;
    }
}
