package com.welab.databridge.common;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Description 请求响应帮助类
 * @Date 2022/3/14 17:34
 */
@Slf4j
public class ResponseVo {
    private int code;
    private String message;
    private Object data;
    private String bizType;

    public ResponseVo(int code, String message, Object data){
        this.code = code;
        this.message = message;
        this.data = data;
    }

    public ResponseVo(int code, String message){
        this.code = code;
        this.message = message;
    }

    public static ResponseVo response(ResultCode resultCode) {
        ResponseVo resultObj = new ResponseVo(resultCode.getCode(), resultCode.getMessage());
        log.debug("响应内容:"+resultObj);
        return resultObj;
    }
    public static ResponseVo response(int code, String message) {
        ResponseVo resultObj = new ResponseVo(code, message, null);
        log.debug("响应内容:"+resultObj);
        return resultObj;
    }

    public static ResponseVo response(int code, String message, Object data ) {
        ResponseVo resultObj = new ResponseVo(code, message, data);
        log.debug("响应内容:"+resultObj);
        return resultObj;
    }

    public static ResponseVo response(ResultCode resultCode, Object data ) {
        ResponseVo resultObj = new ResponseVo(resultCode.getCode(), resultCode.getMessage(), data);
        log.debug("响应内容:"+resultObj);
        return resultObj;
    }

    public static ResponseVo fail() {
        ResponseVo resultObj = new ResponseVo(ResultCode.FAILED.getCode(), ResultCode.FAILED.getMessage(), null);
        log.debug("响应内容:"+resultObj);
        return resultObj;
    }

    public static ResponseVo success() {
        ResponseVo resultObj = new ResponseVo(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMessage(), null);
        log.debug("响应内容:"+resultObj);
        return resultObj;
    }

    public static ResponseVo success(Object data) {
        ResponseVo resultObj = new ResponseVo(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMessage(), data);
        log.debug("响应内容:"+resultObj);
        return resultObj;
    }

    public static ResponseVo error() {
        ResponseVo resultObj = new ResponseVo(ResultCode.SYSTEM_ERROR.getCode(), ResultCode.SYSTEM_ERROR.getMessage());
        log.debug("响应内容:"+resultObj);
        return resultObj;
    }

    public static ResponseVo error(ResultCode resultCode) {
        ResponseVo resultObj = new ResponseVo(resultCode.getCode(), resultCode.getMessage());
        log.debug("响应内容:"+resultObj);
        return resultObj;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }

    public String getBizType() {
        return bizType;
    }

    public void setBizType(String bizType) {
        this.bizType = bizType;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
