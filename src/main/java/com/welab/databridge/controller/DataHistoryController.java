package com.welab.databridge.controller;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.welab.databridge.common.ResponseVo;
import com.welab.databridge.common.ResultCode;
import com.welab.databridge.dto.DataHistoryDto;
import com.welab.databridge.entity.DataHistory;
import com.welab.databridge.service.DataHistoryService;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * 历史版本(DataHistory)表控制层
 * (代码生成器自动生成)
 *
 * <AUTHOR>
 * @date 2023-02-02 15:53:03
 */
@RestController
@RequestMapping("/dataHistory")
@Slf4j
public class DataHistoryController {
    @Resource
    private DataHistoryService service;

    /**
     * xxx列表查询
     *
     * @param userId
     * @param dto
     * @return
     */
    @PostMapping("/search")
    public ResponseVo search(@RequestHeader("x-user-id") Integer userId, @RequestBody DataHistoryDto dto) {
        try {
            log.info("xxx列表查询,userId:" + userId + ",param:" + dto);
            PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
            List<DataHistory> list = service.queryByCondition(dto);
            PageInfo<DataHistory> pageInfo = new PageInfo<>(list);
            log.info("xxx列表查询,成功!userId:" + userId + " size:" + list.size() + " param:" + dto);
            return ResponseVo.success(pageInfo);
        } catch (Exception e) {
            log.error("xxx列表查询,[Controller层]出错!userId:" + userId + ",param:" + dto + " 原因:" + e.getMessage(), e);
            return ResponseVo.response(ResultCode.SYSTEM_EXCEPTION);
        }
    }
}

