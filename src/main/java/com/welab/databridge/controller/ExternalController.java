package com.welab.databridge.controller;

import cn.hutool.core.util.ObjectUtil;
import com.welab.databridge.common.ResponseVo;
import com.welab.databridge.common.ResultCode;
import com.welab.databridge.entity.AudioRecord;
import com.welab.databridge.service.AudioRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 对外提供接口，因为调用方催收系统无法提供token，因此需在网关排除token校验
 */
@RestController
@RequestMapping("/external")
@Slf4j
public class ExternalController {
    @Resource
    private AudioRecordService audioRecordService;

    /**
     * 获取完整手机号码
     *
     * @return
     */
    @GetMapping("/mobile")
    public ResponseVo getMobile(Integer id) {
        if (id == null) {
            return ResponseVo.error(ResultCode.PARAM_ERROR);
        }

        try {
            log.info("获取完整手机号码, id:" + id);
            AudioRecord audioRecord = audioRecordService.getById(id);
            if (ObjectUtil.isEmpty(audioRecord)) {
                return ResponseVo.error(ResultCode.DATA_NOT_EXIST);
            }
            log.info("获取完整手机号码,成功! result:" + audioRecord.getPhone().replace(audioRecord.getPhone().substring(4,9), "****"));
            return ResponseVo.success(audioRecord.getPhone());
        } catch (Exception e) {
            log.error("获取完整手机号码,[Controller层]出错! 原因:" + e.getMessage(), e);
            return ResponseVo.response(ResultCode.SYSTEM_EXCEPTION);
        }
    }
}
