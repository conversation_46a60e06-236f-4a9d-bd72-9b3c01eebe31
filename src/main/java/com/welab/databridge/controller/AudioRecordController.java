package com.welab.databridge.controller;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.welab.databridge.common.ResponseVo;
import com.welab.databridge.common.ResultCode;
import com.welab.databridge.dto.AudioRecordDto;
import com.welab.databridge.entity.AudioRecord;
import com.welab.databridge.enums.SupplierType;
import com.welab.databridge.service.AudioRecordService;
import com.welab.databridge.vo.audio.QueryVo;
import com.welab.databridge.vo.common.BatchVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 音频记录(AudioRecord)表控制层
 * (代码生成器自动生成)
 *
 * <AUTHOR>
 * @date 2023-01-03 14:29:54
 */
@RestController
@RequestMapping("/audioRecord")
@Slf4j
public class AudioRecordController {
    @Resource
    private AudioRecordService service;

    /**
     * 音频记录列表查询
     *
     * @param username
     * @param dto
     * @return
     */
    @PostMapping("/search")
    public ResponseVo search(@RequestHeader("x-user-id") String userid, @RequestHeader("x-user-name") String username, @RequestBody AudioRecordDto dto) {
        try {
            log.info("音频记录列表查询,username:" + username + ", userid:" + userid);
            dto.processSearch();
            PageInfo<AudioRecord> pageInfo = service.queryByCondition(dto);
            log.info("音频记录列表查询,成功!username:" + username + ", userid:" + userid);
            return ResponseVo.success(pageInfo);
        } catch (Exception e) {
            log.error("音频记录列表查询,[Controller层]出错!username:" + username + ", userid:" + userid + ",param:" + dto + " 原因:" + e.getMessage(), e);
            return ResponseVo.response(ResultCode.SYSTEM_EXCEPTION);
        }
    }

    /**
     * 下载音频记录
     *
     * @return ResponseVo
     */
    @PostMapping("/download")
    public ResponseVo download(@RequestHeader("x-user-id") String userid, @RequestHeader("x-user-name") String username, @RequestBody BatchVo batchVo) {
        try {
            log.info("下载音频申请,username:" + username + ", userid:" + userid + ",param:" + batchVo);
            if (ObjectUtil.isEmpty(batchVo) || ObjectUtil.isEmpty(batchVo.getIds())) {
                return ResponseVo.error(ResultCode.PARAM_ERROR);
            }
            ResponseVo result = service.download(batchVo);
            log.info("下载音频申请,成功!username:" + username + ", userid:" + userid + ",param:" + batchVo);
            return result;
        } catch (Exception e) {
            log.error("下载音频申请,[Controller层]出错!username:" + username + ", userid:" + userid + ",param:" + batchVo + " 原因:" + e.getMessage(), e);
            return ResponseVo.response(ResultCode.SYSTEM_EXCEPTION);
        }
    }

    /**
     * 查询语音供应商枚举
     *
     * @return
     */
    @GetMapping("/supplier")
    public ResponseVo supplier() {
        try {
            log.info("获取语音供应商枚举");
            List<JSONObject> list = Arrays.asList(SupplierType.values())
                    .stream()
                    .map(st -> {
                        JSONObject supplier = new JSONObject();
                        supplier.put("key", st.getKey());
                        supplier.put("value", st.getValue());
                        return supplier;
                    }).collect(Collectors.toList());
            log.info("获取语音供应商枚举");
            return ResponseVo.success(list);
        } catch (Exception e) {
            log.error("语音供应商枚举查询,[Controller层]出错! 原因:" + e.getMessage(), e);
            return ResponseVo.response(ResultCode.SYSTEM_EXCEPTION);
        }
    }

    /**
     * 查询语音所属部门列表
     *
     * @return
     */
    @GetMapping("/dept")
    public ResponseVo dept() {
        try {
            log.info("获取语音所属部门列表");
            List<String> list = service.listDept();
            log.info("获取语音所属部门列表,成功!" + " size:" + list.size());
            return ResponseVo.success(list);
        } catch (Exception e) {
            log.error("语音所属部门列表查询,[Controller层]出错! 原因:" + e.getMessage(), e);
            return ResponseVo.response(ResultCode.SYSTEM_EXCEPTION);
        }
    }

    /**
     * 查询语音所属机构列表
     *
     * @return
     */
    @GetMapping("/org")
    public ResponseVo org() {
        try {
            log.info("获取语音所属机构列表");
            List<String> list = service.listOrg();
            log.info("获取语音所属机构列表,成功!" + " size:" + list.size());
            return ResponseVo.success(list);
        } catch (Exception e) {
            log.error("语音所属机构列表查询,[Controller层]出错! 原因:" + e.getMessage(), e);
            return ResponseVo.response(ResultCode.SYSTEM_EXCEPTION);
        }
    }

    /**
     * 获取语音连接
     *
     * @return
     */
    @GetMapping("/url")
    public ResponseVo getUrl(@RequestHeader("x-user-id") String userid, @RequestHeader("x-user-name") String username, Integer id) {
        try {
            log.info("获取语音连接, username:" + username + ", userid:" + userid + ", id:" + id);
            ResponseVo result = service.getUrl(id, userid);
            log.info("获取语音连接,成功!" + ", userid:" + userid + " id:" + id);
            return result;
        } catch (Exception e) {
            log.error("获取语音连接,[Controller层]出错!"  + ", userid:" + userid + "， 原因:" + e.getMessage(), e);
            return ResponseVo.response(ResultCode.SYSTEM_EXCEPTION);
        }
    }


    @PutMapping("/audioListened/{id}")
    public ResponseVo modifyAudioListened(@RequestHeader("x-user-id") String userid, @RequestHeader("x-user-name") String username,
                                        @PathVariable(value = "id") Integer id) {
        try {
            log.info("更新语音听取状态, username:" + username + ", userid:" + userid + ", id:" + id);
            ResponseVo result = service.modifyAudioStatus(id,  userid);
            log.info("更新语音听取状态,成功!" + ", userid:" + userid + " id:" + id);
            return result;
        } catch (Exception e) {
            log.error("更新语音听取状态,[Controller层]出错!" + ", userid:" + userid + "， 原因:" + e.getMessage(), e);
            return ResponseVo.response(ResultCode.SYSTEM_EXCEPTION);
        }
    }

    /**
     * 修复异常数据
     *
     * @return ResponseVo
     */
    @PostMapping("/queryRecord")
    public ResponseVo queryRecord(@RequestHeader("x-user-id") String userid, @RequestHeader("x-user-name") String username, @RequestBody QueryVo queryVo) {
        try {
            log.info("修复异常数据,username:" + username + ", userid:" + userid + ",param:" + queryVo);
            ResponseVo result = service.queryRecord(queryVo);
            log.info("修复异常数据,成功!username:" + username + ", userid:" + userid + ",param:" + queryVo);
            return result;
        } catch (Exception e) {
            log.error("修复异常数据,[Controller层]出错!username:" + username + ", userid:" + userid + ",param:" + queryVo + " 原因:" + e.getMessage(), e);
            return ResponseVo.response(ResultCode.SYSTEM_EXCEPTION);
        }
    }

    /**
     * 获取完整手机号码
     *
     * @return
     */
    @GetMapping("/mobile")
    public ResponseVo getMobile(@RequestHeader("x-user-id") String userid, @RequestHeader("x-user-name") String username, Integer id) {
        try {
            log.info("获取完整手机号码, username:" + username + ", userid:" + userid + ", id:" + id);
            AudioRecord audioRecord = service.getById(id);
            log.info("获取完整手机号码,成功!" + ", userid:" + userid + " result:" + audioRecord.getPhone());
            return ResponseVo.success(audioRecord.getPhone());
        } catch (Exception e) {
            log.error("获取完整手机号码,[Controller层]出错!"  + ", userid:" + userid + "， 原因:" + e.getMessage(), e);
            return ResponseVo.response(ResultCode.SYSTEM_EXCEPTION);
        }
    }

    /**
     * 脱敏导出
     *
     * @return ResponseVo
     */
    @PostMapping("/export")
    public void export(@RequestHeader("x-user-id") String userid, @RequestHeader("x-user-name") String username, @RequestBody AudioRecordDto dto) {
        try {
            log.info("脱敏导出,username:" + username + ", userid:" + userid);
            service.export(dto);
            log.info("脱敏导出,成功!username:" + username + ", userid:" + userid);
        } catch (Exception e) {
            log.error("脱敏导出,出错!username:" + username + ", userid:" + userid + ",param:" + dto + " 原因:" + e.getMessage(), e);
        }
    }

    /**
     * 修改评语建议
     *
     * @return ResponseVo
     */
    @PostMapping("/comment")
    public ResponseVo comment(@RequestHeader("x-user-id") String userid, @RequestHeader("x-user-name") String username, @RequestBody AudioRecordDto dto) {
        try {
            log.info("修改评语建议,username:" + username + ", userid:" + userid);
            ResultCode result = service.update(dto);
            log.info("修改评语建议,成功!username:" + username + ", userid:" + userid + ", result: " + result);
            return ResponseVo.response(result);
        } catch (Exception e) {
            log.error("修改评语建议,出错!username:" + username + ", userid:" + userid + ",param:" + dto + " 原因:" + e.getMessage(), e);
            return ResponseVo.error();
        }
    }
}

