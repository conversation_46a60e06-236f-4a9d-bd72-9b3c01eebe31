package com.welab.databridge.controller;

import com.github.pagehelper.PageInfo;
import com.welab.databridge.common.ResponseVo;
import com.welab.databridge.common.ResultCode;
import com.welab.databridge.dto.MessageRecordDto;
import com.welab.databridge.entity.MessageRecord;
import com.welab.databridge.service.MessageRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 短信记录(MessageRecord)表控制层
 * (代码生成器自动生成)
 *
 * <AUTHOR>
 * @date 2023-01-03 14:29:51
 */
@RestController
@RequestMapping("/messageRecord")
@Slf4j
public class MessageRecordController {
    @Resource
    private MessageRecordService service;

    /**
     * 短信记录列表查询
     *
     * @param username
     * @param dto
     * @return
     */
    @PostMapping("/search")
    public ResponseVo search(@RequestHeader("x-user-id") String userid, @RequestHeader("x-user-name") String username, @RequestBody MessageRecordDto dto) {
        try {
            log.info("短信记录列表查询,username:" + username + ", userid:" + userid);
            dto.processSearch();
            PageInfo<MessageRecord> pageInfo = service.queryByCondition(dto);
            log.info("短信记录列表查询,成功!username:" + username + ", userid:" + userid + " size:" + pageInfo.getList().size());
            return ResponseVo.success(pageInfo);
        } catch (Exception e) {
            log.error("短信记录列表查询,[Controller层]出错!username:" + username + ", userid:" + userid + ",param:" + dto + " 原因:" + e.getMessage(), e);
            return ResponseVo.response(ResultCode.SYSTEM_EXCEPTION);
        }
    }

    /**
     * 导出短信记录
     *
     * @return ResponseVo
     */
    @PostMapping("/export")
    public void export(@RequestHeader("x-user-id") String userid, @RequestHeader("x-user-name") String username, @RequestBody MessageRecordDto dto) {
        try {
            log.info("导出短信记录,username:" + username + ", userid:" + userid);
            dto.processSearch();
            service.export(dto);
            log.info("导出短信记录,成功!username:" + username + ", userid:" + userid);
        } catch (Exception e) {
            log.error("导出短信记录,出错!username:" + username + ", userid:" + userid + ",param:" + dto + " 原因:" + e.getMessage(), e);
        }
    }
}

