package com.welab.databridge.controller;

import com.welab.databridge.common.ResponseVo;
import com.welab.databridge.common.ResultCode;
import com.welab.databridge.enums.ApproveCode;
import com.welab.databridge.service.AudioApplyService;
import com.welab.databridge.service.AudioRecordService;
import com.welab.databridge.service.AudioUploadService;
import com.welab.databridge.service.MessageUploadService;
import com.welab.databridge.vo.audit.CallbackParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/audit")
@Slf4j
public class AuditController {
    @Resource
    private MessageUploadService messageUploadService;
    @Resource
    private AudioUploadService audioUploadService;
    @Resource
    private AudioApplyService audioApplyService;

    /**
     * 审核模块审批回调
     *
     * @return ResponseVo
     */
    @PostMapping("/callback")
    public ResponseVo callback(@RequestHeader("x-user-id") String userid, @RequestHeader("x-user-name") String username, @RequestBody CallbackParam callbackParam) {
        try {
            if (!callbackParam.checkCallbackParam()) {
                return ResponseVo.response(ResultCode.AUDIT_FAILED);
            }
            ResponseVo result;
            log.info("审批回调,username:" + username + ", userid:" + userid + ",param:" + callbackParam);
            String auditConfigCode = callbackParam.getAuditConfigCode();
            if (ApproveCode.MESSAGE_UPLOAD_DELETE.getApproveCode().equals(auditConfigCode)) {
                result = messageUploadService.callback(callbackParam);
            } else if (ApproveCode.AUDIO_UPLOAD_DELETE.getApproveCode().equals(auditConfigCode)) {
                result = audioUploadService.callback(callbackParam);
            }  else if (ApproveCode.AUDIO_RECORD_DOWNLOAD.getApproveCode().equals(auditConfigCode)) {
                result = audioApplyService.callback(callbackParam);
            } else {
                result = ResponseVo.response(ResultCode.AUDIT_INVALID_CODE);
            }
            log.info("审批回调,username:" + username + ", userid:" + userid + ",结果:" + result + ",param:" + callbackParam);
            return result;
        } catch (Exception e) {
            log.error("审批回调,[Controller层]出错!username:" + username + ", userid:" + userid + ",param:" + callbackParam + " 原因:" + e.getMessage(), e);
            return ResponseVo.response(ResultCode.SYSTEM_EXCEPTION);
        }
    }
}
