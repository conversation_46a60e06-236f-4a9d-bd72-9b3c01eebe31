package com.welab.databridge.controller;

import com.github.pagehelper.PageInfo;
import com.welab.databridge.common.ResponseVo;
import com.welab.databridge.common.ResultCode;
import com.welab.databridge.dto.MessageUploadDto;
import com.welab.databridge.entity.MessageUpload;
import com.welab.databridge.service.MessageUploadService;
import com.welab.databridge.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.tomcat.util.http.fileupload.servlet.ServletFileUpload;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 短信上传记录(MessageUpload)表控制层
 * (代码生成器自动生成)
 *
 * <AUTHOR>
 * @date 2023-01-03 14:29:49
 */
@RestController
@RequestMapping("/messageUpload")
@Slf4j
public class MessageUploadController {
    @Resource
    private MessageUploadService service;

    /**
     * 短信上传记录列表查询
     *
     * @param username
     * @param dto
     * @return
     */
    @GetMapping("/search")
    public ResponseVo search(@RequestHeader("x-user-id") String userid, @RequestHeader("x-user-name") String username, @ModelAttribute MessageUploadDto dto) {
        try {
            log.info("短信上传记录列表查询,username:" + username + ", userid:" + userid + ",param:" + dto);
            PageInfo<MessageUpload> pageInfo = service.queryByCondition(dto);
            log.info("短信上传记录列表查询,成功!username:" + username + ", userid:" + userid + " size:" + pageInfo.getList().size() + " param:" + dto);
            return ResponseVo.success(pageInfo);
        } catch (Exception e) {
            log.error("短信上传记录列表查询,[Controller层]出错!username:" + username + ", userid:" + userid + ",param:" + dto + " 原因:" + e.getMessage(), e);
            return ResponseVo.response(ResultCode.SYSTEM_EXCEPTION);
        }
    }

    /**
     * 下载模板
     *
     */
    @PostMapping(value = "/downloadTemplate")
    public void downloadTemplate(HttpServletRequest request, HttpServletResponse response) {
        service.downloadTemplate(request, response);
    }

    /**
     * 上传短信记录
     *
     * @return ResponseVo
     */
    @PostMapping("/upload")
    public ResponseVo upload(@RequestHeader("x-user-id") String userid, @RequestHeader("x-user-name") String username, HttpServletRequest request) {
        try {
            log.info("上传短信记录,username:" + username + ", userid:" + userid);
            if (!ServletFileUpload.isMultipartContent(request)) {
                return ResponseVo.error(ResultCode.FILE_IS_EMPTY);
            }
            String deptId = request.getParameter("deptId");
            if (StringUtil.isEmpty(deptId) || StringUtil.isEmpty(deptId.trim())) {
                return ResponseVo.error(ResultCode.DEPARTMENT_EMPTY);
            }
            MultipartFile file = ((MultipartHttpServletRequest)request).getFile("file");
            if (file == null || file.isEmpty()) {
                return ResponseVo.error(ResultCode.FILE_IS_EMPTY);
            }
            String filename = file.getOriginalFilename();
            if (StringUtils.isEmpty(filename)
                    || (!filename.endsWith(".xls")
                        && !filename.endsWith(".xlsx"))) {
                return ResponseVo.error(ResultCode.FILE_TYPE_LIMIT);
            }
            service.upload(userid, Integer.parseInt(deptId), file.getInputStream());
            log.info("上传短信记录成功, username:" + username + ", userid:" + userid);
            return ResponseVo.success();
        } catch (Exception e) {
            log.error("上传短信记录,[Controller层]出错!username:" + username + ", userid:" + userid + " 原因:" + e.getMessage(), e);
            return ResponseVo.response(ResultCode.SYSTEM_EXCEPTION);
        }
    }

    /**
     * 删除短信记录
     *
     * @return ResponseVo
     */
    @PostMapping("/delete")
    public ResponseVo delete(@RequestHeader("x-user-id") String userid, @RequestHeader("x-user-name") String username, @RequestBody MessageUploadDto dto) {
        try {
            log.info("删除短信上传记录,username:" + username + ", userid:" + userid + ",param:" + dto);
            ResponseVo result = service.deleteById(dto.getId());
            log.info("删除短信上传记录,结果:" + result +", username:" + username + ", userid:" + userid + ",param:" + dto);
            return result;
        } catch (Exception e) {
            log.error("删除短信上传记录,出错!username:" + username + ", userid:" + userid + ",param:" + dto + " 原因:" + e.getMessage(), e);
            return ResponseVo.response(ResultCode.SYSTEM_EXCEPTION);
        }
    }

}

