package com.welab.databridge.controller;

import com.welab.databridge.common.ResponseVo;
import com.welab.databridge.common.ResultCode;
import com.welab.databridge.dto.DepartmentDto;
import com.welab.databridge.entity.Department;
import com.welab.databridge.entity.UserDept;
import com.welab.databridge.service.DepartmentService;
import com.welab.databridge.vo.dept.DeptVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 部门表(Department)表控制层
 * (代码生成器自动生成)
 *
 * <AUTHOR>
 * @date 2023-01-03 14:29:46
 */
@RestController
@RequestMapping("/department")
@Slf4j
public class DepartmentController {
    @Resource
    private DepartmentService service;

    /**
     * 部门列表查询
     *
     * @param username
     * @param dto
     * @return
     */
    @GetMapping("/search")
    public ResponseVo search(@RequestHeader("x-user-id") String userid, @RequestHeader("x-user-name") String username, @ModelAttribute DepartmentDto dto) {
        try {
            log.info("部门列表查询,username:" + username + ", userid:" + userid + ",param:" + dto);
            List<Department> list = service.queryByCondition(dto);
            log.info("部门列表查询,成功!username:" + username + ", userid:" + userid + " size:" + list.size() + " param:" + dto);
            return ResponseVo.success(list);
        } catch (Exception e) {
            log.error("部门列表查询,[Controller层]出错!username:" + username + ", userid:" + userid + ",param:" + dto + " 原因:" + e.getMessage(), e);
            return ResponseVo.response(ResultCode.SYSTEM_EXCEPTION);
        }
    }

    /**
     * 新增部门
     *
     * @return ResponseVo
     */
    @PostMapping("/add")
    public ResponseVo add(@RequestHeader("x-user-id") String userid, @RequestHeader("x-user-name") String username, @RequestBody DepartmentDto dto) {
        try {
            log.info("新增部门,username:" + username + ", userid:" + userid + ",param:" + dto);
            ResponseVo result = service.insert(dto);
            log.info("新增部门结果:" + result + ", username:" + username + ", userid:" + userid + ",param:" + dto);
            return result;
        } catch (Exception e) {
            log.error("新增部门,[Controller层]出错!username:" + username + ", userid:" + userid + ",param:" + dto + " 原因:" + e.getMessage(), e);
            return ResponseVo.response(ResultCode.SYSTEM_EXCEPTION);
        }
    }

    /**
     * 修改部门
     *
     * @return ResponseVo
     */
    @PostMapping("/update")
    public ResponseVo update(@RequestHeader("x-user-id") String userid, @RequestHeader("x-user-name") String username, @RequestBody DepartmentDto dto) {
        try {
            log.info("修改部门,username:" + username + ", userid:" + userid + ",param:" + dto);
            ResponseVo result = service.update(dto);
            log.info("修改部门结果:" + result + ", username:" + username + ", userid:" + userid + ",param:" + dto);
            return result;
        } catch (Exception e) {
            log.error("修改部门,[Controller层]出错!username:" + username + ", userid:" + userid + ",param:" + dto + " 原因:" + e.getMessage(), e);
            return ResponseVo.response(ResultCode.SYSTEM_EXCEPTION);
        }
    }

    /**
     * 删除部门
     *
     * @return ResponseVo
     */
    @PostMapping("/delete")
    public ResponseVo delete(@RequestHeader("x-user-id") String userid, @RequestHeader("x-user-name") String username, @RequestBody DepartmentDto dto) {
        try {
            log.info("删除部门,username:" + username + ", userid:" + userid + ",param:" + dto);
            ResponseVo result = service.deleteById(dto.getId());
            log.info("删除部门结果:" + result + ", username:" + username + ",param:" + dto);
            return result;
        } catch (Exception e) {
            log.error("删除部门,出错!username:" + username + ", userid:" + userid + ",param:" + dto + " 原因:" + e.getMessage(), e);
            return ResponseVo.response(ResultCode.SYSTEM_EXCEPTION);
        }
    }

    /**
     * 获取用户部门列表
     *
     * @param username
     * @return
     */
    @GetMapping("/userDept")
    public ResponseVo userDept(@RequestHeader("x-user-id") String userid, @RequestHeader("x-user-name") String username) {
        try {
            log.info("获取用户部门列表,username:" + username + ", userid:" + userid);
            List<Department> list = service.listUserDepartment();
            log.info("获取用户部门列表,成功!username:" + username + ", userid:" + userid + " size:" + list.size());
            return ResponseVo.success(list);
        } catch (Exception e) {
            log.error("用户部门列表查询,[Controller层]出错!username:" + username + ", userid:" + userid + " 原因:" + e.getMessage(), e);
            return ResponseVo.response(ResultCode.SYSTEM_EXCEPTION);
        }
    }

    /**
     * 获取部门用户列表
     *
     * @param username 用户名
     * @param id 部门ID
     * @return
     */
    @GetMapping("/deptUser")
    public ResponseVo deptUser(@RequestHeader("x-user-id") String userid, @RequestHeader("x-user-name") String username, Integer id) {
        try {
            log.info("获取部门用户列表,username:" + username + ", userid:" + userid);
            if (id == null) {
                return ResponseVo.error(ResultCode.PARAM_ERROR);
            }
            List<UserDept> list = service.listDepartmentUser(id);
            log.info("获取部门用户列表,成功!username:" + username + ", userid:" + userid + " size:" + list.size());
            return ResponseVo.success(list);
        } catch (Exception e) {
            log.error("部门用户列表查询,[Controller层]出错!username:" + username + ", userid:" + userid + " 原因:" + e.getMessage(), e);
            return ResponseVo.response(ResultCode.SYSTEM_EXCEPTION);
        }
    }

    /**
     * 设置部门
     *
     * @param username
     * @param deptVo
     * @return
     */
    @PostMapping("/setDept")
    public ResponseVo setDept(@RequestHeader("x-user-id") String userid, @RequestHeader("x-user-name") String username, @RequestBody DeptVo deptVo) {
        try {
            log.info("设置部门,username:" + username + ", userid:" + userid + ",param:" + deptVo);
            ResponseVo result = service.setDept(deptVo);
            log.info("设置部门结果:" + result + ", username:" + username + ", userid:" + userid + ",param:" + deptVo);
            return result;
        } catch (Exception e) {
            log.error("设置部门,出错!username:" + username + ", userid:" + userid + ",param:" + deptVo + " 原因:" + e.getMessage(), e);
            return ResponseVo.response(ResultCode.SYSTEM_EXCEPTION);
        }
    }

    /**
     * 用户列表
     *
     * @return
     */
    @GetMapping("/user")
    public ResponseVo user() {
        try {
            log.info("获取用户列表");
            List<String> list = service.listUser();
            log.info("获取用户列表,成功!" + " size:" + list.size());
            return ResponseVo.success(list);
        } catch (Exception e) {
            log.error("用户列表查询,[Controller层]出错! 原因:" + e.getMessage(), e);
            return ResponseVo.response(ResultCode.SYSTEM_EXCEPTION);
        }
    }
}

