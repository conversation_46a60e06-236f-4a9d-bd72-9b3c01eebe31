package com.welab.databridge.controller;

import com.welab.databridge.common.ResponseVo;
import com.welab.databridge.common.ResultCode;
import com.welab.databridge.dto.MessageFailDto;
import com.welab.databridge.entity.MessageFail;
import com.welab.databridge.service.MessageFailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 短信上传失败记录(MessageFail)表控制层
 * (代码生成器自动生成)
 *
 * <AUTHOR>
 * @date 2023-01-03 14:29:44
 */
@RestController
@RequestMapping("/messageFail")
@Slf4j
public class MessageFailController {
    @Resource
    private MessageFailService service;

    /**
     * 短信上传失败记录列表查询
     *
     * @param username
     * @param dto
     * @return
     */
    @GetMapping("/search")
    public ResponseVo search(@RequestHeader("x-user-id") String userid, @RequestHeader("x-user-name") String username, @ModelAttribute MessageFailDto dto) {
        try {
            log.info("短信上传失败记录列表查询,username:" + username + ", userid:" + userid + ",param:" + dto);
            List<MessageFail> list = service.queryByCondition(dto);
            log.info("短信上传失败记录列表查询,成功!username:" + username + ", userid:" + userid + " size:" + list.size() + " param:" + dto);
            return ResponseVo.success(list);
        } catch (Exception e) {
            log.error("短信上传失败记录列表查询,[Controller层]出错!userId:" + username + ", userid:" + userid + ",param:" + dto + " 原因:" + e.getMessage(), e);
            return ResponseVo.response(ResultCode.SYSTEM_EXCEPTION);
        }
    }

    /**
     * 导出短信记录
     *
     * @return ResponseVo
     */
    @PostMapping("/export")
    public void export(@RequestHeader("x-user-id") String userid, @RequestHeader("x-user-name") String username, @RequestBody MessageFailDto dto) {
        try {
            log.info("导出上传失败短信记录,username:" + username + ", userid:" + userid + ",param:" + dto);
            service.export(dto);
            log.info("导出上传失败短信记录,成功!username:" + username + ", userid:" + userid + ",param:" + dto);
        } catch (Exception e) {
            log.error("导出上传失败短信记录,出错!username:" + username + ", userid:" + userid + ",param:" + dto + " 原因:" + e.getMessage(), e);
        }
    }
}

