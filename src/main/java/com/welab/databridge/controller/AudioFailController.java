package com.welab.databridge.controller;

import com.welab.databridge.common.ResultCode;
import com.welab.databridge.dto.AudioFailDto;
import com.welab.databridge.entity.AudioFail;
import com.welab.databridge.service.AudioFailService;
import com.welab.databridge.common.ResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 音频上传失败记录(AudioFail)表控制层
 * (代码生成器自动生成)
 *
 * <AUTHOR>
 * @date 2023-01-03 14:29:59
 */
@RestController
@RequestMapping("/audioFail")
@Slf4j
public class AudioFailController {
    @Resource
    private AudioFailService service;

    /**
     * 音频上传失败记录列表查询
     *
     * @param username
     * @param dto
     * @return
     */
    @GetMapping("/search")
    public ResponseVo search(@RequestHeader("x-user-id") String userid, @RequestHeader("x-user-name") String username, @ModelAttribute AudioFailDto dto) {
        try {
            log.info("音频上传失败记录列表查询,username:" + username + ", userid:" + userid + ",param:" + dto);
            List<AudioFail> list = service.queryByCondition(dto);
            log.info("音频上传失败记录列表查询,成功!username:" + username + ", userid:" + userid + " size:" + list.size() + " param:" + dto);
            return ResponseVo.success(list);
        } catch (Exception e) {
            log.error("音频上传失败记录列表查询,[Controller层]出错!username:" + username + ", userid:" + userid + ",param:" + dto + " 原因:" + e.getMessage(), e);
            return ResponseVo.response(ResultCode.SYSTEM_EXCEPTION);
        }
    }
}

