package com.welab.databridge.controller;

import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.welab.databridge.common.ResultCode;
import com.welab.databridge.dto.AudioUploadDto;
import com.welab.databridge.entity.AudioUpload;
import com.welab.databridge.service.AudioUploadService;
import com.welab.databridge.common.ResponseVo;
import com.welab.databridge.util.StringUtil;
import com.welab.databridge.vo.file.FileVo;
import org.apache.tomcat.util.http.fileupload.servlet.ServletFileUpload;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 音频上传记录(AudioUpload)表控制层
 * (代码生成器自动生成)
 *
 * <AUTHOR>
 * @date 2023-01-03 14:29:41
 */
@RestController
@RequestMapping("/audioUpload")
@Slf4j
public class AudioUploadController {
    @Resource
    private AudioUploadService service;

    /**
     * 音频上传记录列表查询
     *
     * @param username
     * @param dto
     * @return
     */
    @GetMapping("/search")
    public ResponseVo search(@RequestHeader("x-user-id") String userid, @RequestHeader("x-user-name") String username, @ModelAttribute AudioUploadDto dto) {
        try {
            log.info("音频上传记录列表查询,username:" + username + ", userid:" + userid + ",param:" + dto);
            PageInfo<AudioUpload> pageInfo = service.queryByCondition(dto);
            log.info("音频上传记录列表查询,成功!userId:" + username + ", userid:" + userid + " size:" + pageInfo.getList().size() + " param:" + dto);
            return ResponseVo.success(pageInfo);
        } catch (Exception e) {
            log.error("音频上传记录列表查询,[Controller层]出错!username:" + username + ", userid:" + userid + ",param:" + dto + " 原因:" + e.getMessage(), e);
            return ResponseVo.response(ResultCode.SYSTEM_EXCEPTION);
        }
    }

    /**
     * 上传音频
     *
     * @return ResponseVo
     */
    @PostMapping("/upload")
    public ResponseVo upload(@RequestHeader("x-user-id") String userid, @RequestHeader("x-user-name") String username, HttpServletRequest request) {
        try {
            log.info("上传音频,username:" + username + ", userid:" + userid);
            if (!ServletFileUpload.isMultipartContent(request)) {
                return ResponseVo.error(ResultCode.FILE_IS_EMPTY);
            }
            String deptId = request.getParameter("deptId");
            if (StringUtil.isEmpty(deptId) || StringUtil.isEmpty(deptId.trim())) {
                return ResponseVo.error(ResultCode.DEPARTMENT_EMPTY);
            }

            // 获取可选的坐席/代理人参数
            String agentId = request.getParameter("agentId");
            if (agentId != null) {
                agentId = agentId.trim();
                if (agentId.isEmpty()) {
                    agentId = null;
                }
            }
            List<MultipartFile> files = ((MultipartHttpServletRequest)request).getFiles("file");
            if (ObjectUtil.isEmpty(files)) {
                return ResponseVo.error(ResultCode.FILE_IS_EMPTY);
            }

            List<FileVo> list = new ArrayList<>();
            FileVo fileVo;
            for (MultipartFile file : files) {
                fileVo = new FileVo();
                fileVo.setFileName(file.getOriginalFilename());
                fileVo.setInputStream(file.getInputStream());
                list.add(fileVo);
            }

            service.upload(userid, Integer.parseInt(deptId), agentId, list);
            log.info("上传音频，username:" + username + ", userid:" + userid + ", agentId:" + agentId);
            return ResponseVo.success();
        } catch (Exception e) {
            log.error("新增音频上传记录,[Controller层]出错!username:" + username + ", userid:" + userid + " 原因:" + e.getMessage(), e);
            return ResponseVo.response(ResultCode.SYSTEM_EXCEPTION);
        }
    }

    /**
     * 删除音频上传记录
     *
     * @return ResponseVo
     */
    @PostMapping("/delete")
    public ResponseVo delete(@RequestHeader("x-user-id") String userid, @RequestHeader("x-user-name") String username, @RequestBody AudioUploadDto dto) {
        try {
            log.info("删除音频上传记录,username:" + username + ", userid:" + userid + ",param:" + dto);
            ResponseVo result = service.deleteById(dto.getId());
            log.info("删除音频上传记录,结果:" + result + "!username:" + username + ", userid:" + userid + ",param:" + dto);
            return result;
        } catch (Exception e) {
            log.error("删除音频上传记录,出错!username:" + username + ", userid:" + userid + ",param:" + dto + " 原因:" + e.getMessage(), e);
            return ResponseVo.response(ResultCode.SYSTEM_EXCEPTION);
        }
    }

}

