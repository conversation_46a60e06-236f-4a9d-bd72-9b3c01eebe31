package com.welab.databridge.controller;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.welab.databridge.common.ResponseVo;
import com.welab.databridge.common.ResultCode;
import com.welab.databridge.dto.AudioTempDto;
import com.welab.databridge.entity.AudioTemp;
import com.welab.databridge.service.AudioTempService;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * (AudioTemp)表控制层
 * (代码生成器自动生成)
 *
 * <AUTHOR>
 * @date 2023-03-17 14:46:37
 */
@RestController
@RequestMapping("/audioTemp")
@Slf4j
public class AudioTempController {
    @Resource
    private AudioTempService service;

    /**
     * xxx列表查询
     *
     * @param userId
     * @param dto
     * @return
     */
    @PostMapping("/search")
    public ResponseVo search(@RequestHeader("x-user-id") Integer userId, @RequestBody AudioTempDto dto) {
        try {
            log.info("xxx列表查询,userId:" + userId + ",param:" + dto);
            PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
            List<AudioTemp> list = service.queryByCondition(dto);
            PageInfo<AudioTemp> pageInfo = new PageInfo<>(list);
            log.info("xxx列表查询,成功!userId:" + userId + " size:" + list.size() + " param:" + dto);
            return ResponseVo.success(pageInfo);
        } catch (Exception e) {
            log.error("xxx列表查询,[Controller层]出错!userId:" + userId + ",param:" + dto + " 原因:" + e.getMessage(), e);
            return ResponseVo.response(ResultCode.SYSTEM_EXCEPTION);
        }
    }

    /**
     * 修改xxx
     *
     * @return ResponseVo
     */
    @PostMapping("/update")
    public ResponseVo update(@RequestHeader("x-user-id") Integer userId, @RequestBody AudioTempDto dto) {
        try {
            log.info("修改xxx,userId:" + userId + ",param:" + dto);
            ResultCode result = service.update(dto);
            if (ResultCode.SUCCESS != result) {
                log.warn("修改xxx,失败!userId:" + userId + ",param:" + dto);
                return ResponseVo.response(result);
            }
            log.info("修改xxx,成功!userId:" + userId + ",param:" + dto);
            return ResponseVo.success();
        } catch (Exception e) {
            log.error("修改xxx,[Controller层]出错!userId:" + userId + ",param:" + dto + " 原因:" + e.getMessage(), e);
            return ResponseVo.response(ResultCode.SYSTEM_EXCEPTION);
        }
    }

    /**
     * 删除xxx
     *
     * @return ResponseVo
     */
    @PostMapping("/delete")
    public ResponseVo delete(@RequestHeader("x-user-id") Integer userId, @RequestBody AudioTempDto dto) {
        try {
            log.info("删除xxx,userId:" + userId + ",param:" + dto);
            ResultCode result = service.deleteById(dto.getId());
            if (ResultCode.SUCCESS != result) {
                log.warn("删除xxx,失败!userId:" + userId + ",param:" + dto);
                return ResponseVo.response(result);
            }
            log.info("删除xxx,成功!userId:" + userId + ",param:" + dto);
            return ResponseVo.success();
        } catch (Exception e) {
            log.error("删除xxx,出错!userId:" + userId + ",param:" + dto + " 原因:" + e.getMessage(), e);
            return ResponseVo.response(ResultCode.SYSTEM_EXCEPTION);
        }
    }

    /**
     * 修改状态
     *
     * @return ResponseVo
     */
    @PostMapping("/updateStatus")
    public ResponseVo updateStatus(@RequestHeader("x-user-id") Integer userId, @RequestBody AudioTempDto dto) {
        try {
            log.info("修改状态,userId:" + userId + ",param:" + dto);
            service.updateStatus(dto.getQueryDate(), dto.getStatus());
            log.info("修改状态成功,userId:" + userId + ",param:" + dto);
            return ResponseVo.success();
        } catch (Exception e) {
            log.error("修改状态,[Controller层]出错!userId:" + userId + ",param:" + dto + " 原因:" + e.getMessage(), e);
            return ResponseVo.response(ResultCode.SYSTEM_EXCEPTION);
        }
    }

}

