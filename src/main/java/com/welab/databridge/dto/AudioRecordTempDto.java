package com.welab.databridge.dto;

import java.util.Date;

import com.welab.databridge.dto.common.BasePageDto;
import com.welab.databridge.entity.AudioRecordTemp;

import lombok.Data;

/**
 * 音频记录临时(AudioRecordTemp)传输对象
 *
 * <AUTHOR>
 * @since 2024-03-14 10:50:17
 */
@Data
public class AudioRecordTempDto extends BasePageDto {
    /**
     * 主键
     */
    private Integer id;
    /**
     * 部门ID
     */
    private Integer deptId;
    /**
     * userid
     */
    private String userid;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 身份证号
     */
    private String cnid;

    private String url;
    /**
     * 接听时间
     */
    private Date startTime;
    /**
     * 手动上传时间，接口传输时为null
     */
    private Date uploadTime;
    /**
     * 上传方式：0-接口，1-手动
     */
    private Integer uploadMethod;
    /**
     * 服务商
     */
    private String supplier;
    /**
     * 是否入库：0-否，1-是
     */
    private Integer flag;

    private String dir;

    private String operator;

    /**
     * 获取新增实体bean
     */
    public AudioRecordTemp toAddBean() {
        AudioRecordTemp bean = new AudioRecordTemp();
        return bean;
    }

    /**
     * 获取修改实体bean
     */
    public AudioRecordTemp toUpdateBean() {
        AudioRecordTemp bean = new AudioRecordTemp();
        bean.setFlag(flag);
        return bean;
    }

}

