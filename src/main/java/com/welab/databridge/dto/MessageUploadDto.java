package com.welab.databridge.dto;

import com.welab.databridge.dto.common.BasePageDto;
import com.welab.databridge.entity.MessageUpload;
import lombok.Data;

import java.util.List;

/**
 * 短信上传记录(MessageUpload)传输对象
 *
 * <AUTHOR>
 * @since 2023-01-03 14:29:48
 */
@Data
public class MessageUploadDto extends BasePageDto {
    /**
     * 主键
     */
    private Integer id;
    /**
     * 部门ID
     */
    private Integer deptId;
    /**
     * 批次号
     */
    private String batchNo;
    /**
     * 提交数量
     */
    private Integer uploadNum;
    /**
     * 上传成功数量
     */
    private Integer successNum;
    /**
     * 上传失败数量
     */
    private Integer failNum;
    /**
     * 操作类型：add-增加，delete-删除
     */
    private String operation;
    /**
     * 状态：processing-处理中，finish-处理完成,fail-处理失败
     */
    private String status;
    /**
     * 失败原因
     */
    private String failMsg;
    /**
     * 审核状态：default-默认，in_review-审核中，audit_reject-审核拒绝，approved-审核通过
     */
    private String approveStatus;
    /**
     * 审核类型：online-上线，offline-下线，suspend-暂停，delete-删除
     */
    private String approveType;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 上传开始时间
     */
    private String startTime;
    /**
     * 上传结束时间
     */
    private String endTime;

    private List<Integer> deptIds;

    /**
     * 获取新增实体bean
     */
    public MessageUpload toAddBean() {
        MessageUpload bean = new MessageUpload();
        //TODO 设置需要的参数, 光标在bean上,然后command+Enter自动生成
        return bean;
    }

    /**
     * 获取修改实体bean
     */
    public MessageUpload toUpdateBean() {
        MessageUpload bean = new MessageUpload();
        //TODO 设置需要修改的参数, 光标在bean上,然后command+Enter自动生成
        return bean;
    }
}

