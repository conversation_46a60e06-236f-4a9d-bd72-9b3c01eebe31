package com.welab.databridge.dto;

import com.welab.databridge.dto.common.BasePageDto;
import com.welab.databridge.entity.Department;
import com.welab.databridge.util.DateUtil;
import lombok.Data;

/**
 * 部门表(Department)传输对象
 *
 * <AUTHOR>
 * @since 2023-01-03 14:29:46
 */
@Data
public class DepartmentDto extends BasePageDto {
    /**
     * 主键
     */
    private Integer id;
    /**
     * 父部门ID
     */
    private Integer parentId;
    /**
     * 部门名称
     */
    private String name;

    /**
     * 获取新增实体bean
     */
    public Department toAddBean() {
        Department bean = toUpdateBean();
        bean.setCreateTime(DateUtil.getCurrentDateTime());
        return bean;
    }

    /**
     * 获取修改实体bean
     */
    public Department toUpdateBean() {
        Department bean = new Department();
        bean.setParentId(parentId == null ? 0 : parentId);
        bean.setName(name);
        bean.setUpdateTime(DateUtil.getCurrentDateTime());
        return bean;
    }

}

