package com.welab.databridge.dto;

import java.util.Date;
import java.util.List;

import com.welab.databridge.dto.common.BasePageDto;
import com.welab.databridge.entity.AudioUpload;

import lombok.Data;

/**
 * 音频上传记录(AudioUpload)传输对象
 *
 * <AUTHOR>
 * @since 2023-01-03 14:29:40
 */
@Data
public class AudioUploadDto extends BasePageDto {
    /**
     * 主键
     */
    private Integer id;
    /**
     * 部门ID
     */
    private Integer deptId;
    /**
     * 批次号
     */
    private String batchNo;
    /**
     * 提交数量
     */
    private Integer uploadNum;
    /**
     * 上传成功数量
     */
    private Integer successNum;
    /**
     * 上传失败数量
     */
    private Integer failNum;
    /**
     * 操作类型：add-增加，delete-删除
     */
    private String operation;
    /**
     * 状态：proccessing-处理中，finish-处理完成,fail-处理失败
     */
    private String status;
    /**
     * 审核状态：default-默认，in_review-审核中，audit_reject-审核拒绝，approved-审核通过
     */
    private String approveStatus;
    /**
     * 审核类型：online-上线，offline-下线，suspend-暂停，delete-删除
     */
    private String approveType;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 坐席/代理人ID
     */
    private String agentId;
    /**
     * 上传时间
     */
    private Date createTime;
    /**
     * 操作时间
     */
    private Date updateTime;

    /**
     * 操作时间-开始
     */
    private String startTime;
    /**
     * 操作时间-结束
     */
    private String endTime;

    private List<Integer> deptIds;

    /**
     * 获取新增实体bean
     */
    public AudioUpload toAddBean() {
        AudioUpload bean = new AudioUpload();
        //TODO 设置需要的参数, 光标在bean上,然后command+Enter自动生成
        return bean;
    }

    /**
     * 获取修改实体bean
     */
    public AudioUpload toUpdateBean() {
        AudioUpload bean = new AudioUpload();
        //TODO 设置需要修改的参数, 光标在bean上,然后command+Enter自动生成
        return bean;
    }

}

