package com.welab.databridge.dto;

import com.welab.databridge.dto.common.BasePageDto;
import com.welab.databridge.entity.AudioTemp;
import lombok.Data;

/**
 * (AudioTemp)传输对象
 *
 * <AUTHOR>
 * @since 2023-03-17 14:46:35
 */
@Data
public class AudioTempDto extends BasePageDto {
    /**
     * id
     */
    private Integer id;
    /**
     * 坐席工号
     */
    private String cno;
    /**
     * 客户手机号
     */
    private String mobile;
    /**
     * 呼叫中心编码对应的token
     */
    private String sign;
    /**
     * 录音文件地址
     */
    private String recordFile;
    /**
     * 版本
     */
    private String version;
    /**
     * 通话类型
     */
    private String callType;
    /**
     * 供应商名称，TR 或者 JS
     */
    private String provider;
    /**
     * 接听状态
     */
    private String callStatus;
    /**
     * 坐席名称
     */
    private String staffName;
    /**
     * 拨打时间
     */
    private String startTime;
    /**
     * 结束时间
     */
    private String endTime;
    /**
     * 呼叫中心编码
     */
    private String enterpriseId;
    /**
     * 地区编号
     */
    private String region;
    /**
     * 部门名称
     */
    private String enterpriseName;
    /**
     * 催收组
     */
    private String groupCode;
    /**
     * 状态：proccessing-处理中，finish-处理完成,fail-处理失败
     */
    private String status;
    private String queryDate;
    /**
     * 创建时间
     */
    private String createTime;

    private String sort;

    private Integer limit;

    /**
     * 获取新增实体bean
     */
    public AudioTemp toAddBean() {
        AudioTemp bean = new AudioTemp();
        return bean;
    }

    /**
     * 获取修改实体bean
     */
    public AudioTemp toUpdateBean() {
        AudioTemp bean = new AudioTemp();
        bean.setCallType(callType);
        return bean;
    }
}

