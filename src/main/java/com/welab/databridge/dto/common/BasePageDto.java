package com.welab.databridge.dto.common;

import com.welab.databridge.util.StringUtil;
import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 * @Description 分页请求
 * @Date 2022/3/15 上午10:06
 */
@Slf4j
public class BasePageDto {

    /**
     * 默认每页显示条数
     */
    private static final int DEFAULT_PAGE_SIZE = 20;
    /**
     * 默认页码
     */
    private static final int DEFAULT_PAGE_NUMBER = 1;

    /**
     * 当前页码
     */
    private Integer pageNum;

    /**
     * 每页显示数,最大不超过3000条
     */
    private Integer pageSize;

    /**
     * 分页偏移量 如: limit offset, pageSize
     * 当pagehelper不支持一对一或一对多结果映射查询时,可使用.
     */
    private int offset;

    public int getPageNum() {
        if (!StringUtil.isGreaterThanZero(pageNum)) {
            return DEFAULT_PAGE_NUMBER;
        }
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        if (!StringUtil.isGreaterThanZero(pageSize)) {
            return DEFAULT_PAGE_SIZE;
        }

        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    /**
     * 排序：asc-正序，desc-倒序
     */
    private String sort;

    public String getSort() {
        return sort;
    }

    public void setSort(String sort) {
        this.sort = sort;
    }

    public int getOffset() {
        offset = (getPageNum()-1) * getPageSize();
        return offset;
    }

    public void setOffset(int offset) {
        this.offset = offset;
    }
}
