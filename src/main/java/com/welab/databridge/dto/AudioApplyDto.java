package com.welab.databridge.dto;

import com.welab.databridge.dto.common.BasePageDto;
import com.welab.databridge.entity.AudioApply;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * (AudioApply)传输对象
 *
 * <AUTHOR>
 * @since 2023-03-01 18:07:58
 */
@Data
public class AudioApplyDto extends BasePageDto {
    /**
     * 主键
     */
    private Integer id;
    /**
     * 申请下载的语音记录ID
     */
    private List<Integer> audioIds;
    /**
     * 申请下载原因
     */
    private String reason;
    /**
     * 状态：default-初始状态；generate-正在生成下载链接；send-下载链接已邮件发送；fail-下载失败
     */
    private String status;
    /**
     * 审核状态：default-默认，in_review-审核中，audit_reject-审核拒绝，approved-审核通过
     */
    private String approveStatus;
    /**
     * 申请人
     */
    private String applier;
    /**
     * 申请时间
     */
    private String createTime;

    private String startTime;
    private String endTime;

    /**
     * 获取新增实体bean
     */
    public AudioApply toAddBean() {
        AudioApply bean = new AudioApply();
        //TODO 设置需要的参数, 光标在bean上,然后command+Enter自动生成
        return bean;
    }

    /**
     * 获取修改实体bean
     */
    public AudioApply toUpdateBean() {
        AudioApply bean = new AudioApply();
        //TODO 设置需要修改的参数, 光标在bean上,然后command+Enter自动生成
        return bean;
    }

}

