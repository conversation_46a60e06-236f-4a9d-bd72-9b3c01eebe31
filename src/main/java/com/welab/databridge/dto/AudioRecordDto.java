package com.welab.databridge.dto;

import com.welab.databridge.dto.common.BasePageDto;
import com.welab.databridge.entity.AudioRecord;
import com.welab.databridge.util.DateUtil;
import com.welab.databridge.util.StringUtil;
import lombok.Data;

import java.util.Arrays;
import java.util.List;

/**
 * 音频记录(AudioRecord)传输对象
 *
 * <AUTHOR>
 * @since 2023-01-03 14:29:53
 */
@Data
public class AudioRecordDto extends BasePageDto {
    /**
     * 主键
     */
    private Integer id;
    /**
     * 上传批次ID
     */
    private Integer uploadId;
    /**
     * 部门ID
     */
    private Integer deptId;
    /**
     * userid
     */
    private String userid;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 身份证号
     */
    private String cnid;
    /**
     * 接听时间-开始
     */
    private String startTime;
    /**
     * 接听时间-结束
     */
    private String endTime;
    /**
     * 通话类型，0-外呼，1-来电
     */
    private Integer callType;
    /**
     * 手动上传时间，接口传输时为null
     */
    private String uploadTime;
    /**
     * 接听状态，0-接听成功，1-接听失败
     */
    private Integer status;
    /**
     * 上传方式：0-接口，1-手动
     */
    private Integer uploadMethod;
    /**
     * 是否已回听：0-否，1-是
     */
    private Integer listened;
    /**
     * 回听开始时间
     */
    private String listenStartTime;
    /**
     * 回听结束时间
     */
    private String listenEndTime;
    /**
     * 回听人
     */
    private String listener;
    /**
     * 评语建议
     */
    private String comment;

    private Integer deleted;
    /**
     * 服务商
     */
    private String supplier;
    /**
     * 所属部门
     */
    private String dept;
    /**
     * 所属机构
     */
    private String org;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 录音时长最小值
     */
    private Integer durationMin;
    
    /**
     * 录音时长最大值
     */
    private Integer durationMax;
    
    

    private List<Integer> deptIds;

    private List<String> userids;

    private List<String> phones;

    private List<String> cnids;

    /**
     * 获取新增实体bean
     */
    public AudioRecord toAddBean() {
        AudioRecord bean = toUpdateBean();
        bean.setCreateTime(DateUtil.getCurrentDateTime());
        return bean;
    }

    /**
     * 获取修改实体bean
     */
    public AudioRecord toUpdateBean() {
        AudioRecord bean = new AudioRecord();
        bean.setComment(comment);
        return bean;
    }

    public void processSearch() {
        if (StringUtil.isNotEmpty(userid) && userid.replace(" ", "").replace("，", ",").split(",").length > 0) {
            userids = Arrays.asList(userid.replace(" ", "").replace("，", ",").split(","));
            if (userids.size() > 50) {
                userids = userids.subList(0, 50);
            }
        }
        if (StringUtil.isNotEmpty(phone) && phone.replace(" ", "").replace("，", ",").split(",").length > 0) {
            phones = Arrays.asList(phone.replace(" ", "").replace("，", ",").split(","));
            if (phones.size() > 50) {
                phones = phones.subList(0, 50);
            }
        }
        if (StringUtil.isNotEmpty(cnid) && cnid.replace(" ", "").replace("，", ",").split(",").length > 0) {
            cnids = Arrays.asList(cnid.replace(" ", "").replace("，", ",").split(","));
            if (cnids.size() > 50) {
                cnids = cnids.subList(0, 50);
            }
        }
    }
}

