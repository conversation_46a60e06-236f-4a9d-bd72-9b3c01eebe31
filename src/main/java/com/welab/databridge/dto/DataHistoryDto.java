package com.welab.databridge.dto;

import com.welab.databridge.dto.common.BasePageDto;
import com.welab.databridge.entity.DataHistory;
import lombok.Data;

import java.util.Map;

/**
 * 历史版本(DataHistory)传输对象
 *
 * <AUTHOR>
 * @since 2023-02-02 15:53:02
 */
@Data
public class DataHistoryDto extends BasePageDto {
    /**
     * 主键,自增长
     */
    private Integer id;
    /**
     * 业务类型
     */
    private String bizType;
    /**
     * 业务id
     */
    private Integer bizId;
    /**
     * 操作 add:新增 modify:修改 delete:删除 online:上线 offline下线 cancel:撤销 suspend:暂停 upload:上传
     */
    private String operation;
    /**
     * 版本号
     */
    private String version;
    /**
     * 内容 json
     */
    private Map content;
    /**
     * 操作人id
     */
    private String operatorId;
    /**
     * 操作人姓名
     */
    private String operatorName;
    /**
     * 添加时间
     */
    private String createTime;
    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 获取新增实体bean
     */
    public DataHistory toAddBean() {
        DataHistory bean = new DataHistory();
        bean.setBizId(bizId);
        bean.setBizType(bizType);
        bean.setOperation(operation);
        bean.setContent(content);
        return bean;
    }

    /**
     * 获取修改实体bean
     */
    public DataHistory toUpdateBean() {
        return toAddBean();
    }

}

