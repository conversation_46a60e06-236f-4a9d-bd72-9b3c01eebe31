package com.welab.databridge.dto;

import com.welab.databridge.dto.common.BasePageDto;
import com.welab.databridge.entity.MessageFail;
import lombok.Data;

import java.util.List;

/**
 * 短信上传失败记录(MessageFail)传输对象
 *
 * <AUTHOR>
 * @since 2023-01-03 14:29:43
 */
@Data
public class MessageFailDto extends BasePageDto {
    /**
     * 主键
     */
    private Integer id;
    /**
     * 上传批次ID
     */
    private Integer uploadId;
    /**
     * 部门ID
     */
    private Integer deptId;
    /**
     * 发送内容
     */
    private String content;
    /**
     * 供应商
     */
    private String supplier;
    /**
     * 发送时间
     */
    private String sendTime;
    /**
     * 发送状态，发送成功，发送失败、未知
     */
    private String status;
    /**
     * 失败原因
     */
    private String failMsg;
    /**
     * 操作人
     */
    private String operator;

    private List<Integer> deptIds;

    /**
     * 获取新增实体bean
     */
    public MessageFail toAddBean() {
        MessageFail bean = new MessageFail();
        //TODO 设置需要的参数, 光标在bean上,然后command+Enter自动生成
        return bean;
    }

    /**
     * 获取修改实体bean
     */
    public MessageFail toUpdateBean() {
        MessageFail bean = new MessageFail();
        //TODO 设置需要修改的参数, 光标在bean上,然后command+Enter自动生成
        return bean;
    }

}

