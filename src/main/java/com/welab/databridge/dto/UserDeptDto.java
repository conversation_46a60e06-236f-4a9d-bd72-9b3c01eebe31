package com.welab.databridge.dto;

import java.util.Date;

import com.welab.databridge.dto.common.BasePageDto;
import com.welab.databridge.entity.UserDept;

import lombok.Data;

/**
 * 用户部门关系表(UserDept)传输对象
 *
 * <AUTHOR>
 * @since 2023-01-03 14:29:56
 */
@Data
public class UserDeptDto extends BasePageDto {
    /**
     * 主键
     */
    private Integer id;
    /**
     * 部门ID
     */
    private Integer deptId;
    /**
     * userid
     */
    private String userid;
    /**
     * 用户名
     */
    private String name;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 获取新增实体bean
     */
    public UserDept toAddBean() {
        UserDept bean = new UserDept();
        //TODO 设置需要的参数, 光标在bean上,然后command+Enter自动生成
        return bean;
    }

    /**
     * 获取修改实体bean
     */
    public UserDept toUpdateBean() {
        UserDept bean = new UserDept();
        //TODO 设置需要修改的参数, 光标在bean上,然后command+Enter自动生成
        return bean;
    }

}

