package com.welab.databridge.dto;

import com.welab.databridge.dto.common.BasePageDto;
import com.welab.databridge.entity.MessageRecord;
import com.welab.databridge.util.StringUtil;
import lombok.Data;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 短信记录(MessageRecord)传输对象
 *
 * <AUTHOR>
 * @since 2023-01-03 14:29:50
 */
@Data
public class MessageRecordDto extends BasePageDto {
    /**
     * 主键
     */
    private Integer id;
    /**
     * 上传批次ID
     */
    private Integer uploadId;
    /**
     * 部门ID
     */
    private Integer deptId;
    /**
     * userid
     */
    private String userid;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 身份证号
     */
    private String cnid;
    /**
     * 发送内容
     */
    private String content;
    /**
     * 服务商
     */
    private String supplier;
    /**
     * 发送时间
     */
    private String sendTime;
    /**
     * 发送状态，0-发送成功，1-发送失败、2-未知
     */
    private Integer status;
    /**
     * 上传方式：0-接口，1-手动
     */
    private Integer uploadMethod;
    private Integer deleted;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 发送开始时间
     */
    private String startTime;
    /**
     * 发送结束时间
     */
    private String endTime;

    private List<Integer> deptIds;

    private List<String> userids;

    private List<String> phones;

    private List<String> cnids;

    /**
     * 获取新增实体bean
     */
    public MessageRecord toAddBean() {
        MessageRecord bean = new MessageRecord();
        //TODO 设置需要的参数, 光标在bean上,然后command+Enter自动生成
        return bean;
    }

    /**
     * 获取修改实体bean
     */
    public MessageRecord toUpdateBean() {
        MessageRecord bean = new MessageRecord();
        //TODO 设置需要修改的参数, 光标在bean上,然后command+Enter自动生成
        return bean;
    }

    public void processSearch() {
        if (StringUtil.isNotEmpty(userid) && userid.replace(" ", "").replace("，", ",").split(",").length > 0) {
            userids = Arrays.asList(userid.replace(" ", "").replace("，", ",").split(","));
            if (userids.size() > 50) {
                userids = userids.subList(0, 50);
            }
        }
        if (StringUtil.isNotEmpty(phone) && phone.replace(" ", "").replace("，", ",").split(",").length > 0) {
            phones = Arrays.asList(phone.replace(" ", "").replace("，", ",").split(","));
            if (phones.size() > 50) {
                phones = phones.subList(0, 50);
            }
        }
        if (StringUtil.isNotEmpty(cnid) && cnid.replace(" ", "").replace("，", ",").split(",").length > 0) {
            cnids = Arrays.asList(cnid.replace(" ", "").replace("，", ",").split(","));
            if (cnids.size() > 50) {
                cnids = cnids.subList(0, 50);
            }
        }
    }

}

