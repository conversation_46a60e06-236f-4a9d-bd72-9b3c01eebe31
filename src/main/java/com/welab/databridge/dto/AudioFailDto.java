package com.welab.databridge.dto;

import com.welab.databridge.dto.common.BasePageDto;
import com.welab.databridge.entity.AudioFail;
import lombok.Data;

import java.util.List;

/**
 * 音频上传失败记录记录(AudioFail)传输对象
 *
 * <AUTHOR>
 * @since 2023-01-03 14:29:58
 */
@Data
public class AudioFailDto extends BasePageDto {
    /**
     * 主键
     */
    private Integer id;
    /**
     * 上传批次ID
     */
    private Integer uploadId;
    /**
     * 部门ID
     */
    private Integer deptId;
    /**
     * 手机号
     */
    private String name;
    /**
     * 上传方式：0-接口，1-手动
     */
    private Integer uploadMethod;
    /**
     * 失败原因
     */
    private String failMsg;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 创建时间
     */
    private String createTime;

    private List<Integer> deptIds;

    /**
     * 获取新增实体bean
     */
    public AudioFail toAddBean() {
        AudioFail bean = new AudioFail();
        //TODO 设置需要的参数, 光标在bean上,然后command+Enter自动生成
        return bean;
    }

    /**
     * 获取修改实体bean
     */
    public AudioFail toUpdateBean() {
        AudioFail bean = new AudioFail();
        //TODO 设置需要修改的参数, 光标在bean上,然后command+Enter自动生成
        return bean;
    }
}

