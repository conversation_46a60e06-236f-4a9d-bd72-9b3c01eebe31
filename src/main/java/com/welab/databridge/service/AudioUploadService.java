package com.welab.databridge.service;

import com.github.pagehelper.PageInfo;
import com.welab.databridge.common.ResponseVo;
import com.welab.databridge.common.ResultCode;
import com.welab.databridge.dto.AudioUploadDto;
import com.welab.databridge.entity.AudioUpload;
import com.welab.databridge.vo.audit.CallbackParam;
import com.welab.databridge.vo.file.FileVo;

import java.util.List;

/**
 * 音频上传记录(AudioUpload)表服务接口
 * (代码生成器自动生成)
 *
 * <AUTHOR>
 * @date 2023-01-03 14:29:39
 */
public interface AudioUploadService {

    /**
     * 根据ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    AudioUpload getById(Integer id);

    /**
     * 列表条件查询
     *
     * @param dto
     * @return
     */
    PageInfo<AudioUpload> queryByCondition(AudioUploadDto dto);

    /**
     * 新增数据
     *
     * @param dto 实例对象
     * @return ResultCode
     */
    ResultCode insert(AudioUploadDto dto);

    /**
     * 修改数据
     *
     * @param dto 实例对象
     * @return ResultCode
     */
    ResultCode update(AudioUploadDto dto);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return ResultCode
     */
    ResponseVo deleteById(Integer id);

    /**
     * 上传音频
     *
     * @param operatorId 操作人id
     * @param deptId 部门ID
     * @param manualAgentId 手动输入的坐席ID（可选）
     * @param files 上传文件
     */
    void upload(String operatorId, int deptId, String manualAgentId, List<FileVo> files);

    /**
     * 审核回调
     *
     * @param callbackParam 回调参数
     * @return ResponseVo
     */
    ResponseVo callback(CallbackParam callbackParam);
}
