package com.welab.databridge.service;

import com.github.pagehelper.PageInfo;
import com.welab.databridge.common.ResponseVo;
import com.welab.databridge.common.ResultCode;
import com.welab.databridge.dto.MessageUploadDto;
import com.welab.databridge.entity.MessageUpload;
import com.welab.databridge.vo.audit.CallbackParam;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.List;

/**
 * 短信上传记录(MessageUpload)表服务接口
 * (代码生成器自动生成)
 *
 * <AUTHOR>
 * @date 2023-01-03 14:29:47
 */
public interface MessageUploadService {

    /**
     * 根据ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    MessageUpload getById(Integer id);

    /**
     * 列表条件查询
     *
     * @param dto
     * @return
     */
    PageInfo<MessageUpload> queryByCondition(MessageUploadDto dto);

    /**
     * 新增数据
     *
     * @param dto 实例对象
     * @return ResultCode
     */
    ResultCode insert(MessageUploadDto dto);

    /**
     * 修改数据
     *
     * @param dto 实例对象
     * @return ResultCode
     */
    ResultCode update(MessageUploadDto dto);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return ResultCode
     */
    ResponseVo deleteById(Integer id);

    /**
     * 下载模板
     *
     * @param request
     * @param response
     */
    void downloadTemplate(HttpServletRequest request, HttpServletResponse response);

    /**
     * 上传短信
     *
     * @param operatorId 操作人Id
     * @param deptId 部门ID
     * @param inputStream 上传文件
     */
    void upload(String operatorId, int deptId, InputStream inputStream);

    /**
     * 审核回调
     *
     * @param callbackParam 回调参数
     * @return ResponseVo
     */
    ResponseVo callback(CallbackParam callbackParam);
}
