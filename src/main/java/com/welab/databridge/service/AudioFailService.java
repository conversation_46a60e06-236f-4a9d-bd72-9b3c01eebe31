package com.welab.databridge.service;

import com.welab.databridge.common.ResultCode;
import com.welab.databridge.entity.AudioFail;
import com.welab.databridge.dto.AudioFailDto;

import java.util.List;

/**
 * 音频上传失败记录记录(AudioFail)表服务接口
 * (代码生成器自动生成)
 *
 * <AUTHOR>
 * @date 2023-01-03 14:29:58
 */
public interface AudioFailService {

    /**
     * 根据ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    AudioFail getById(Integer id);

    /**
     * 列表条件查询
     *
     * @param dto
     * @return
     */
    List<AudioFail> queryByCondition(AudioFailDto dto);

    /**
     * 新增数据
     *
     * @param dto 实例对象
     * @return ResultCode
     */
    ResultCode insert(AudioFailDto dto);

    /**
     * 修改数据
     *
     * @param dto 实例对象
     * @return ResultCode
     */
    ResultCode update(AudioFailDto dto);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return ResultCode
     */
    ResultCode deleteById(Integer id);

}
