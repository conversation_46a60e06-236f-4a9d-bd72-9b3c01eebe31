package com.welab.databridge.service;

import com.welab.databridge.common.ResultCode;
import com.welab.databridge.entity.AudioRecordTemp;
import com.welab.databridge.dto.AudioRecordTempDto;

import java.util.List;

/**
 * 音频记录临时(AudioRecordTemp)表服务接口
 * (代码生成器自动生成)
 *
 * <AUTHOR>
 * @date 2024-03-14 10:50:16
 */
public interface AudioRecordTempService {

    /**
     * 根据ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    AudioRecordTemp getById(Integer id);

    /**
     * 列表条件查询
     *
     * @param dto
     * @return
     */
    List<AudioRecordTemp> queryByCondition(AudioRecordTempDto dto);

    /**
     * 查询未入库的正常记录
     *
     * @return
     */
    List<AudioRecordTemp> queryUnSync();

    /**
     * 新增数据
     *
     * @param bean 实例对象
     * @return ResultCode
     */
    ResultCode insert(AudioRecordTemp bean);

    /**
     * 修改数据
     *
     * @param dto 实例对象
     * @return ResultCode
     */
    ResultCode update(AudioRecordTempDto dto);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return ResultCode
     */
    ResultCode deleteById(Integer id);

}
