package com.welab.databridge.service;

import com.welab.databridge.common.ResponseVo;
import com.welab.databridge.common.ResultCode;
import com.welab.databridge.entity.Department;
import com.welab.databridge.dto.DepartmentDto;
import com.welab.databridge.entity.UserDept;
import com.welab.databridge.vo.dept.DeptVo;

import java.util.List;

/**
 * 部门表(Department)表服务接口
 * (代码生成器自动生成)
 *
 * <AUTHOR>
 * @date 2023-01-03 14:29:45
 */
public interface DepartmentService {

    /**
     * 根据ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    Department getById(Integer id);

    /**
     * 列表条件查询
     *
     * @param dto
     * @return
     */
    List<Department> queryByCondition(DepartmentDto dto);

    /**
     * 新增数据
     *
     * @param dto 实例对象
     * @return ResultCode
     */
    ResponseVo insert(DepartmentDto dto);

    /**
     * 修改数据
     *
     * @param dto 实例对象
     * @return ResultCode
     */
    ResponseVo update(DepartmentDto dto);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return ResultCode
     */
    ResponseVo deleteById(Integer id);

    /**
     * 通过父部门查询子部门
     *
     * @param list
     * @param parentId
     */
    void listChildrenByParent(List<Integer> list, Integer parentId);

    /**
     * 获取用户所有部门
     *
     * @return
     */
    List<Department> listUserDepartment();

    /**
     * 获取部门用户
     *
     * @param id 部门ID
     * @return
     */
    List<UserDept> listDepartmentUser(int id);

    /**
     * 用户列表
     *
     * @return
     */
    List<String> listUser();

    /**
     * 设置部门
     * @param deptVo
     * @return
     */
    ResponseVo setDept(DeptVo deptVo);
}
