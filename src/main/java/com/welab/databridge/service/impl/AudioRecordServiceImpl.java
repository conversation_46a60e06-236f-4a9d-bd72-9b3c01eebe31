package com.welab.databridge.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.welab.anti.fraud.vo.VoiceSendVo;
import com.welab.databridge.common.ResponseVo;
import com.welab.databridge.common.ResultCode;
import com.welab.databridge.dao.AudioApplyDao;
import com.welab.databridge.dao.AudioRecordDao;
import com.welab.databridge.dao.AudioTempDao;
import com.welab.databridge.dao.AudioUploadDao;
import com.welab.databridge.dto.AudioApplyDto;
import com.welab.databridge.dto.AudioRecordDto;
import com.welab.databridge.dto.AudioRecordTempDto;
import com.welab.databridge.dto.AudioTempDto;
import com.welab.databridge.entity.*;
import com.welab.databridge.enums.*;
import com.welab.databridge.help.AudioHelpService;
import com.welab.databridge.help.AudioHelper;
import com.welab.databridge.oss.OssService;
import com.welab.databridge.service.AudioApplyService;
import com.welab.databridge.service.AudioRecordService;
import com.welab.databridge.service.AudioRecordTempService;
import com.welab.databridge.service.AudioTempService;
import com.welab.databridge.service.impl.common.BaseServiceImpl;
import com.welab.databridge.util.*;
import com.welab.databridge.vo.audio.*;
import com.welab.databridge.vo.audit.CallbackParam;
import com.welab.databridge.vo.common.BatchVo;
import com.welab.databridge.vo.common.MessageInfo;
import com.welab.databridge.vo.file.FileVo;
import com.welab.user.interfaces.dto.UserDTO;
import com.welab.user.interfaces.facade.UserServiceFacade;
import com.welab.usercenter.model.base.Profile;
import com.welab.usercenter.service.ProfileService;
import com.wolaidai.approval.model.voice.VoiceSendDetail;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.beans.IntrospectionException;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.nio.file.Files;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 音频记录(AudioRecord)表服务实现类
 * (代码生成器自动生成)
 *
 * <AUTHOR>
 * @date 2023-01-03 14:29:53
 */
@Slf4j
@Service("audioRecordService")
public class AudioRecordServiceImpl extends BaseServiceImpl implements AudioRecordService {
    @Value("${downloadLimit:100}")
    private int downloadLimit;
    @Value("${env}")
    private String env;

    @Resource
    private AudioRecordDao audioRecordDao;
    @Resource
    private OssService ossService;
    @Resource
    private AudioApplyDao audioApplyDao;
    @Resource
    private AudioUploadDao audioUploadDao;
    @Resource
    private AudioApplyService audioApplyService;
    @Resource
    private AudioHelpService audioHelpService;
    @Resource
    private AudioTempService audioTempService;
    @Resource
    private AudioTempDao audioTempDao;
    @Reference(timeout = 8000)
    private UserServiceFacade userServiceFacade;
    @Reference(timeout = 8000)
    private ProfileService profileService;
    @Resource
    private AudioRecordTempService audioRecordTempService;
    @Resource
    private AudioRecordService audioRecordService;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public AudioRecord getById(Integer id) {
        return this.audioRecordDao.getById(id);
    }

    /**
     * 列表条件查询
     *
     * @param dto
     * @return
     */
    @Override
    public PageInfo<AudioRecord> queryByCondition(AudioRecordDto dto) {
        List<AudioRecord> list = new ArrayList<>();
        PageInfo<AudioRecord> pageInfo = new PageInfo<>(list);
        List<Integer> deptIds = getDeptList(dto.getDeptId());
        if (ObjectUtil.isNotEmpty(deptIds)) {
            dto.setDeptIds(deptIds);
            PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
            list = this.audioRecordDao.queryByCondition(dto);
            pageInfo = new PageInfo<>(list);
            if (ObjectUtil.isNotEmpty(pageInfo.getList())) {
                pageInfo.getList().stream()
                        .forEach(v -> {
                            v.setPhone(v.getPhone().replace(v.getPhone().substring(3, 7), "****"));
                            v.setCnid("");
                            v.setSupplier(SupplierType.getValueByKey(v.getSupplier()));
                            if (Objects.equals(v.getUploadMethod(), UploadMethod.INTERFACE.getValue())) {
                                v.setAgentId(v.getOperator());
                            }
                        });
            }
        }
        return pageInfo;
    }

    /**
     * 新增数据
     *
     * @param dto 转换对象
     */
    @Override
    public ResultCode insert(AudioRecordDto dto) {
        AudioRecord bean = dto.toAddBean();
        this.audioRecordDao.insert(bean);
        return ResultCode.SUCCESS;
    }

    /**
     * 修改数据
     *
     * @param dto 转换对象
     */
    @Override
    public ResultCode update(AudioRecordDto dto) {
        AudioRecord bean = this.audioRecordDao.getById(dto.getId());
        if (null == bean) {
            return ResultCode.DATA_NOT_EXIST;
        }
        AudioRecord updateBean = dto.toUpdateBean();
        updateBean.setId(bean.getId());
        this.audioRecordDao.update(updateBean);
        return ResultCode.SUCCESS;
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return ResultCode
     */
    @Override
    public ResultCode deleteById(Integer id) {
        boolean bool = this.audioRecordDao.deleteById(id);
        if (bool) {
            return ResultCode.SUCCESS;
        } else {
            return ResultCode.FAILED;
        }
    }

    /**
     * 下载音频
     *
     * @param batchVo
     * @return ResultCode
     */
    @Override
    public ResponseVo download(BatchVo batchVo) {
        Map map = new HashMap();
        map.put("ids", batchVo.getIds());
        List<AudioRecord> list = this.audioRecordDao.getByIds(map);
        if (ObjectUtil.isEmpty(list)) {
            return ResponseVo.error(ResultCode.DATA_NOT_EXIST);
        }

        if (checkApplyLimit(batchVo.getIds())) {
            return ResponseVo.error(ResultCode.AUDIO_DOWNLOAD_LIMIT);
        }

        AudioApply audioApply = new AudioApply();
        audioApply.setAudioIds(batchVo.getIds());
        audioApply.setReason(batchVo.getReason());
        audioApply.setStatus(AudioDownloadStatus.DEFAULT.getStatus());
        audioApply.setApproveStatus(AuditStatus.DEFAULT.getValue());
        audioApply.setApplier(getOperatorName());
        audioApply.setCreateTime(DateUtil.getCurrentDateTime());
        this.audioApplyDao.insert(audioApply);

        //向审核模块发起下载申请
        List<AudioDownloadAuditVo> auditList = Arrays.asList(buildAuditObject(list, audioApply));
        List<String> conditionKeys = Arrays.asList("batchNo", "operator");
        return applyAudit(auditList, ApproveCode.AUDIO_RECORD_DOWNLOAD.getApproveCode(), Operation.DOWNLOAD.getType(), Operation.DOWNLOAD.getType(), conditionKeys);
    }

    /**
     * 校验用户单日下载量是否超限
     * @param ids
     * @return
     */
    private boolean checkApplyLimit(List<Integer> ids) {
        List<Integer> idList = new ArrayList<>();
        idList.addAll(ids);
        AudioApplyDto query = new AudioApplyDto();
        query.setApplier(getOperatorName());
        query.setStartTime(DateUtil.getCurrentDateStart());
        query.setEndTime(DateUtil.getCurrentDateEnd());
        List<AudioApply> list = this.audioApplyDao.queryByCondition(query);
        if (ObjectUtil.isNotEmpty(list)) {
            for (AudioApply audioApply : list) {
                idList.addAll(audioApply.getAudioIds());
            }
        }
        idList = idList.stream().collect(Collectors.toSet()).stream().collect(Collectors.toList());
        if (idList.size() > downloadLimit) {
            return true;
        }
        return false;
    }

    /**
     * 构造用于提交审核系统的数据
     *
     * @param list
     * @param audioApply
     * @return
     */
    private AudioDownloadAuditVo buildAuditObject(List<AudioRecord> list, AudioApply audioApply) {
        List<Integer> uploadIds = list.stream().map(v -> v.getUploadId()).collect(Collectors.toList());
        Map query = new HashMap();
        query.put("ids", uploadIds);
        List<AudioUpload> audioUploads = audioUploadDao.getByIds(query);
        AudioDownloadAuditVo audioDownloadAuditVo = new AudioDownloadAuditVo();
        audioDownloadAuditVo.setId(audioApply.getId());
        if (ObjectUtil.isNotEmpty(audioUploads)) {
            audioDownloadAuditVo.setBatchNo(audioUploads.stream().map(v->v.getBatchNo()).collect(Collectors.toSet()).stream().collect(Collectors.joining(",")));
        }
        audioDownloadAuditVo.setOperator(getOperatorName());
        audioDownloadAuditVo.setApplyTime(audioApply.getCreateTime());
        audioDownloadAuditVo.setReason(audioApply.getReason());
        audioDownloadAuditVo.setDownloadNum(audioApply.getAudioIds().size());

        List<AudioRecordVo> audioRecordVos = new ArrayList<>();
        AudioRecordVo audioRecordVo;
        for (AudioRecord audioRecord : list) {
            AudioUpload audioUpload = null;
            if (ObjectUtil.isNotEmpty(audioUploads)) {
                audioUpload = audioUploads.stream()
                        .filter(au -> au.getId().intValue() == audioRecord.getUploadId().intValue())
                        .findFirst()
                        .orElse(null);
            }
            audioRecordVo = new AudioRecordVo();
            BeanUtil.copyProperties(audioRecord, audioRecordVo);
            if (audioUpload == null || StringUtil.isEmpty(audioUpload.getBatchNo())) {
                audioRecordVo.setBatchNo("" + System.currentTimeMillis());
            } else {
                audioRecordVo.setBatchNo(audioUpload.getBatchNo());
            }
            audioRecordVo.setPhone(audioRecord.getPhone().replace(audioRecord.getPhone().substring(3, 7), "****"));
            audioRecordVos.add(audioRecordVo);
        }
        audioDownloadAuditVo.setDetailList(audioRecordVos);
        return audioDownloadAuditVo;
    }

    /**
     * 查询语音所属部门列表
     *
     * @return
     */
    @Override
    public List<String> listDept() {
        List<String> list = new ArrayList<>();
//        List<AudioRecord> deptList = this.audioRecordDao.listDept();
//        if (ObjectUtil.isNotEmpty(deptList)) {
//            list = deptList.stream()
//                    .filter(d -> ObjectUtil.isNotEmpty(d) && StringUtil.isNotEmpty(d.getDept()))
//                    .map(dept -> dept.getDept())
//                    .collect(Collectors.toList());
//        }
        return list;
    }

    /**
     * 查询语音所属机构列表
     * 表中数据太多，全表扫描太耗时，此方法注释掉，后续可以考虑为机构单独维护一张表
     *
     * @return
     */
    @Override
    public List<String> listOrg() {
        List<String> list = new ArrayList<>();
//        List<AudioRecord> orgList = this.audioRecordDao.listOrg();
//        if (ObjectUtil.isNotEmpty(orgList)) {
//            list = orgList.stream()
//                    .filter(audioRecord -> ObjectUtil.isNotEmpty(audioRecord) && StringUtil.isNotEmpty(audioRecord.getOrg()))
//                    .map(audioRecord -> audioRecord.getOrg())
//                    .collect(Collectors.toList());
//        }
        return list;
    }

    /**
     * 获取语音连接
     *
     * @return
     */
    @Override
    public ResponseVo getUrl(Integer id, String userid) {
        String url = "";
        List<Integer> deptList = getDeptByOperatorId(userid);
        AudioRecord audioRecord = this.audioRecordDao.getById(id);
        if (ObjectUtil.isNotEmpty(audioRecord)) {
            if (deptList.contains(audioRecord.getDeptId())) {
                if (!LocalDateTime.now().minusDays(1095l).isBefore(DateUtil.format(audioRecord.getStartTime(), null))) {
                    try {
                        if (ossService.isRestoreCompleted(audioRecord.getUrl())) {
                            url = ossService.getOssUrlByDocServer(audioRecord.getUrl(), 3600 * 1000 * 2l);
                        }
                    } catch (Exception e) {
                        new Thread() {
                            public void run() {
                                ossService.restoreByDocServer(audioRecord.getUrl());
                            }
                        }.start();
                        return ResponseVo.error(ResultCode.AUDIO_IS_ARCHIVE);
                    }
                } else {
                    url = ossService.getOssUrlByDocServer(audioRecord.getUrl(), 3600 * 1000 * 2l);
                }
            }
        }
        return ResponseVo.success(url);
    }

    /**
     * 将回听状态更新为已听
     * @param id 数据id
     * @param userid 用户id
     * @return
     */
    @Override
    public ResponseVo modifyAudioStatus(Integer id, String userid) {
        AudioRecord audioRecord = this.audioRecordDao.getById(id);
        List<Integer> deptList = getDeptByOperatorId(userid);
        if (ObjectUtil.isEmpty(audioRecord)) {
            return ResponseVo.error(ResultCode.DATA_NOT_EXIST);
        }
        if (!deptList.contains(audioRecord.getDeptId())) {
            return ResponseVo.error(ResultCode.FAILED);
        }
        audioRecord.setListened(ListenedRemark.LISTENED.getValue());
        audioRecord.setListenTime(DateUtil.getCurrentDateTime());
        String operatorName = getOperatorName();
        if (StringUtil.isEmpty(audioRecord.getListener())) {
            audioRecord.setListener(operatorName);
        } else if (!audioRecord.getListener().contains(operatorName)) {
            audioRecord.setListener(audioRecord.getListener() + "," + operatorName);
        }
        this.audioRecordDao.update(audioRecord);
        return ResponseVo.success();
    }

    /**
     * 修复异常数据
     * @param queryVo
     * @return
     */
    @Override
    public ResponseVo queryRecord(QueryVo queryVo) {
//        if (QueryType.QUERY_APPROVAL.getKey().equals(queryVo.getQueryType())) {
//            List<VoiceSendDetail> list = audioHelpService.callApprovalRecord(queryVo.getQueryDate());
//            audioTempService.saveApprovalData(list, queryVo.getQueryDate());
//        } else if (QueryType.QUERY_FRAUD.getKey().equals(queryVo.getQueryType())) {
//            List<VoiceSendVo> list = audioHelpService.callFraudRecord(queryVo.getQueryDate());
//            audioTempService.saveFraudData(list, queryVo.getQueryDate());
//        } else if (QueryType.QUERY_CS.getKey().equals(queryVo.getQueryType())) {
//            List<CollectionRecordVo> list = AudioHelper.callCSRecord(queryVo.getQueryDate());
//            audioTempService.saveCSAndCollectionData(list, queryVo.getQueryDate());
//        } else if (QueryType.QUERY_COLLECTION.getKey().equals(queryVo.getQueryType())) {
//            List<CollectionRecordVo> list = AudioHelper.callCollectionRecord(queryVo.getQueryDate());
//            audioTempService.saveCSAndCollectionData(list, queryVo.getQueryDate());
//        } else
        if (QueryType.QUERY_CTI.getKey().equals(queryVo.getQueryType())) {
            long start = System.currentTimeMillis();
            List<AudioTemp> audioTemps = audioTempService.getByIds(queryVo.getAudioTempIds());
            JSONObject data = AudioHelper.processCtiData(audioTemps);
            saveCtiRecord(audioTemps, data);
            log.info("天润语音拉取上传,时间: " + (System.currentTimeMillis() - start));
        } else if (QueryType.QUERY_94AI.getKey().equals(queryVo.getQueryType())) {
            long start = System.currentTimeMillis();
            List<AudioTemp> audioTemps = audioTempService.getByIds(queryVo.getAudioTempIds());
            List<JSONObject> list = AudioHelper.process94AiData(audioTemps);
            save94AiRecord(audioTemps, list);
            log.info("94语音拉取上传,时间: " + (System.currentTimeMillis() - start));
        } else if (QueryType.FIX_94AI.getKey().equals(queryVo.getQueryType())) {
            long start = System.currentTimeMillis();
            fix94AiRecord(queryVo.getStartDate(), queryVo.getEndDate(), queryVo.getType());
            log.info("修复94语音,时间: " + (System.currentTimeMillis() - start));
        } else if (QueryType.QUERY_LB.getKey().equals(queryVo.getQueryType())) {
            long start = System.currentTimeMillis();
            List<AudioTemp> audioTemps = audioTempService.getByIds(queryVo.getAudioTempIds());
            saveLBRecord(audioTemps);
            log.info("灵伴语音拉取上传,时间: " + (System.currentTimeMillis() - start));
        } else if (QueryType.PROCESS_DATA.getKey().equals(queryVo.getQueryType())) {
            AudioTempDto query = new AudioTempDto();
            query.setQueryDate(queryVo.getQueryDate());
            query.setStatus(ProcessStatus.DEFAULT.getStatus());
            List<AudioTemp> audioTemps = audioTempService.queryByCondition(query);
            if (ObjectUtil.isNotEmpty(audioTemps)) {
                processData(audioTemps);
            }
        } else if (QueryType.PROCESS_CTI_DATA.getKey().equals(queryVo.getQueryType())) {
            processCtiData(queryVo.getStartDate(), queryVo.getEndDate(), queryVo.getEnterpriseId());
        }
        else if (QueryType.PULL_LB_DAY_DATA.getKey().equals(queryVo.getQueryType())) {
            pullLBDayData(queryVo);
        }
        else if (QueryType.PROCESS_LB_DAY_DATA.getKey().equals(queryVo.getQueryType())) {
            processLBDayData(queryVo);
        }
        else if (QueryType.PROCESS_LB_DATA.getKey().equals(queryVo.getQueryType())) {
            String endDate = queryVo.getEndDate();
            while (queryVo.getStartDate().compareTo(endDate) < 0) {
                queryVo.setQueryDate(endDate);
                pullLBDayData(queryVo);
                processLBDayData(queryVo);
                endDate = DateUtil.formatDate(DateUtil.formatDate(endDate, DateUtil.FORMAT_SIMPLE_DATE).minusDays(1), DateUtil.FORMAT_SIMPLE_DATE);
            }
        }
        else if (QueryType.PULL_CTI_DATA.getKey().equals(queryVo.getQueryType())) {
            pullCtiData(queryVo);
        }
        else if (QueryType.DELETE_DATA.getKey().equals(queryVo.getQueryType())) {
            Map map = new HashMap();
            map.put("startDate", queryVo.getStartDate());
            map.put("endDate", queryVo.getEndDate());
            if (StringUtil.isNotEmpty(queryVo.getEnterpriseId())) {
                map.put("enterpriseId", queryVo.getEnterpriseId());
            } else {
                if (queryVo.getDeptId() != null) {
                    map.put("deptId", queryVo.getDeptId());
                }
            }
            audioRecordDao.deleteByStartTime(map);
            audioTempDao.deleteByQueryDate(map);
        } else if (QueryType.LIST_DIR.getKey().equals(queryVo.getQueryType())) {
            List<String> list = new ArrayList<>();
            ossService.listDir(queryVo.getCommonPrefix(), list);
            log.info("list dir result: " + list.stream().collect(Collectors.joining(",")));
            return ResponseVo.success(list);
        } else if (QueryType.LIST_FILE.getKey().equals(queryVo.getQueryType())) {
            List<AudioRecordTemp> tempList = new ArrayList<>();
            List<OssMeta> list = new ArrayList<>();
            ossService.listFile(queryVo.getCommonPrefix(), list);
            List<String> noMatchList = matchFile(queryVo, list, tempList, QueryType.LIST_FILE.getKey());
            return ResponseVo.success(noMatchList);
        } else if (QueryType.SYNC_FILE.getKey().equals(queryVo.getQueryType())) {
            List<AudioRecordTemp> tempList = new ArrayList<>();
            List<OssMeta> list = new ArrayList<>();
            ossService.listFile(queryVo.getCommonPrefix(), list);
            List<String> noMatchList = matchFile(queryVo, list, tempList, QueryType.SYNC_FILE.getKey());
            if (tempList.size() > 0) {
                for (AudioRecordTemp audioRecordTemp : tempList) {
                    try {
                        audioRecordTempService.insert(audioRecordTemp);
                    } catch (Exception e) {
                        log.error("AudioRecordTemp save error: " + JSONObject.toJSONString(audioRecordTemp));
                    }
                }
                List<AudioRecordTemp> audioRecordTempList = audioRecordTempService.queryUnSync();
                if (ObjectUtil.isNotEmpty(audioRecordTempList)) {
                    List<AudioRecord> audioRecordList = new ArrayList<>();
                    AudioRecord audioRecord;
                    for (AudioRecordTemp audioRecordTemp : audioRecordTempList) {
                        audioRecord = new AudioRecord();
                        if (audioRecordTemp.getDeptId() == -1) {
                            audioRecord.setDeptId(44);
                        } else {
                            audioRecord.setDeptId(audioRecordTemp.getDeptId());
                        }
                        audioRecord.setUploadId(-1);
                        audioRecord.setPhone(audioRecordTemp.getPhone());
                        audioRecord.setUserid(audioRecordTemp.getUserid());
                        audioRecord.setCnid(audioRecordTemp.getCnid());
                        audioRecord.setUrl(audioRecordTemp.getUrl());
                        audioRecord.setStartTime(audioRecordTemp.getStartTime());
                        if (audioRecordTemp.getDir().contains("呼入")) {
                            audioRecord.setCallType(1);
                        } else {
                            audioRecord.setCallType(0);
                        }
                        audioRecord.setUploadTime(audioRecordTemp.getUploadTime());
                        audioRecord.setUploadMethod(audioRecordTemp.getUploadMethod());
                        audioRecord.setStatus(0);
                        audioRecord.setOperator(audioRecordTemp.getOperator());
                        audioRecord.setCreateTime(DateUtil.getCurrentDateTime());
                        audioRecord.setSupplier(audioRecordTemp.getSupplier());
                        audioRecordList.add(audioRecord);
                    }
                    if (ObjectUtil.isNotEmpty(audioRecordList)) {
                        audioRecordService.saveRecord(audioRecordList);
                    }
                    for (AudioRecordTemp audioRecordTemp : audioRecordTempList) {
                        AudioRecordTempDto dto = new AudioRecordTempDto();
                        dto.setId(audioRecordTemp.getId());
                        dto.setFlag(1);
                        audioRecordTempService.update(dto);
                    }
                }
            }
            return ResponseVo.success(noMatchList);
        } else if (QueryType.PROCESS_ZIP.getKey().equals(queryVo.getQueryType())) {
            List<OssMeta> list = new ArrayList<>();
            ossService.listZip(queryVo.getCommonPrefix(), list);
            log.info("zip file: " + list);
            return ResponseVo.success(list);
        }
//        else if (QueryType.PROCESS_EXISTS_AMR.getKey().equals(queryVo.getQueryType())) {
//            processExistsAmr();
//        } else if (QueryType.PROCESS_NULL_FILE.getKey().equals(queryVo.getQueryType())) {
//            processNullFile();
//        }
        return ResponseVo.success();
    }

    private List<String> matchFile(QueryVo queryVo, List<OssMeta> list, List<AudioRecordTemp> tempList, String method) {
        String fileName;
        Pattern p;
        List<Pattern> patternList = new ArrayList<>();
        for (String pattern : queryVo.getPatterns()) {
            p = Pattern.compile(pattern);
            patternList.add(p);
        }
        List<String> fileNames = new ArrayList<>();
        List<String> noMatchList = new ArrayList<>();
        List<String> noDeptList = new ArrayList<>();
        int dirCount = 0;
        for (int i=0; i<list.size(); i++) {
            String str = list.get(i).getKey();
            String updateTime = list.get(i).getUpdateTime();
            if (str.endsWith("/")) {
                dirCount++;
                continue;
            }
            if (str.endsWith(".zip")) {
                log.info("zip file: " + str);
            }
            fileName = str.substring(str.lastIndexOf("/") + 1);
            if (!fileName.contains(".")) {
                continue;
            }
            Integer deptId = DeptEnum.getIdByName(str);
            if (deptId == -1) {
                noDeptList.add(str);
            }
            boolean match = false;
            Matcher m;
            AudioRecordTemp audioRecordTemp = new AudioRecordTemp();
            audioRecordTemp.setDir(str);
            audioRecordTemp.setOperator(queryVo.getOperator());
            audioRecordTemp.setUploadTime(updateTime);
            audioRecordTemp.setDeptId(deptId);
            audioRecordTemp.setUploadMethod(1);
            audioRecordTemp.setSupplier("SA");
            audioRecordTemp.setFlag(0);
            for (Pattern pattern : patternList) {
                m = pattern.matcher(fileName);
                if (m.matches()) {
                    try {
                        if (m.group(1).length() == 6) {
                            audioRecordTemp.setPhone(m.group(2));
                            String time = m.group(1).substring(0,2) + ":" + m.group(1).substring(2,4) + ":" + m.group(1).substring(4);
                            String startTime;
                            try {
                                startTime = updateTime.substring(0, 11) + time;
                                DateUtil.format(startTime, DateUtil.FORMAT_FULL_DATE);
                            } catch (Exception e) {
                                startTime = updateTime;
                            }
                            audioRecordTemp.setStartTime(startTime);
                        } else {
                            AudioVo audioVo = new AudioVo();
                            AudioHelper.setValue(audioVo, m.group(1), m.group(2));
                            audioRecordTemp.setPhone(audioVo.getMobile());
                            audioRecordTemp.setStartTime(audioVo.getStartTime());
                        }
                    } catch (Exception e) {
                        audioRecordTemp.setPhone(m.group(1));
                        audioRecordTemp.setStartTime(updateTime);
                    }
                    fileNames.add(str);
                    match = true;
                    break;
                }
            }
            if (StringUtil.isNotEmpty(audioRecordTemp.getPhone())) {
                audioRecordTemp.setUserid(getUserid(audioRecordTemp.getPhone()));
                audioRecordTemp.setCnid(getCnid(audioRecordTemp.getUserid()));
            }
            String ossKey = "";
            if (QueryType.SYNC_FILE.getKey().equals(method) && !"fat".equals(env)) {
                try {
                    if (str.endsWith(".amr")) {
                        FileVo fileVo = new FileVo();
                        fileVo.setFileName(str.substring(str.lastIndexOf("/") + 1));
                        fileVo.setInputStream(ossService.getFile(str).getObjectContent());
                        AudioHelper.changeAmrToMp3(fileVo);
                        ossKey = ossService.uploadByDocServer(BizType.AUDIO_UPLOAD.getKey(), queryVo.getOperator(), fileVo.getFileName(), fileVo.getInputStream(), false);
                        fileVo.getInputStream().close();
                        AudioHelper.deleteTempFile(fileVo.getFileName());
                    } else {
                        ossKey = ossService.uploadByDocServer(BizType.AUDIO_UPLOAD.getKey(), queryVo.getOperator(),
                                str.substring(str.lastIndexOf("/") + 1), ossService.getFile(str).getObjectContent(), false);
                    }
                } catch (Exception e) {
                    MessageInfo msg = new MessageInfo();
                    msg.setTitle("SA录音拉取失败");
                    msg.setTopic("下载&上传录音失败");
                    msg.setContent("下载&上传文件失败，录音文件地址为：" + str + "  异常: " + e.getMessage());
                    MailUtil.send(msg, Arrays.asList("<EMAIL>"));
                    log.error("下载&上传文件失败，ex: " + e.getMessage() + ", url: " + str);
                }
            }
            audioRecordTemp.setUrl(ossKey);
            tempList.add(audioRecordTemp);
            if (!match) {
                noMatchList.add(str);
            }
        }
        log.info("total count: " + list.size());
        log.info("dir count: " + dirCount);
        log.info("match file count: " + fileNames.size());
        log.info("not match file count: " + noMatchList.size());
        log.info("not dept count: " + noDeptList.size());
        log.info("not match file list: " + noMatchList.stream().collect(Collectors.joining(",")));
        log.info("not dept list: " + noDeptList.stream().collect(Collectors.joining(",")));
        return noMatchList;
    }

    private void pullLBDayData(QueryVo queryVo) {
        log.info("灵伴数据拉取开始,日期：" + queryVo.getQueryDate());
        List<CollectionRecordVo> list = AudioHelper.callCollectionRecord(queryVo.getQueryDate());
        list = list.stream()
                .filter(v -> SupplierType.ST_LB.getKey().equals(v.getProvider()))
                .collect(Collectors.toList());
        audioTempService.saveCSAndCollectionData(list, queryVo.getQueryDate());
        log.info("灵伴数据拉取结束，日期：" + queryVo.getQueryDate());
    }

    private void processLBDayData(QueryVo queryVo) {
        log.info("灵伴数据处理开始,日期：" + queryVo.getQueryDate());
        AudioTempDto query = new AudioTempDto();
        query.setQueryDate(queryVo.getQueryDate());
        query.setProvider(SupplierType.ST_LB.getKey());
        query.setStatus(ProcessStatus.DEFAULT.getStatus());
        List<AudioTemp> audioTemps = audioTempService.queryByCondition(query);
        if (ObjectUtil.isNotEmpty(audioTemps)) {
            saveLBRecord(audioTemps);
        }
        log.info("灵伴数据处理结束，日期：" + queryVo.getQueryDate());
    }

    private void saveCtiRecord(List<AudioTemp> audioTemps, JSONObject data) {
        if (ObjectUtil.isEmpty(audioTemps)) {
            return;
        }

        List<AudioRecord> list = new ArrayList<>();
        List<AudioTemp> finishList = new ArrayList<>();
        AudioRecord audioRecord;
        for (AudioTemp audioTemp : audioTemps) {
            String operator = (audioTemp.getCno() == null ? "" : audioTemp.getCno()) + (audioTemp.getStaffName() == null ? "" : audioTemp.getStaffName());
            audioRecord = buildAudioRecord(audioTemp);
            audioRecord.setOperator(operator);
            String ossKey = "";
            if (CallStatus.SUCCESS.getRemark().equals(audioTemp.getCallStatus())
                    && ObjectUtil.isNotEmpty(data)) {
                String url = data.getString(audioTemp.getRecordFile());
                if (StringUtil.isNotEmpty(url)) {
                    try {
                        File file = FileUtil.getFileFromNet(url);
                        boolean isArchive = false;
                        if (StringUtil.isNotEmpty(audioTemp.getStartTime())) {
                            if (!LocalDateTime.now().minusDays(1095l).isBefore(DateUtil.format(audioTemp.getStartTime(), null))) {
                                isArchive = true;
                            }
                        }
                        FileVo fileVo = new FileVo();
                        fileVo.setFileName(file.getName());
                        fileVo.setInputStream(Files.newInputStream(file.toPath()));
                        audioRecord.setDuration(AudioHelper.getDuration(fileVo));
                        ossKey = ossService.uploadByDocServer(BizType.AUDIO_UPLOAD.getKey(), operator, file.getName(), fileVo.getInputStream(), isArchive);
                    } catch (Exception e) {
                        MessageInfo msg = new MessageInfo();
                        msg.setTitle("TR录音拉取失败");
                        msg.setTopic("下载&上传录音失败");
                        msg.setContent("下载&上传文件失败，录音文件地址为：" + url + "  异常: " + e.getMessage());
                        MailUtil.send(msg, Arrays.asList("<EMAIL>"));
                        log.warn("下载&上传文件失败，ex: " + e.getMessage() + ", url: " +url);
                    }
                }
            }
            audioRecord.setUrl(ossKey);
            list.add(audioRecord);
            finishList.add(audioTemp);
        }

        if (ObjectUtil.isNotEmpty(list)) {
            saveRecord(list);
            this.audioTempService.updateStatus(finishList, ProcessStatus.FINISH.getStatus());
        }
    }

    private void save94AiRecord(List<AudioTemp> audioTemps, List<JSONObject> dataList) {
        if (ObjectUtil.isEmpty(audioTemps)) {
            return;
        }

        List<AudioRecord> list = new ArrayList<>();
        List<AudioTemp> finishList = new ArrayList<>();
        AudioRecord audioRecord;
        for (AudioTemp audioTemp : audioTemps) {
            String operator = (audioTemp.getCno() == null ? "" : audioTemp.getCno()) + (audioTemp.getStaffName() == null ? "" : audioTemp.getStaffName());
            audioRecord = buildAudioRecord(audioTemp);
            audioRecord.setOperator(operator);
            String ossKey = "";
            if (CallStatus.SUCCESS.getRemark().equals(audioTemp.getCallStatus())) {
                String url = "";
                if (ObjectUtil.isNotEmpty(dataList)) {
                    url = dataList.stream()
                            .filter(data -> audioTemp.getMobile().equals(data.getString("number"))
                                    && audioTemp.getStartTime().equals(data.getString("callBeginTime")))
                            .findFirst()
                            .orElse(new JSONObject()).getString("chatRecord");
                }

                if (StringUtil.isNotEmpty(url)) {
                    try {
                        File file = FileUtil.getFileFromNet(url);
                        boolean isArchive = false;
                        if (StringUtil.isNotEmpty(audioTemp.getStartTime())) {
                            if (!LocalDateTime.now().minusDays(1095l).isBefore(DateUtil.format(audioTemp.getStartTime(), null))) {
                                isArchive = true;
                            }
                        }
                        ossKey = ossService.uploadByDocServer(BizType.AUDIO_UPLOAD.getKey(), operator, file.getName(), new FileInputStream(file), isArchive);
                    } catch (Exception e) {
                        MessageInfo msg = new MessageInfo();
                        msg.setTitle("94录音拉取失败");
                        msg.setTopic("下载&上传录音失败");
                        msg.setContent("下载&上传文件失败，录音文件地址为：" + url + "  异常: " + e.getMessage());
                        MailUtil.send(msg, Arrays.asList("<EMAIL>"));
                        log.warn("下载&上传文件失败，ex: " + e.getMessage() + ", url: " +url);
                    }
                }
            }
            audioRecord.setUrl(ossKey);
            list.add(audioRecord);
            finishList.add(audioTemp);
        }

        if (ObjectUtil.isNotEmpty(list)) {
            saveRecord(list);
            this.audioTempService.updateStatus(finishList, ProcessStatus.FINISH.getStatus());
        }
    }

    private void save94AiRecordNew(List<AudioTemp> audioTemps) {
        if (ObjectUtil.isEmpty(audioTemps)) {
            return;
        }

        List<AudioRecord> list = new ArrayList<>();
        List<AudioTemp> finishList = new ArrayList<>();
        AudioRecord audioRecord;
        for (AudioTemp audioTemp : audioTemps) {
            String operator = (audioTemp.getCno() == null ? "" : audioTemp.getCno()) + (audioTemp.getStaffName() == null ? "" : audioTemp.getStaffName());
            audioRecord = buildAudioRecord(audioTemp);
            audioRecord.setOperator(operator);
            String ossKey = "";
            if (CallStatus.SUCCESS.getRemark().equals(audioTemp.getCallStatus())) {
                if (StringUtil.isNotEmpty(audioTemp.getRecordFile())) {
                    try {
                        File file = FileUtil.getFileFromNet(audioTemp.getRecordFile());
                        boolean isArchive = false;
                        if (StringUtil.isNotEmpty(audioTemp.getStartTime())) {
                            if (!LocalDateTime.now().minusDays(1095l).isBefore(DateUtil.format(audioTemp.getStartTime(), null))) {
                                isArchive = true;
                            }
                        }
                        ossKey = ossService.uploadByDocServer(BizType.AUDIO_UPLOAD.getKey(), operator, file.getName(), new FileInputStream(file), isArchive);
                    } catch (Exception e) {
                        MessageInfo msg = new MessageInfo();
                        msg.setTitle("94录音拉取失败");
                        msg.setTopic("下载&上传录音失败");
                        msg.setContent("下载&上传文件失败，录音文件地址为：" + audioTemp.getRecordFile() + "  异常: " + e.getMessage());
                        MailUtil.send(msg, Arrays.asList("<EMAIL>"));
                        log.warn("下载&上传文件失败，ex: " + e.getMessage() + ", url: " + audioTemp.getRecordFile());
                    }
                }
            }
            audioRecord.setUrl(ossKey);
            list.add(audioRecord);
            finishList.add(audioTemp);
        }

        if (ObjectUtil.isNotEmpty(list)) {
            saveRecord(list);
            this.audioTempService.updateStatus(finishList, ProcessStatus.FINISH.getStatus());
        }
    }

    private AudioRecord buildAudioRecord(AudioTemp audioTemp) {
        AudioRecord audioRecord = new AudioRecord();
        audioRecord.setDeptId(AudioHelper.getDeptIdByDeptName(audioTemp.getEnterpriseName(), audioTemp.getProvider()));
        audioRecord.setUploadId(-1);
        audioRecord.setPhone(audioTemp.getMobile());
        audioRecord.setUserid(getUserid(audioRecord.getPhone()));
        audioRecord.setCnid(getCnid(audioRecord.getUserid()));
        if (ObjectUtil.isEmpty(audioTemp.getStartTime())) {
            audioRecord.setStartTime(audioTemp.getQueryDate());
        } else {
            audioRecord.setStartTime(audioTemp.getStartTime());
        }
        audioRecord.setEndTime(audioTemp.getEndTime());
        audioRecord.setCallType(CallType.getValueByRemark(audioTemp.getCallType()));
        audioRecord.setUploadMethod(UploadMethod.INTERFACE.getValue());
        audioRecord.setStatus(ListenStatus.getValueByRemark(audioTemp.getCallStatus()));
        audioRecord.setCreateTime(DateUtil.getCurrentDateTime());
        audioRecord.setSupplier(SupplierType.getValueByKey(audioTemp.getProvider()));
        audioRecord.setDept(audioTemp.getEnterpriseName());
        audioRecord.setOrg(audioTemp.getGroupCode());
        String operator = (audioTemp.getCno() == null ? "" : audioTemp.getCno()) + (audioTemp.getStaffName() == null ? "" : audioTemp.getStaffName());
        audioRecord.setAgentId(operator);
        return audioRecord;
    }

    private void fix94AiRecord(String queryDate, String endDate, String type) {
        while (queryDate.compareTo(endDate) < 0) {
            log.info("fix 94Ai record: " + queryDate);
            String endTime = DateUtil.formatDate(DateUtil.formatDate(queryDate, DateUtil.FORMAT_SIMPLE_DATE).plusDays(1), DateUtil.FORMAT_SIMPLE_DATE);
            AudioRecordDto query = new AudioRecordDto();
            query.setDeptId(ConfigUtil.config.getCollection94DeptId());
            query.setStartTime(queryDate);
            query.setEndTime(endTime);
            query.setStatus(CallStatus.SUCCESS.getValue());
            List<AudioRecord> list = audioRecordDao.query94Empty(query);
            if (ObjectUtil.isNotEmpty(list)) {
                AudioRecord bean;
                List<JSONObject> dataList = new ArrayList<>();
                if ("day".equals(type)) {
                    dataList = AudioHelper.fix94AiData(queryDate, list);
                }
                for (AudioRecord audioRecord : list) {
                    if ("one".equals(type)) {
                        dataList = AudioHelper.fix94AiData(queryDate, Arrays.asList(audioRecord));
                    }
                    String ossKey = "";
                    String url = "";
                    if (ObjectUtil.isNotEmpty(dataList)) {
                        url = dataList.stream()
                                .filter(data -> audioRecord.getPhone().equals(data.getString("number"))
                                        && audioRecord.getStartTime().equals(data.getString("callBeginTime")))
                                .findFirst()
                                .orElse(new JSONObject()).getString("chatRecord");
                    }

                    if (StringUtil.isNotEmpty(url)) {
                        try {
                            File file = FileUtil.getFileFromNet(url);
                            boolean isArchive = false;
                            if (StringUtil.isNotEmpty(audioRecord.getStartTime())) {
                                if (!LocalDateTime.now().minusDays(1095l).isBefore(DateUtil.format(audioRecord.getStartTime(), null))) {
                                    isArchive = true;
                                }
                            }
                            ossKey = ossService.uploadByDocServer(BizType.AUDIO_UPLOAD.getKey(), audioRecord.getOperator(), file.getName(), new FileInputStream(file), isArchive);
                        } catch (Exception e) {
                            MessageInfo msg = new MessageInfo();
                            msg.setTitle("94录音拉取失败");
                            msg.setTopic("下载&上传录音失败");
                            msg.setContent("下载&上传文件失败，录音文件地址为：" + url + "  异常: " + e.getMessage());
                            MailUtil.send(msg, Arrays.asList("<EMAIL>"));
                            log.warn("下载&上传文件失败，ex: " + e.getMessage() + ", url: " + url);
                        }
                    }

                    if (StringUtil.isNotEmpty(ossKey)) {
                        bean = new AudioRecord();
                        bean.setId(audioRecord.getId());
                        bean.setUrl(ossKey);
                        audioRecordDao.update(bean);
                    }
                }
            }
            log.info("fix 94Ai record: " + queryDate + " success");
            queryDate = endTime;
        }
    }

    private void saveLBRecord(List<AudioTemp> audioTemps) {
        if (ObjectUtil.isEmpty(audioTemps)) {
            return;
        }

        List<AudioRecord> list = new ArrayList<>();
        List<AudioTemp> finishList = new ArrayList<>();
        AudioRecord audioRecord;
        for (AudioTemp audioTemp : audioTemps) {
            String operator = (audioTemp.getCno() == null ? "" : audioTemp.getCno()) + (audioTemp.getStaffName() == null ? "" : audioTemp.getStaffName());
            audioRecord = buildAudioRecord(audioTemp);
            audioRecord.setOperator(operator);
            String ossKey = "";
            if (CallStatus.SUCCESS.getRemark().equals(audioTemp.getCallStatus())) {
                String fileName = audioTemp.getRecordFile().substring(audioTemp.getRecordFile().lastIndexOf("/")) + ".mp3";
                try {
                    InputStream inputStream = AudioHelper.callLB(audioTemp.getRecordFile());
                    boolean isArchive = false;
                    if (StringUtil.isNotEmpty(audioTemp.getStartTime())) {
                        if (!LocalDateTime.now().minusDays(1095l).isBefore(DateUtil.format(audioTemp.getStartTime(), null))) {
                            isArchive = true;
                        }
                    }
                    if (inputStream != null) {
                        ossKey = ossService.uploadByDocServer(BizType.AUDIO_UPLOAD.getKey(), operator, fileName, inputStream, isArchive);
                    }
                } catch (Exception e) {
                    MessageInfo msg = new MessageInfo();
                    msg.setTitle("灵伴录音拉取失败");
                    msg.setTopic("下载&上传录音失败");
                    msg.setContent("下载&上传文件失败，录音文件地址为：" + audioTemp.getRecordFile() + "  异常: " + e.getMessage());
                    MailUtil.send(msg, Arrays.asList("<EMAIL>"));
                    log.warn("下载&上传文件失败，ex: " + e.getMessage() + ", url: " + audioTemp.getRecordFile());
                }

            }
            audioRecord.setUrl(ossKey);
            list.add(audioRecord);
            finishList.add(audioTemp);
        }

        if (ObjectUtil.isNotEmpty(list)) {
            saveRecord(list);
            this.audioTempService.updateStatus(finishList, ProcessStatus.FINISH.getStatus());
        }
    }

    public void saveRecord(List<AudioRecord> list) {
        if (ObjectUtil.isNotEmpty(list)) {
            if (list.size() > 500) {
                int number = list.size()%500 == 0 ? list.size()/500 : (list.size()/500)+1;
                for (int j=0; j<number; j++) {
                    if ((j + 1) * 500 > list.size()) {
                        this.audioRecordDao.insertBatch(list.subList(j * 500, list.size()));
                    } else {
                        this.audioRecordDao.insertBatch(list.subList(j * 500, (j + 1) * 500));
                    }
                }
            } else {
                this.audioRecordDao.insertBatch(list);
            }
        }
    }

    private Integer getUserid(String phone) {
        try {
            UserDTO userDTO = userServiceFacade.getUnblockedUserByMobile(phone);
            if (ObjectUtil.isNotEmpty(userDTO)) {
                Integer userid = userDTO.getId();
                if (userid != null) return userid;
            }
        } catch (Exception e) {
            log.error("获取userid失败，手机号：" + phone + "，ex:" + e.getMessage());
        }
        return -1;
    }

    private String getCnid(Integer userid) {
        try {
            if (userid.intValue() != -1) {
                Profile profile = profileService.getProfileByBorroweId(userid);
                if (ObjectUtil.isNotEmpty(profile)) {
                    String cnid = profile.getCnid();
                    if (StringUtil.isNotEmpty(cnid)) {
                        return cnid;
                    }
                }
            }
        }  catch (Exception e) {
            log.error("获取cnid失败，userid：" + userid + "，ex:" + e.getMessage());
        }
        return "";
    }

    @Override
    public void processTodayData() {
        String queryDate = DateUtil.format(LocalDateTime.now().minusDays(1), DateUtil.FORMAT_SIMPLE_DATE);
        queryData(queryDate);

        AudioTempDto query = new AudioTempDto();
        query.setQueryDate(queryDate);
        query.setStatus(ProcessStatus.DEFAULT.getStatus());
        List<AudioTemp> audioTemps = this.audioTempService.queryByCondition(query);
        if (ObjectUtil.isNotEmpty(audioTemps)) {
            processData(audioTemps);
        } else {
            AudioTemp audioTemp = new AudioTemp();
            audioTemp.setMobile("");
            audioTemp.setStatus(ProcessStatus.FINISH.getStatus());
            audioTemp.setQueryDate(queryDate);
            audioTemp.setCreateTime(DateUtil.getCurrentDateTime());
            this.audioTempService.insert(audioTemp);
        }
    }

    @Override
    public void processHistoryData() {
        String endDate = "2018-01-01";
        AudioTempDto query = new AudioTempDto();
        query.setSort("asc");
        query.setLimit(1);
        List<AudioTemp> audioTemps = this.audioTempService.queryByCondition(query);
        if (ObjectUtil.isNotEmpty(audioTemps)) {
            String queryDate = audioTemps.get(0).getQueryDate();
            if (endDate.equals(queryDate)) {
                log.info("历史数据已全部拉取，日期: " + queryDate + ", 停止拉取");
                return;
            }

            query = new AudioTempDto();
            query.setQueryDate(queryDate);
            query.setStatus(ProcessStatus.DEFAULT.getStatus());
            audioTemps = this.audioTempService.queryByCondition(query);
            try {
                if (ObjectUtil.isNotEmpty(audioTemps)) {
                    processData(audioTemps);
                } else {
                    queryDate = DateUtil.formatDate(DateUtil.formatDate(queryDate, DateUtil.FORMAT_SIMPLE_DATE).minusDays(1), DateUtil.FORMAT_SIMPLE_DATE);
                    queryData(queryDate);
                    query.setQueryDate(queryDate);
                    audioTemps = this.audioTempService.queryByCondition(query);
                    if (ObjectUtil.isNotEmpty(audioTemps)) {
                        processData(audioTemps);
                    } else {
                        AudioTemp audioTemp = new AudioTemp();
                        audioTemp.setMobile("");
                        audioTemp.setStatus(ProcessStatus.FINISH.getStatus());
                        audioTemp.setQueryDate(queryDate);
                        audioTemp.setCreateTime(DateUtil.getCurrentDateTime());
                        this.audioTempService.insert(audioTemp);
                    }
                }
            } catch (Exception e) {
                log.error("历史数据处理异常，跳过当日数据，日期: " + queryDate);
                audioTempService.updateStatus(queryDate, ProcessStatus.FINISH.getStatus());
            }
        }
    }

    public void processCtiData(String startDate, String endDate, String enterpriseId) {
        log.info("天润数据处理开始，范围: " + startDate + " - " + endDate);
        String queryDate = startDate;
        while (!endDate.equals(queryDate)) {
            try {
                log.info("开始处理日期: " + queryDate);
                AudioTempDto query = new AudioTempDto();
                query.setQueryDate(queryDate);
                query.setLimit(1);
                List<AudioTemp> audioTemps = this.audioTempService.queryByCondition(query);
                if (ObjectUtil.isEmpty(audioTemps)) {
                    queryData(queryDate);
                    audioTemps = this.audioTempService.queryByCondition(query);
                    if (ObjectUtil.isEmpty(audioTemps)) {
                        AudioTemp audioTemp = new AudioTemp();
                        audioTemp.setMobile("");
                        audioTemp.setStatus(ProcessStatus.FINISH.getStatus());
                        audioTemp.setQueryDate(queryDate);
                        audioTemp.setCreateTime(DateUtil.getCurrentDateTime());
                        this.audioTempService.insert(audioTemp);
                    }
                }

                query = new AudioTempDto();
                query.setQueryDate(queryDate);
                if (StringUtil.isNotEmpty(enterpriseId)) {
                    query.setEnterpriseId(enterpriseId);
                }
                query.setProvider(SupplierType.ST_CTI.getKey());
                query.setStatus(ProcessStatus.DEFAULT.getStatus());
                audioTemps = this.audioTempService.queryByCondition(query);

                if (ObjectUtil.isNotEmpty(audioTemps)) {
                    processData(audioTemps);
                }
                log.info("结束处理日期: " + JSONObject.toJSONString(query));
            } catch (Exception e) {
                log.error("天润数据处理异常，跳过当日数据，日期: " + queryDate);
                audioTempService.updateStatus(queryDate, ProcessStatus.FINISH.getStatus());
            }
            queryDate = DateUtil.formatDate(DateUtil.formatDate(queryDate, DateUtil.FORMAT_SIMPLE_DATE).plusDays(1), DateUtil.FORMAT_SIMPLE_DATE);
        }

        log.info("天润数据处理已结束，范围: " + startDate + " - " + endDate);
    }

    private void pullCtiData(QueryVo queryVo) {
        processWebCall(queryVo);
        processLd(queryVo);
        processWh(queryVo);
        processPreWh(queryVo);
    }

    private void processWebCall(QueryVo queryVo) {
        log.info("开始拉取webcall数据：" + JSONObject.toJSONString(queryVo));
        String url = "https://api-region.cticloud.cn/interface/v10/cdr/webcall/copy";
        String field = "dbWebcallCdrs";
        long id = 0;
        JSONArray dataArr;
        do {
            dataArr = AudioHelper.pullCtiRecord(url, queryVo.getRegion(), queryVo.getEnterpriseId(),
                    queryVo.getToken(), queryVo.getMonth(), id, field);
            if (!dataArr.isEmpty()) {
                List<CollectionRecordVo> list = new ArrayList<>();
                for (int i=0; i<dataArr.size(); i++) {
                    CollectionRecordVo collectionRecordVo = AudioHelper.buildCollectionRecordVo(dataArr.getJSONObject(i), queryVo.getToken(),
                            queryVo.getEnterpriseId(), queryVo.getEnterpriseName(), queryVo.getRegion());
                    if (collectionRecordVo != null) {
                        collectionRecordVo.setCallType(CallType.WEB_CALL.getRemark());
                        list.add(collectionRecordVo);
                    }
                }

                if (list.size() > 0) {
                    Map<String, List<CollectionRecordVo>> crMap = list.parallelStream().collect(Collectors.groupingBy(e -> (e.getQueryDate())));
                    for (String queryDate : crMap.keySet()) {
                        List<CollectionRecordVo> crList = crMap.get(queryDate);
                        if (ObjectUtil.isNotEmpty(crList)) {
                            log.info("保存webcall数据，日期：" + queryDate);
                            audioTempService.saveCSAndCollectionData(crList, queryDate);
                        }
                    }
                }
                id = dataArr.getJSONObject(dataArr.size()-1).getLongValue("id");
            }
        } while (!dataArr.isEmpty());
        log.info("拉取webcall数据结束：" + JSONObject.toJSONString(queryVo));
    }

    private void processLd(QueryVo queryVo) {
        log.info("开始拉取来电数据：" + JSONObject.toJSONString(queryVo));
        String url = "https://api-region.cticloud.cn/interface/v10/cdr/ib/copy";
        String field = "dbIbCdrs";
        long id = 0;
        JSONArray dataArr;
        do {
            dataArr = AudioHelper.pullCtiRecord(url, queryVo.getRegion(), queryVo.getEnterpriseId(),
                    queryVo.getToken(), queryVo.getMonth(), id, field);
            if (!dataArr.isEmpty()) {
                List<CollectionRecordVo> list = new ArrayList<>();
                for (int i=0; i<dataArr.size(); i++) {
                    CollectionRecordVo collectionRecordVo = AudioHelper.buildCollectionRecordVo(dataArr.getJSONObject(i), queryVo.getToken(),
                            queryVo.getEnterpriseId(), queryVo.getEnterpriseName(), queryVo.getRegion());
                    if (collectionRecordVo != null) {
                        collectionRecordVo.setCallType(CallType.LISTEN.getRemark());
                        list.add(collectionRecordVo);
                    }
                }

                if (list.size() > 0) {
                    Map<String, List<CollectionRecordVo>> crMap = list.parallelStream().collect(Collectors.groupingBy(e -> (e.getQueryDate())));
                    for (String queryDate : crMap.keySet()) {
                        List<CollectionRecordVo> crList = crMap.get(queryDate);
                        if (ObjectUtil.isNotEmpty(crList)) {
                            log.info("保存来电数据，日期：" + queryDate);
                            audioTempService.saveCSAndCollectionData(crList, queryDate);
                        }
                    }
                }
                id = dataArr.getJSONObject(dataArr.size()-1).getLongValue("id");
            }
        } while (!dataArr.isEmpty());
        log.info("拉取来电数据结束：" + JSONObject.toJSONString(queryVo));
    }

    private void processWh(QueryVo queryVo) {
        log.info("开始拉取外呼数据：" + JSONObject.toJSONString(queryVo));
        String url = "https://api-region.cticloud.cn/interface/v10/cdr/ob/copy";
        String field = "dbObCdrs";
        long id = 0;
        JSONArray dataArr;
        do {
            dataArr = AudioHelper.pullCtiRecord(url, queryVo.getRegion(), queryVo.getEnterpriseId(),
                    queryVo.getToken(), queryVo.getMonth(), id, field);
            if (!dataArr.isEmpty()) {
                List<CollectionRecordVo> list = new ArrayList<>();
                for (int i=0; i<dataArr.size(); i++) {
                    CollectionRecordVo collectionRecordVo = AudioHelper.buildCollectionRecordVo(dataArr.getJSONObject(i), queryVo.getToken(),
                            queryVo.getEnterpriseId(), queryVo.getEnterpriseName(), queryVo.getRegion());
                    if (collectionRecordVo != null) {
                        collectionRecordVo.setCallType(CallType.CALL.getRemark());
                        list.add(collectionRecordVo);
                    }
                }

                if (list.size() > 0) {
                    Map<String, List<CollectionRecordVo>> crMap = list.parallelStream().collect(Collectors.groupingBy(e -> (e.getQueryDate())));
                    for (String queryDate : crMap.keySet()) {
                        List<CollectionRecordVo> crList = crMap.get(queryDate);
                        if (ObjectUtil.isNotEmpty(crList)) {
                            log.info("保存外呼数据，日期：" + queryDate);
                            audioTempService.saveCSAndCollectionData(crList, queryDate);
                        }
                    }
                }
                id = dataArr.getJSONObject(dataArr.size()-1).getLongValue("id");
            }
        } while (!dataArr.isEmpty());
        log.info("拉取外呼数据结束：" + JSONObject.toJSONString(queryVo));
    }

    private void processPreWh(QueryVo queryVo) {
        log.info("开始拉取预测式外呼数据：" + JSONObject.toJSONString(queryVo));
        String url = "https://api-region.cticloud.cn/interface/v10/cdr/predictiveCall/copy";
        String field = "dbPredictiveCallCdrs";
        long id = 0;
        JSONArray dataArr;
        do {
            dataArr = AudioHelper.pullCtiRecord(url, queryVo.getRegion(), queryVo.getEnterpriseId(),
                    queryVo.getToken(), queryVo.getMonth(), id, field);
            if (!dataArr.isEmpty()) {
                List<CollectionRecordVo> list = new ArrayList<>();
                for (int i=0; i<dataArr.size(); i++) {
                    CollectionRecordVo collectionRecordVo = AudioHelper.buildCollectionRecordVo(dataArr.getJSONObject(i), queryVo.getToken(),
                            queryVo.getEnterpriseId(), queryVo.getEnterpriseName(), queryVo.getRegion());
                    if (collectionRecordVo != null) {
                        collectionRecordVo.setCallType(CallType.CALL.getRemark());
                        list.add(collectionRecordVo);
                    }
                }

                if (list.size() > 0) {
                    Map<String, List<CollectionRecordVo>> crMap = list.parallelStream().collect(Collectors.groupingBy(e -> (e.getQueryDate())));
                    for (String queryDate : crMap.keySet()) {
                        List<CollectionRecordVo> crList = crMap.get(queryDate);
                        if (ObjectUtil.isNotEmpty(crList)) {
                            log.info("保存预测式外呼数据，日期：" + queryDate);
                            audioTempService.saveCSAndCollectionData(crList, queryDate);
                        }
                    }
                }
                id = dataArr.getJSONObject(dataArr.size()-1).getLongValue("id");
            }
        } while (!dataArr.isEmpty());
        log.info("拉取预测式外呼数据结束：" + JSONObject.toJSONString(queryVo));
    }

    private void queryData(String queryDate) {
        log.info("拉取数据开始，日期: " + queryDate);
        //拉取审批当天数据
        List<VoiceSendDetail> approvalList = audioHelpService.callApprovalRecord(queryDate);
        audioTempService.saveApprovalData(approvalList, queryDate);
        //拉取反欺诈当天数据
        List<VoiceSendVo> fraudList = audioHelpService.callFraudRecord(queryDate);
        audioTempService.saveFraudData(fraudList, queryDate);
        //拉取客服当天数据
        List<CollectionRecordVo> csList = AudioHelper.callCSRecord(queryDate);
        audioTempService.saveCSAndCollectionData(csList, queryDate);
        //拉取催收当天数据
        List<CollectionRecordVo> collectionList = AudioHelper.callCollectionRecord(queryDate);
        audioTempService.saveCSAndCollectionData(collectionList, queryDate);
        log.info("拉取数据结束，日期: " + queryDate);
    }

    private void processData(List<AudioTemp> audioTemps) {
        if (ObjectUtil.isNotEmpty(audioTemps)) {
            log.info("处理数据开始，日期: " + audioTemps.get(0).getQueryDate());
            List<AudioTemp> ctiAudioTemps = audioTemps.stream()
                    .filter(audioTemp -> SupplierType.ST_CTI.getKey().equals(audioTemp.getProvider()))
                    .filter(audioTemp -> StringUtil.isNotEmpty(audioTemp.getEnterpriseId())
                            && StringUtil.isNotEmpty(audioTemp.getSign())
                            && StringUtil.isNotEmpty(audioTemp.getRegion()))
                    .collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(ctiAudioTemps)) {
                Map<String, List<AudioTemp>> audioTempMap = ctiAudioTemps.parallelStream().collect(Collectors.groupingBy(e -> (e.getEnterpriseId())));
                for (String key : audioTempMap.keySet()) {
                    List<AudioTemp> audioTempList = audioTempMap.get(key);
                    if (ObjectUtil.isNotEmpty(audioTempList)) {
                        JSONObject data = AudioHelper.processCtiData(audioTempList);
                        saveCtiRecord(audioTempList, data);
                    }
                }
            }

            List<AudioTemp> ctiNullAudioTemps = audioTemps.stream()
                    .filter(audioTemp -> SupplierType.ST_CTI.getKey().equals(audioTemp.getProvider()))
                    .filter(audioTemp -> StringUtil.isEmpty(audioTemp.getEnterpriseId())
                            || StringUtil.isEmpty(audioTemp.getSign())
                            || StringUtil.isEmpty(audioTemp.getRegion()))
                    .collect(Collectors.toList());
            saveCtiRecord(ctiNullAudioTemps, null);


            List<AudioTemp> ai94AudioTemps = audioTemps.stream()
                    .filter(audioTemp -> SupplierType.ST_94AI.getKey().equals(audioTemp.getProvider()))
                    .collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(ai94AudioTemps)) {
                List<AudioTemp> ai94UrlList = ai94AudioTemps.stream()
                        .filter(audioTemp -> StringUtil.isNotEmpty(audioTemp.getRecordFile()))
                        .collect(Collectors.toList());
                List<AudioTemp> ai94EmptyList = ai94AudioTemps.stream()
                        .filter(audioTemp -> StringUtil.isEmpty(audioTemp.getRecordFile()))
                        .collect(Collectors.toList());
                //催收客服系统直接返回94的录音地址以后，可以通过录音地址下载文件
                if (ObjectUtil.isNotEmpty(ai94UrlList)) {
                    save94AiRecordNew(ai94UrlList);
                }
                //没有录音地址的继续走以前的逻辑
                if (ObjectUtil.isNotEmpty(ai94EmptyList)) {
                    List<JSONObject> list = AudioHelper.process94AiData(ai94EmptyList);
                    save94AiRecord(ai94EmptyList, list);
                }
            }

            List<AudioTemp> lbAudioTemps = audioTemps.stream()
                    .filter(audioTemp -> SupplierType.ST_LB.getKey().equals(audioTemp.getProvider()))
                    .collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(lbAudioTemps)) {
                saveLBRecord(lbAudioTemps);
            }

            log.info("处理数据结束，日期: " + audioTemps.get(0).getQueryDate());
        }
    }

    public void processExistsAmr() {
        List<AudioRecord> list = this.audioRecordDao.queryUploadAudio(null);
        if (ObjectUtil.isNotEmpty(list)) {
            list = list.stream()
                    .filter(ar -> StringUtil.isNotEmpty(ar.getUrl()) && ar.getUrl().endsWith(".amr"))
                    .collect(Collectors.toList());
            list.forEach(ar -> {
                if (!LocalDateTime.now().minusDays(1095l).isBefore(DateUtil.format(ar.getStartTime(), null))) {
                    ossService.restoreByDocServer(ar.getUrl());
                }
                String url = ossService.getOssUrlByDocServer(ar.getUrl(), 3600 * 1000 * 2l);

                if (StringUtil.isNotEmpty(url)) {
                    try {
                        File amrFile = FileUtil.getFileFromNet(url);
                        FileVo fileVo = new FileVo();
                        fileVo.setFileName(amrFile.getName());
                        fileVo.setInputStream(new FileInputStream(amrFile));
                        AudioHelper.changeAmrToMp3(fileVo);
                        String ossKey = ossService.uploadByDocServer(BizType.AUDIO_UPLOAD.getKey(), ar.getOperator(), fileVo.getFileName(), fileVo.getInputStream(), false);
                        fileVo.getInputStream().close();
                        AudioHelper.deleteTempFile(fileVo.getFileName());
                        ar.setUrl(ossKey);
                        this.audioRecordDao.update(ar);
                    } catch (Exception e) {
                        log.error("download amr file error:" + e.getMessage());
                    }
                }
            });
        }
    }

    public void processNullFile() {
        List<AudioTemp> audioTempList = audioTempDao.listNullFile();
        if (ObjectUtil.isNotEmpty(audioTempList)) {
            audioTempList.forEach(v -> {
                String uniqueId = AudioHelper.callCSUniqueId(v.getMobile(), v.getStartTime());
                if (StringUtil.isNotEmpty(uniqueId)) {
                    String fileName = AudioHelper.getNullFileName(v.getRegion(), v.getEnterpriseId(), v.getSign(), uniqueId);
                    v.setRecordFile(fileName);
//                audioTempDao.update(v);
                }
            });
//            JSONObject data = AudioHelper.processCtiData(audioTempList);
//            saveCtiRecord(audioTempList, data);
        }
    }

    /**
     * 导出筛选数据
     *
     * @param dto 导出条件
     * @return ResultCode
     */
    @Override
    public void export(AudioRecordDto dto) {
        String currentTime = DateUtil.getCurrentDateTime();
        List<Integer> deptIds = getDeptList(dto.getDeptId());
        if (ObjectUtil.isNotEmpty(deptIds)) {
            dto.setDeptIds(deptIds);
            List<AudioRecord> list = this.audioRecordDao.queryByCondition(dto);
            if (ObjectUtil.isNotEmpty(list)) {
                list.stream()
                        .forEach(v -> {
                            v.setPhone(v.getPhone().replace(v.getPhone().substring(3, 7), "****"));
                            v.setExportTime(currentTime);
                            v.setListenedRemark(ListenedRemark.getRemarkByValue(v.getListened()));
                        });
            }

            try {
                ExcelWriter writer;
                if (ObjectUtil.isEmpty(list)) {
                    writer = ExcelUtil.exportEmptyBuilder(AudioRecord.class);
                } else {
                    writer = ExcelUtil.exportBuilder(list);
                }
                ExcelUtil.export(response, "result.xls", writer.getWorkbook());
            } catch (IntrospectionException e) {
                log.error(getOperatorName() + " export error, param:" + dto +", ex:" + e.getMessage());
            }
        }
    }

    @Override
    protected void saveOperationHistory(Integer id, String operation) {

    }

    @Override
    protected void operateAuditData(Integer id, String operation) {
        AudioApply audioApply = this.audioApplyDao.getById(id);
        CallbackParam callbackParam = new CallbackParam();
        callbackParam.setBusinessIds(Arrays.asList(id.toString()));
        callbackParam.setAction(AuditStatus.APPROVED.getAction());
        callbackParam.setAuditConfigCode(ApproveCode.AUDIO_RECORD_DOWNLOAD.getApproveCode());
        this.audioApplyService.process(audioApply, callbackParam);
    }
}
