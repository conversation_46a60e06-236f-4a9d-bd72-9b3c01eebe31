package com.welab.databridge.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.welab.databridge.common.ResultCode;
import com.welab.databridge.dao.AudioFailDao;
import com.welab.databridge.dto.AudioFailDto;
import com.welab.databridge.entity.AudioFail;
import com.welab.databridge.service.AudioFailService;
import com.welab.databridge.service.impl.common.BaseServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 音频上传失败记录记录(AudioFail)表服务实现类
 * (代码生成器自动生成)
 *
 * <AUTHOR>
 * @date 2023-01-03 14:29:59
 */
@Service("audioFailService")
public class AudioFailServiceImpl extends BaseServiceImpl implements AudioFailService {
    @Resource
    private AudioFailDao audioFailDao;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public AudioFail getById(Integer id) {
        return this.audioFailDao.getById(id);
    }

    /**
     * 列表条件查询
     *
     * @param dto
     * @return
     */
    @Override
    public List<AudioFail> queryByCondition(AudioFailDto dto) {
        List<AudioFail> list = new ArrayList<>();
        List<Integer> deptIds = getDeptList(dto.getDeptId());
        if (ObjectUtil.isNotEmpty(deptIds)) {
            dto.setDeptIds(deptIds);
            list = this.audioFailDao.queryByCondition(dto);
        }
        return list;
    }

    /**
     * 新增数据
     *
     * @param dto 转换对象
     */
    @Override
    public ResultCode insert(AudioFailDto dto) {
        AudioFail bean = dto.toAddBean();
        this.audioFailDao.insert(bean);
        return ResultCode.SUCCESS;
    }

    /**
     * 修改数据
     *
     * @param dto 转换对象
     */
    @Override
    public ResultCode update(AudioFailDto dto) {
        AudioFail bean = this.audioFailDao.getById(dto.getId());
        if (null == bean) {
            return ResultCode.DATA_NOT_EXIST;
        }
        AudioFail updateBean = dto.toUpdateBean();
        updateBean.setId(bean.getId());
        this.audioFailDao.update(updateBean);
        return ResultCode.SUCCESS;
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return ResultCode
     */
    @Override
    public ResultCode deleteById(Integer id) {
        boolean bool = this.audioFailDao.deleteById(id);
        if (bool) {
            return ResultCode.SUCCESS;
        } else {
            return ResultCode.FAILED;
        }
    }

    @Override
    protected void saveOperationHistory(Integer id, String operation) {

    }

    @Override
    protected void operateAuditData(Integer id, String operation) {

    }
}
