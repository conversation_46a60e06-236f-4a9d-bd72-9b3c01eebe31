package com.welab.databridge.service.impl.common;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.welab.databridge.annotation.AuditShowField;
import com.welab.databridge.common.ResponseVo;
import com.welab.databridge.common.ResultCode;
import com.welab.databridge.dao.UserDeptDao;
import com.welab.databridge.dto.DataHistoryDto;
import com.welab.databridge.dto.UserDeptDto;
import com.welab.databridge.entity.UserDept;
import com.welab.databridge.enums.ApproveCode;
import com.welab.databridge.enums.Operation;
import com.welab.databridge.service.DataHistoryService;
import com.welab.databridge.service.DepartmentService;
import com.welab.databridge.vo.audit.ApproveRequest;
import com.welab.databridge.vo.audit.ApproveResponse;
import com.welab.databridge.vo.audit.AuditParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.ReflectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.beans.IntrospectionException;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;

@Slf4j
public abstract class BaseServiceImpl<T> {
    @Resource
    protected HttpServletRequest request;
    @Resource
    protected HttpServletResponse response;
    @Resource
    private UserDeptDao userDeptDao;
    @Resource
    private DepartmentService departmentService;
    @Resource
    private DataHistoryService dataHistoryService;


    public String getOperatorId() {
        return request.getHeader("x-user-id");
    }

    public String getOperatorName() {
        return getOperatorNameById(getOperatorId());
    }

    public String getOperatorNameById(String operatorId) {
        UserDeptDto userDeptDto = new UserDeptDto();
        userDeptDto.setUserid(operatorId);
        List<UserDept> userDepts = userDeptDao.queryByCondition(userDeptDto);
        if (ObjectUtil.isNotEmpty(userDepts)) {
            return userDepts.get(0).getName();
        }
        return request.getHeader("x-user-name");
    }

    public String getUserToken() {
        return request.getHeader("x-user-token");
    }

    /**
     * 查询指定部门及子部门ID列表
     *
     * @param deptId 指定部门ID（前提用户有权限）
     *
     * @return deptId及子部门ID列表，deptId为空时返回当前用户所有部门及子部门
     */
    public List<Integer> getDeptList(Integer deptId) {
        List<Integer> idList = getDeptByOperatorId(getOperatorId());

        if (deptId != null) {
            if (idList.contains(deptId)) {
                idList.clear();
                idList.add(deptId);
                departmentService.listChildrenByParent(idList, deptId);
            } else {
                idList.clear();
            }
        }
        return idList;
    }

    protected List<Integer> getDeptByOperator(String operator) {
        List<Integer> parentIdList = new ArrayList<>();
        UserDeptDto userDeptDto = new UserDeptDto();
        userDeptDto.setName(operator);
        List<UserDept> userDepts = userDeptDao.queryByCondition(userDeptDto);
        if (userDepts != null) {
            userDepts.forEach(v -> parentIdList.add(v.getDeptId()));
        }

        List<Integer> idList = new ArrayList<>();
        for (Integer parentId : parentIdList) {
            idList.add(parentId);
            departmentService.listChildrenByParent(idList, parentId);
        }
        return idList;
    }

    protected List<Integer> getDeptByOperatorId(String operatorId) {
        List<Integer> parentIdList = new ArrayList<>();
        UserDeptDto userDeptDto = new UserDeptDto();
        userDeptDto.setUserid(operatorId);
        List<UserDept> userDepts = userDeptDao.queryByCondition(userDeptDto);
        if (userDepts != null) {
            userDepts.forEach(v -> parentIdList.add(v.getDeptId()));
        }

        List<Integer> idList = new ArrayList<>();
        for (Integer parentId : parentIdList) {
            idList.add(parentId);
            departmentService.listChildrenByParent(idList, parentId);
        }
        return idList;
    }

    protected ResponseVo applyAudit(List<T> list, String approveCode, String releaseOperation, String historyOperation, List<String> conditionKeys) {
        List<String> businessIds = new ArrayList<>();
        try {
            for (T l : list) {
                PropertyDescriptor pd = null;
                pd = new PropertyDescriptor("id", l.getClass());
                Method getMethod = pd.getReadMethod();//获得get方法
                String businessId = Optional.ofNullable(ReflectionUtils.invokeMethod(getMethod, l)).orElse("0").toString();
                businessIds.add(businessId);
            }
            log.info("businessIds:" + businessIds);
            Map<String, String> header = new HashMap<>();
            header.put("x-user-token", getUserToken());
            log.info("token:" + getUserToken());
            ApproveResponse approveResponse = ApproveRequest.create(approveCode)
                    .businessIds(businessIds)
                    .param(buildParam(list, approveCode, conditionKeys, historyOperation))
                    .headers(header)
                    .executeSubmit();
            if (approveResponse.getData().isNeedAudit()) {//需要审核
                if (!approveResponse.isSuccess()) {//申请失败回滚，申请成功通过审核模块SQL更改审核状态
                    // 手动回滚MySQL
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return ResponseVo.error(ResultCode.FAILED);
                } else {
                    String operation = "";
                    if (Operation.ONLINE.getType().equals(releaseOperation)) {
                        operation = Operation.ONLINE_APPLY.getType();
                    } else if (Operation.OFFLINE.getType().equals(releaseOperation)){
                        operation = Operation.OFFLINE_APPLY.getType();
                    } else if (Operation.DELETE.getType().equals(releaseOperation)){
                        operation = Operation.DELETE_APPLY.getType();
                    } else if (Operation.DOWNLOAD.getType().equals(releaseOperation)){
                        operation = Operation.DOWNLOAD_APPLY.getType();
                    }
                    for (String businessId : businessIds) {
                        saveOperationHistory(Integer.parseInt(businessId), operation);
                    }
                }
            } else {//不需要审核则直接上下架
                for (String businessId : businessIds) {
                    saveOperationHistory(Integer.parseInt(businessId), historyOperation);
                    operateAuditData(Integer.parseInt(businessId), releaseOperation);
                }
            }
            return ResponseVo.success();
        } catch (Exception e) {
            log.error("提交审核失败，ex:" + e.getMessage());
            return ResponseVo.error(ResultCode.FAILED);
        }
    }

    /**
     *
     * @param list 提交审核的数据列表
     * @param approveCode 审核编码
     * @param conditionKeys 审核模块查询条件字段
     * @param operation 操作类型
     * @return
     * @throws IntrospectionException
     */
    protected JSONObject buildParam(List<T> list, String approveCode, List<String> conditionKeys, String operation) throws Exception {
        AuditParam param = new AuditParam();
        JSONObject columns = new JSONObject();
        JSONObject details = new JSONObject();
        Field[] fields = list.get(0).getClass().getDeclaredFields();
        for (T obj : list) {
            PropertyDescriptor pd = new PropertyDescriptor("id", obj.getClass());
            Method getMethod = pd.getReadMethod();//获得get方法
            String id = Optional.ofNullable(ReflectionUtils.invokeMethod(getMethod, obj)).orElse("0").toString();
            log.info("id:" + id);

            List<AuditParam.Column> columnList = new ArrayList<>();
            for (String conditionKey : conditionKeys) {
                log.info("conditionKey:" + conditionKey);
                pd = new PropertyDescriptor(conditionKey, obj.getClass());
                getMethod = pd.getReadMethod();
                String conditionValue = ReflectionUtils.invokeMethod(getMethod, obj) == null ? ("" + System.currentTimeMillis())
                        : ReflectionUtils.invokeMethod(getMethod, obj).toString();
                log.info("conditionValue:" + conditionValue);

                AuditParam.Column column = param.new Column();
                column.setKey(conditionKey);
                column.setValue(conditionValue);
                columnList.add(column);
                columns.put(id, columnList);
            }
            List<AuditParam.TabContent> tabContentList = new ArrayList<>();
            for (Field field : fields) {
                if (field.isAnnotationPresent(AuditShowField.class)) {
                    String type = field.getAnnotation(AuditShowField.class).type();
                    AuditParam.TabContent tabContent = param.new TabContent();
                    tabContent.setLabel(field.getAnnotation(AuditShowField.class).name());
                    pd = new PropertyDescriptor(field.getName(), obj.getClass());
                    getMethod = pd.getReadMethod();
                    Object fieldValue = Optional.ofNullable(ReflectionUtils.invokeMethod(getMethod, obj)).orElse(new ArrayList<>());

                    if ("table".equals(type)) {
                        List<Object> tableList = (List<Object>) fieldValue;
                        Field[] tableColumns = tableList.get(0).getClass().getDeclaredFields();
                        List<AuditParam.TableColumn> tableColumnList = new ArrayList<>();
                        for (Field tableColumn : tableColumns) {
                            AuditParam.TableColumn column = param.new TableColumn();
                            column.setKey(tableColumn.getName());
                            column.setTitle(tableColumn.getAnnotation(AuditShowField.class).name());
                            tableColumnList.add(column);
                        }
                        tabContent.setTableColumns(tableColumnList);
                    }

                    tabContent.setValue(fieldValue);
                    tabContent.setType(type);
                    tabContentList.add(tabContent);
                }
            }

            AuditParam.Detail detail = param.new Detail();
            detail.setTabName(ApproveCode.getDescByCode(approveCode));
            detail.setTabTitle(ApproveCode.getModuleByCode(approveCode) + "-" + Operation.getDescByType(operation));
            detail.setTabContent(tabContentList);
            details.put(id, detail);
        }
        param.setColumns(columns);
        param.setDetail(details);
        return JSON.parseObject(JSON.toJSONString(param));
    }

    protected void saveHistoryVersion(Integer bizId, String bizType, String operation, JSONObject content) {
        DataHistoryDto dto = new DataHistoryDto();
        dto.setBizType(bizType);
        dto.setBizId(bizId);
        dto.setOperation(operation);
        dto.setVersion("");
        dto.setContent(content);
        dataHistoryService.insert(dto);
    }

    protected abstract void saveOperationHistory(Integer id, String operation);

    protected abstract void operateAuditData(Integer id, String operation);
}
