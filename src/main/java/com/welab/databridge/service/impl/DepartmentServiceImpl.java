package com.welab.databridge.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.welab.databridge.common.ResponseVo;
import com.welab.databridge.dao.AudioRecordDao;
import com.welab.databridge.dao.MessageRecordDao;
import com.welab.databridge.dao.UserDeptDao;
import com.welab.databridge.dto.DepartmentDto;
import com.welab.databridge.dto.UserDeptDto;
import com.welab.databridge.entity.AudioRecord;
import com.welab.databridge.entity.Department;
import com.welab.databridge.dao.DepartmentDao;
import com.welab.databridge.entity.MessageRecord;
import com.welab.databridge.entity.UserDept;
import com.welab.databridge.service.DepartmentService;
import com.welab.databridge.service.impl.common.BaseServiceImpl;
import com.welab.databridge.util.DateUtil;
import com.welab.databridge.util.StringUtil;
import com.welab.databridge.vo.dept.DeptVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.welab.databridge.common.ResultCode;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 部门表(Department)表服务实现类
 * (代码生成器自动生成)
 *
 * <AUTHOR>
 * @date 2023-01-03 14:29:46
 */
@Slf4j
@Service("departmentService")
public class DepartmentServiceImpl extends BaseServiceImpl implements DepartmentService {
    @Resource
    private DepartmentDao departmentDao;
    @Resource
    private UserDeptDao userDeptDao;
    @Resource
    private AudioRecordDao audioRecordDao;
    @Resource
    private MessageRecordDao messageRecordDao;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public Department getById(Integer id) {
        return this.departmentDao.getById(id);
    }

    /**
     * 列表条件查询
     *
     * @param dto
     * @return
     */
    @Override
    public List<Department> queryByCondition(DepartmentDto dto) {
        List<Department> list = new ArrayList<>();
        List<Department> departments = this.departmentDao.queryByCondition(dto);
        if (ObjectUtil.isNotEmpty(departments)) {
            departments.forEach(department -> {
                if (department.getParentId().intValue() == 0) {
                    list.add(department);
                    setChildren(departments, department);
                } else {
                    setChildren(departments, department);
                }
            });
        }
        return list;
    }

    private void setChildren(List<Department> departments, Department dept) {
        List<Department> list = departments.stream()
                .filter(department -> department.getParentId().intValue() == dept.getId().intValue())
                .collect(Collectors.toList());
        dept.setChildren(list);
    }

    /**
     * 新增数据
     *
     * @param dto 转换对象
     */
    @Override
    public ResponseVo insert(DepartmentDto dto) {
        DepartmentDto query = new DepartmentDto();
        query.setName(dto.getName());
        List<Department> list = this.departmentDao.queryByCondition(query);
        if (ObjectUtil.isNotEmpty(list)) {
            return ResponseVo.error(ResultCode.DATA_EXIST);
        }

        Department bean = dto.toAddBean();
        bean.setOperator(getOperatorName());
        this.departmentDao.insert(bean);
        return ResponseVo.success();
    }

    /**
     * 修改数据
     *
     * @param dto 转换对象
     */
    @Override
    public ResponseVo update(DepartmentDto dto) {
        Department bean = this.departmentDao.getById(dto.getId());
        if (null == bean) {
            return ResponseVo.error(ResultCode.DATA_NOT_EXIST);
        }

        DepartmentDto query = new DepartmentDto();
        query.setName(dto.getName());
        List<Department> list = this.departmentDao.queryByCondition(query);
        if (ObjectUtil.isNotEmpty(list)) {
            boolean existFlag = list.stream().anyMatch(v -> v.getId().intValue() != bean.getId());
            if (existFlag) {
                return ResponseVo.error(ResultCode.DATA_EXIST);
            }
        }

        List<Integer> childrenIds = new ArrayList<>();
        listChildrenByParent(childrenIds, bean.getId());
        if (childrenIds.contains(dto.getParentId())) {
            return ResponseVo.error(ResultCode.PARENT_DEPARTMENT_ERROR);
        }

        Department updateBean = dto.toUpdateBean();
        updateBean.setId(bean.getId());
        updateBean.setOperator(getOperatorName());
        this.departmentDao.update(updateBean);
        return ResponseVo.success();
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return ResultCode
     */
    @Override
    public ResponseVo deleteById(Integer id) {
        Department bean = this.departmentDao.getById(id);
        if (null == bean) {
            return ResponseVo.error(ResultCode.DATA_NOT_EXIST);
        }

        List<Integer> childrenIds = new ArrayList<>();
        listChildrenByParent(childrenIds, bean.getId());
        if (ObjectUtil.isNotEmpty(childrenIds)) {
            return ResponseVo.error(ResultCode.IN_USE);
        }

        UserDeptDto userDeptDto = new UserDeptDto();
        userDeptDto.setDeptId(bean.getId());
        List<UserDept> userDepts = this.userDeptDao.queryByCondition(userDeptDto);
        if (ObjectUtil.isNotEmpty(userDepts)) {
            return ResponseVo.error(ResultCode.IN_USE);
        }

        Map query = new HashMap();
        query.put("deptId", id);
        query.put("limit", 1);
        List<AudioRecord> audioRecords = this.audioRecordDao.queryDeptAudio(query);
        if (ObjectUtil.isNotEmpty(audioRecords)) {
            return ResponseVo.error(ResultCode.IN_USE);
        }

        List<MessageRecord> messageRecords = this.messageRecordDao.queryDeptMsg(query);
        if (ObjectUtil.isNotEmpty(messageRecords)) {
            return ResponseVo.error(ResultCode.IN_USE);
        }

        boolean bool = this.departmentDao.deleteById(id);
        if (bool) {
            return ResponseVo.success();
        } else {
            return ResponseVo.error(ResultCode.FAILED);
        }
    }

    /**
     * 通过父部门查询子部门
     *
     * @param list
     * @param parentId
     */
    @Override
    public void listChildrenByParent(List<Integer> list, Integer parentId) {
        DepartmentDto departmentDto = new DepartmentDto();
        departmentDto.setParentId(parentId);
        List<Department> children = departmentDao.queryByCondition(departmentDto);
        if (ObjectUtil.isNotEmpty(children)) {
            for (Department dept : children) {
                if (dept == null) continue;
                list.add(dept.getId());
                listChildrenByParent(list, dept.getId());
            }
        }
    }

    /**
     * 获取用户所有部门
     *
     * @return
     */
    @Override
    public List<Department> listUserDepartment() {
        List<Department> list = new ArrayList<>();
        List<Integer> ids = getDeptList(null);
        if (ObjectUtil.isNotEmpty(ids)) {
            log.info("ids:" + ids.stream().map(v -> v.toString()).collect(Collectors.joining(",")));
            Map map = new HashMap();
            map.put("ids", ids);
            list = this.departmentDao.getByIds(map);
        }
        return list;
    }

    @Override
    public List<UserDept> listDepartmentUser(int id) {
        UserDeptDto userDeptDto = new UserDeptDto();
        userDeptDto.setDeptId(id);
        List<UserDept> userDepts = this.userDeptDao.queryByCondition(userDeptDto);
        return userDepts;
    }

    /**
     * 设置部门
     * @param deptVo
     * @return
     */
    @Transactional
    @Override
    public ResponseVo setDept(DeptVo deptVo) {
        if (ObjectUtil.isEmpty(deptVo)
                || ObjectUtil.isEmpty(deptVo.getDeptIds())) {
            return ResponseVo.error(ResultCode.PARAM_ERROR);
        }

        Map map = new HashMap();
        map.put("deptIds", deptVo.getDeptIds());
        this.userDeptDao.deleteByDeptIds(map);
        List<UserDept> list = new ArrayList<>();

        deptVo.getDeptIds().forEach(deptId -> {
            deptVo.getUsers().forEach(user -> {
                if (StringUtil.isNotEmpty(user.getUserid())) {
                    UserDept userDept = new UserDept();
                    userDept.setDeptId(deptId);
                    userDept.setUserid(user.getUserid());
                    userDept.setName(user.getName());
                    userDept.setPhone(user.getPhone());
                    userDept.setEmail(user.getEmail());
                    userDept.setOperator(getOperatorName());
                    userDept.setCreateTime(DateUtil.getCurrentDateTime());
                    list.add(userDept);
                }
            });
        });

        if (ObjectUtil.isNotEmpty(list)) {
            boolean result = this.userDeptDao.insertBatch(list);
            if (!result) {
                return ResponseVo.error(ResultCode.FAILED);
            }
        }

        return ResponseVo.success();
    }

    /**
     * 用户列表
     *
     * @return
     */
    @Override
    public List<String> listUser() {
        List<String> userList = new ArrayList<>();
        List<UserDept> userDepts = this.userDeptDao.listUser();
        if (ObjectUtil.isNotEmpty(userDepts)) {
            userList = userDepts.stream()
                    .map(userDept -> userDept.getName())
                    .collect(Collectors.toList());
        }
        return userList;
    }

    @Override
    protected void saveOperationHistory(Integer id, String operation) {

    }

    @Override
    protected void operateAuditData(Integer id, String operation) {

    }
}
