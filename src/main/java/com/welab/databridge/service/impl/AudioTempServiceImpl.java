package com.welab.databridge.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.welab.anti.fraud.vo.VoiceSendVo;
import com.welab.databridge.dto.AudioTempDto;
import com.welab.databridge.entity.AudioTemp;
import com.welab.databridge.dao.AudioTempDao;
import com.welab.databridge.enums.CallStatus;
import com.welab.databridge.enums.CallType;
import com.welab.databridge.enums.ProcessStatus;
import com.welab.databridge.enums.SupplierType;
import com.welab.databridge.service.AudioTempService;
import com.welab.databridge.util.ConfigUtil;
import com.welab.databridge.util.DateUtil;
import com.welab.databridge.util.StringUtil;
import com.welab.databridge.vo.audio.CollectionRecordVo;
import com.wolaidai.approval.model.voice.VoiceSendDetail;
import org.springframework.stereotype.Service;
import com.welab.databridge.common.ResultCode;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * (AudioTemp)表服务实现类
 * (代码生成器自动生成)
 *
 * <AUTHOR>
 * @date 2023-03-17 14:46:36
 */
@Service("audioTempService")
public class AudioTempServiceImpl implements AudioTempService {
    @Resource
    private AudioTempDao audioTempDao;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public AudioTemp getById(Integer id) {
        return this.audioTempDao.getById(id);
    }

    /**
     * 通过IDs查询数据
     *
     * @param ids 主键
     * @return 实例对象
     */
    @Override
    public List<AudioTemp> getByIds(List<Integer> ids) {
        Map map = new HashMap<>();
        map.put("ids", ids);
        return this.audioTempDao.getByIds(map);
    }

    /**
     * 列表条件查询
     *
     * @param dto
     * @return
     */
    @Override
    public List<AudioTemp> queryByCondition(AudioTempDto dto) {
        return this.audioTempDao.queryByCondition(dto);
    }

    /**
     * 新增数据
     *
     * @param bean 对象
     */
    @Override
    public ResultCode insert(AudioTemp bean) {
        this.audioTempDao.insert(bean);
        return ResultCode.SUCCESS;
    }

    /**
     * 修改数据
     *
     * @param dto 转换对象
     */
    @Override
    public ResultCode update(AudioTempDto dto) {
        AudioTemp bean = this.audioTempDao.getById(dto.getId());
        if (null == bean) {
            return ResultCode.DATA_NOT_EXIST;
        }
        AudioTemp updateBean = dto.toUpdateBean();
        updateBean.setId(bean.getId());
        this.audioTempDao.update(updateBean);
        return ResultCode.SUCCESS;
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return ResultCode
     */
    @Override
    public ResultCode deleteById(Integer id) {
        boolean bool = this.audioTempDao.deleteById(id);
        if (bool) {
            return ResultCode.SUCCESS;
        } else {
            return ResultCode.FAILED;
        }
    }

    /**
     * 保存从审批获取的通话信息
     *
     * @param list
     */
    @Override
    public void saveApprovalData(List<VoiceSendDetail> list, String queryDate) {
        if (ObjectUtil.isEmpty(list)) {
            return;
        }
        List<AudioTemp> audioTempList = new ArrayList<>();
        AudioTemp audioTemp;
        VoiceSendDetail voiceSendDetail;
        for (int i=0; i<list.size(); i++) {
            audioTemp = new AudioTemp();
            voiceSendDetail = list.get(i);
            audioTemp.setCno(voiceSendDetail.getCno());
            audioTemp.setMobile(voiceSendDetail.getCustomerNum());
            audioTemp.setSign(ConfigUtil.config.getApprovalToken());
            audioTemp.setRecordFile(voiceSendDetail.getRecordFileName());
            audioTemp.setCallType(CallType.CALL.getRemark());
            audioTemp.setProvider(SupplierType.ST_CTI.getKey());
            if (StringUtil.isEmpty(voiceSendDetail.getVoiceFileUrl())) {
                audioTemp.setCallStatus(CallStatus.FAIL.getRemark());
            } else {
                audioTemp.setCallStatus(CallStatus.SUCCESS.getRemark());
            }
            audioTemp.setStaffName(voiceSendDetail.getAccountName());
            if(voiceSendDetail.getBridgeTime() != null) {
                audioTemp.setStartTime(DateUtil.format(voiceSendDetail.getBridgeTime(), null));
            }
            audioTemp.setEnterpriseId(ConfigUtil.config.getApprovalEnterpriseId());
            audioTemp.setRegion("6");
            audioTemp.setEnterpriseName("审批");
            audioTemp.setStatus(ProcessStatus.DEFAULT.getStatus());
            audioTemp.setQueryDate(queryDate);
            audioTemp.setCreateTime(DateUtil.getCurrentDateTime());
            audioTempList.add(audioTemp);
        }
        saveRecord(audioTempList);
    }

    /**
     * 保存从反欺诈获取的通话信息
     *
     * @param list
     */
    @Override
    public void saveFraudData(List<VoiceSendVo> list, String queryDate) {
        if (ObjectUtil.isEmpty(list)) {
            return;
        }
        List<AudioTemp> audioTempList = new ArrayList<>();
        AudioTemp audioTemp;
        VoiceSendVo voiceSendDetail;
        for (int i=0; i<list.size(); i++) {
            audioTemp = new AudioTemp();
            voiceSendDetail = list.get(i);
            audioTemp.setCno(voiceSendDetail.getCno());
            audioTemp.setMobile(voiceSendDetail.getCustomerNum());
            audioTemp.setSign(ConfigUtil.config.getFraudToken());
            audioTemp.setRecordFile(voiceSendDetail.getRecordFileName());
            audioTemp.setCallType(CallType.CALL.getRemark());
            audioTemp.setProvider(SupplierType.ST_CTI.getKey());
            if (StringUtil.isEmpty(voiceSendDetail.getVoiceFileUrl())) {
                audioTemp.setCallStatus(CallStatus.FAIL.getRemark());
            } else {
                audioTemp.setCallStatus(CallStatus.SUCCESS.getRemark());
            }
            audioTemp.setStaffName(voiceSendDetail.getNumberTrunk());
            if(voiceSendDetail.getBridgeTime() != null) {
                audioTemp.setStartTime(DateUtil.format(voiceSendDetail.getBridgeTime(), null));
            }
            audioTemp.setEnterpriseId(ConfigUtil.config.getFraudEnterpriseId());
            audioTemp.setRegion("6");
            audioTemp.setEnterpriseName("反欺诈");
            audioTemp.setStatus(ProcessStatus.DEFAULT.getStatus());
            audioTemp.setQueryDate(queryDate);
            audioTemp.setCreateTime(DateUtil.getCurrentDateTime());
            audioTempList.add(audioTemp);
        }
        saveRecord(audioTempList);
    }

    /**
     * 保存从客服和催收获取的通话信息
     *
     * @param list
     */
    @Override
    public void saveCSAndCollectionData(List<CollectionRecordVo> list, String queryDate) {
        if (ObjectUtil.isEmpty(list)) {
            return;
        }
        List<AudioTemp> audioTempList = new ArrayList<>();
        AudioTemp audioTemp;
        for (int i=0; i<list.size(); i++) {
            audioTemp = new AudioTemp();
            BeanUtil.copyProperties(list.get(i), audioTemp);
            audioTemp.setStartTime(audioTemp.getStartTime() == null ? null : audioTemp.getStartTime().split("\\.")[0]);
            audioTemp.setStatus(ProcessStatus.DEFAULT.getStatus());
            if (StringUtil.isEmpty(audioTemp.getCallType())) {
                audioTemp.setCallType(CallType.CALL.getRemark());
            }
            audioTemp.setQueryDate(queryDate);
            audioTemp.setCreateTime(DateUtil.getCurrentDateTime());
            audioTempList.add(audioTemp);
        }

        saveRecord(audioTempList);
    }

    @Override
    public void saveRecord(List<AudioTemp> audioTempList) {
        if (ObjectUtil.isNotEmpty(audioTempList)) {
            if (audioTempList.size() > 500) {
                int number = audioTempList.size()%500 == 0 ? audioTempList.size()/500 : (audioTempList.size()/500)+1;
                for (int j=0; j<number; j++) {
                    if ((j + 1) * 500 > audioTempList.size()) {
                        this.audioTempDao.insertBatch(audioTempList.subList(j * 500, audioTempList.size()));
                    } else {
                        this.audioTempDao.insertBatch(audioTempList.subList(j * 500, (j + 1) * 500));
                    }
                }
            } else {
                this.audioTempDao.insertBatch(audioTempList);
            }
        }
    }

    @Override
    public void updateStatus(List<AudioTemp> audioTempList, String status) {
        if (ObjectUtil.isNotEmpty(audioTempList)) {
            List<Integer> ids = audioTempList.stream().map(AudioTemp::getId).collect(Collectors.toList());
            Map map = new HashMap();
            map.put("ids", ids);
            map.put("status", status);
            this.audioTempDao.updateStatus(map);
        }
    }

    @Override
    public void updateStatus(String queryDate, String status) {
        Map map = new HashMap();
        map.put("queryDate", queryDate);
        map.put("status", status);
        this.audioTempDao.updateStatusByDate(map);
    }
}
