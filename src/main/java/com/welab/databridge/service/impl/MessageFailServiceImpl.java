package com.welab.databridge.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.welab.databridge.common.ResponseVo;
import com.welab.databridge.common.ResultCode;
import com.welab.databridge.dao.MessageFailDao;
import com.welab.databridge.dto.MessageFailDto;
import com.welab.databridge.entity.MessageFail;
import com.welab.databridge.entity.MessageRecord;
import com.welab.databridge.service.MessageFailService;
import com.welab.databridge.service.impl.common.BaseServiceImpl;
import com.welab.databridge.util.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.beans.IntrospectionException;
import java.util.ArrayList;
import java.util.List;

/**
 * 短信上传失败记录(MessageFail)表服务实现类
 * (代码生成器自动生成)
 *
 * <AUTHOR>
 * @date 2023-01-03 14:29:43
 */
@Slf4j
@Service("messageFailService")
public class MessageFailServiceImpl extends BaseServiceImpl implements MessageFailService {
    @Resource
    private MessageFailDao messageFailDao;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public MessageFail getById(Integer id) {
        return this.messageFailDao.getById(id);
    }

    /**
     * 列表条件查询
     *
     * @param dto
     * @return
     */
    @Override
    public List<MessageFail> queryByCondition(MessageFailDto dto) {
        List<MessageFail> list = new ArrayList<>();
        List<Integer> deptIds = getDeptList(dto.getDeptId());
        if (ObjectUtil.isNotEmpty(deptIds)) {
            dto.setDeptIds(deptIds);
            list = this.messageFailDao.queryByCondition(dto);
        }
        return list;
    }

    /**
     * 新增数据
     *
     * @param dto 转换对象
     */
    @Override
    public ResultCode insert(MessageFailDto dto) {
        MessageFail bean = dto.toAddBean();
        this.messageFailDao.insert(bean);
        return ResultCode.SUCCESS;
    }

    /**
     * 修改数据
     *
     * @param dto 转换对象
     */
    @Override
    public ResultCode update(MessageFailDto dto) {
        MessageFail bean = this.messageFailDao.getById(dto.getId());
        if (null == bean) {
            return ResultCode.DATA_NOT_EXIST;
        }
        MessageFail updateBean = dto.toUpdateBean();
        updateBean.setId(bean.getId());
        this.messageFailDao.update(updateBean);
        return ResultCode.SUCCESS;
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return ResultCode
     */
    @Override
    public ResultCode deleteById(Integer id) {
        boolean bool = this.messageFailDao.deleteById(id);
        if (bool) {
            return ResultCode.SUCCESS;
        } else {
            return ResultCode.FAILED;
        }
    }

    /**
     * 导出筛选数据
     *
     * @param dto 导出条件
     * @return ResultCode
     */
    @Override
    public void export(MessageFailDto dto) {
        List<MessageFail> list = queryByCondition(dto);
        try {
            ExcelWriter writer;
            if (ObjectUtil.isEmpty(list)){
                writer = ExcelUtil.exportEmptyBuilder(MessageFail.class);
            } else {
                writer = ExcelUtil.exportBuilder(list);
            }
            ExcelUtil.export(response, "result.xls", writer.getWorkbook());
        } catch (IntrospectionException e) {
            log.error(getOperatorName() + " export error, param:" + dto +", ex:" + e.getMessage());
        }
    }

    @Override
    protected void saveOperationHistory(Integer id, String operation) {

    }

    @Override
    protected void operateAuditData(Integer id, String operation) {

    }
}
