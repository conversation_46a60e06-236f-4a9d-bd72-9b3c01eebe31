package com.welab.databridge.service.impl;

import com.welab.databridge.common.ResultCode;
import com.welab.databridge.dao.DataHistoryDao;
import com.welab.databridge.dto.DataHistoryDto;
import com.welab.databridge.entity.DataHistory;
import com.welab.databridge.service.DataHistoryService;
import com.welab.databridge.service.impl.common.BaseServiceImpl;
import com.welab.databridge.util.DateUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 历史版本(DataHistory)表服务实现类
 * (代码生成器自动生成)
 *
 * <AUTHOR>
 * @date 2023-02-02 15:53:02
 */
@Service("dataHistoryService")
public class DataHistoryServiceImpl extends BaseServiceImpl implements DataHistoryService {
    @Resource
    private DataHistoryDao dataHistoryDao;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public DataHistory getById(Integer id) {
        return this.dataHistoryDao.getById(id);
    }

    /**
     * 列表条件查询
     *
     * @param dto
     * @return
     */
    @Override
    public List<DataHistory> queryByCondition(DataHistoryDto dto) {
        return this.dataHistoryDao.queryByCondition(dto);
    }

    /**
     * 新增数据
     *
     * @param dto 转换对象
     */
    @Override
    public ResultCode insert(DataHistoryDto dto) {
        DataHistory bean = dto.toAddBean();
        bean.setVersion("");
        bean.setOperatorId(getOperatorId());
        bean.setOperatorName(getOperatorName());
        bean.setCreateTime(DateUtil.getCurrentDateTime());
        bean.setUpdateTime(DateUtil.getCurrentDateTime());
        this.dataHistoryDao.insert(bean);
        return ResultCode.SUCCESS;
    }

    /**
     * 修改数据
     *
     * @param dto 转换对象
     */
    @Override
    public ResultCode update(DataHistoryDto dto) {
        DataHistory bean = this.dataHistoryDao.getById(dto.getId());
        if (null == bean) {
            return ResultCode.DATA_NOT_EXIST;
        }
        DataHistory updateBean = dto.toUpdateBean();
        updateBean.setId(bean.getId());
        this.dataHistoryDao.update(updateBean);
        return ResultCode.SUCCESS;
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return ResultCode
     */
    @Override
    public ResultCode deleteById(Integer id) {
        boolean bool = this.dataHistoryDao.deleteById(id);
        if (bool) {
            return ResultCode.SUCCESS;
        } else {
            return ResultCode.FAILED;
        }
    }

    @Override
    protected void saveOperationHistory(Integer id, String operation) {

    }

    @Override
    protected void operateAuditData(Integer id, String operation) {

    }
}
