package com.welab.databridge.service.impl;

import com.welab.databridge.common.ResultCode;
import com.welab.databridge.dto.AudioRecordTempDto;
import com.welab.databridge.entity.AudioRecordTemp;
import com.welab.databridge.dao.AudioRecordTempDao;
import com.welab.databridge.service.AudioRecordTempService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 音频记录临时(AudioRecordTemp)表服务实现类
 * (代码生成器自动生成)
 *
 * <AUTHOR>
 * @date 2024-03-14 10:50:17
 */
@Service("audioRecordTempService")
public class AudioRecordTempServiceImpl implements AudioRecordTempService {
    @Resource
    private AudioRecordTempDao audioRecordTempDao;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public AudioRecordTemp getById(Integer id) {
        return this.audioRecordTempDao.getById(id);
    }

    /**
     * 列表条件查询
     *
     * @param dto
     * @return
     */
    @Override
    public List<AudioRecordTemp> queryByCondition(AudioRecordTempDto dto) {
        return this.audioRecordTempDao.queryByCondition(dto);
    }

    /**
     * 查询未入库的正常记录
     *
     * @return
     */
    @Override
    public List<AudioRecordTemp> queryUnSync() {
        return this.audioRecordTempDao.queryUnSync();
    }

    /**
     * 新增数据
     *
     * @param bean
     */
    @Override
    public ResultCode insert(AudioRecordTemp bean) {
        this.audioRecordTempDao.insert(bean);
        return ResultCode.SUCCESS;
    }

    /**
     * 修改数据
     *
     * @param dto 转换对象
     */
    @Override
    public ResultCode update(AudioRecordTempDto dto) {
        AudioRecordTemp updateBean = dto.toUpdateBean();
        updateBean.setId(dto.getId());
        this.audioRecordTempDao.update(updateBean);
        return ResultCode.SUCCESS;
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return ResultCode
     */
    @Override
    public ResultCode deleteById(Integer id) {
        boolean bool = this.audioRecordTempDao.deleteById(id);
        if (bool) {
            return ResultCode.SUCCESS;
        } else {
            return ResultCode.FAILED;
        }
    }
}
