package com.welab.databridge.service.impl;

import com.welab.databridge.dto.UserDeptDto;
import com.welab.databridge.entity.UserDept;
import com.welab.databridge.dao.UserDeptDao;
import com.welab.databridge.service.UserDeptService;
import org.springframework.stereotype.Service;
import com.welab.databridge.common.ResultCode;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 用户部门关系表(UserDept)表服务实现类
 * (代码生成器自动生成)
 *
 * <AUTHOR>
 * @date 2023-01-03 14:29:56
 */
@Service("userDeptService")
public class UserDeptServiceImpl implements UserDeptService {
    @Resource
    private UserDeptDao userDeptDao;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public UserDept getById(Integer id) {
        return this.userDeptDao.getById(id);
    }

    /**
     * 列表条件查询
     *
     * @param dto
     * @return
     */
    @Override
    public List<UserDept> queryByCondition(UserDeptDto dto) {
        return this.userDeptDao.queryByCondition(dto);
    }

    /**
     * 新增数据
     *
     * @param dto 转换对象
     */
    @Override
    public ResultCode insert(UserDeptDto dto) {
        UserDept bean = dto.toAddBean();
        this.userDeptDao.insert(bean);
        return ResultCode.SUCCESS;
    }

    /**
     * 修改数据
     *
     * @param dto 转换对象
     */
    @Override
    public ResultCode update(UserDeptDto dto) {
        UserDept bean = this.userDeptDao.getById(dto.getId());
        if (null == bean) {
            return ResultCode.DATA_NOT_EXIST;
        }
        UserDept updateBean = dto.toUpdateBean();
        updateBean.setId(bean.getId());
        this.userDeptDao.update(updateBean);
        return ResultCode.SUCCESS;
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return ResultCode
     */
    @Override
    public ResultCode deleteById(Integer id) {
        boolean bool = this.userDeptDao.deleteById(id);
        if (bool) {
            return ResultCode.SUCCESS;
        } else {
            return ResultCode.FAILED;
        }
    }

    @Override
    public UserDept getDeptByName(String name) {
        UserDept user = null;
        UserDeptDto userDeptDto = new UserDeptDto();
        userDeptDto.setName(name);
        List<UserDept> userDepts = userDeptDao.queryByCondition(userDeptDto);
        if (userDepts != null) {
            user = userDepts.get(0);
        }
        return user;
    }
}
