package com.welab.databridge.service.impl;

import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.model.OSSObject;
import com.welab.databridge.common.ResponseVo;
import com.welab.databridge.common.ResultCode;
import com.welab.databridge.dao.AudioApplyDao;
import com.welab.databridge.dao.AudioRecordDao;
import com.welab.databridge.dto.AudioApplyDto;
import com.welab.databridge.entity.AudioApply;
import com.welab.databridge.entity.AudioRecord;
import com.welab.databridge.entity.UserDept;
import com.welab.databridge.enums.ApproveCode;
import com.welab.databridge.enums.AudioDownloadStatus;
import com.welab.databridge.enums.AuditStatus;
import com.welab.databridge.enums.BizType;
import com.welab.databridge.oss.OssService;
import com.welab.databridge.service.AudioApplyService;
import com.welab.databridge.service.UserDeptService;
import com.welab.databridge.service.impl.common.BaseServiceImpl;
import com.welab.databridge.util.*;
import com.welab.databridge.vo.audit.CallbackParam;
import com.welab.databridge.vo.common.MessageInfo;
import com.welab.document.enums.OssStorageClassEnum;
import com.welab.document.interfaces.dto.UploadOssDTO;
import com.welab.document.interfaces.facade.OssClientServiceFacade;
import com.welab.document.util.FileUploader;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * (AudioApply)表服务实现类
 * (代码生成器自动生成)
 *
 * <AUTHOR>
 * @date 2023-03-01 18:07:59
 */
@Slf4j
@Service("audioApplyService")
public class AudioApplyServiceImpl extends BaseServiceImpl implements AudioApplyService {
    @Resource
    private AudioApplyDao audioApplyDao;
    @Resource
    private OssService ossService;
    @Resource
    private AudioRecordDao audioRecordDao;
    @Resource
    private UserDeptService userDeptService;

    @Value("${aliyun.oss.bucketName}")
    private String ALIYUN_OSS_BUCKET_NAME;
    @Value("${aliyun.oss.dir.prefix}")
    private String ALIYUN_OSS_DIR_PREFIX;
    @Value("${appId}")
    private String appId;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public AudioApply getById(Integer id) {
        return this.audioApplyDao.getById(id);
    }

    /**
     * 列表条件查询
     *
     * @param dto
     * @return
     */
    @Override
    public List<AudioApply> queryByCondition(AudioApplyDto dto) {
        return this.audioApplyDao.queryByCondition(dto);
    }

    /**
     * 新增数据
     *
     * @param dto 转换对象
     */
    @Override
    public ResultCode insert(AudioApplyDto dto) {
        AudioApply bean = dto.toAddBean();
        this.audioApplyDao.insert(bean);
        return ResultCode.SUCCESS;
    }

    /**
     * 修改数据
     *
     * @param dto 转换对象
     */
    @Override
    public ResultCode update(AudioApplyDto dto) {
        AudioApply bean = this.audioApplyDao.getById(dto.getId());
        if (null == bean) {
            return ResultCode.DATA_NOT_EXIST;
        }
        AudioApply updateBean = dto.toUpdateBean();
        updateBean.setId(bean.getId());
        this.audioApplyDao.update(updateBean);
        return ResultCode.SUCCESS;
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return ResultCode
     */
    @Override
    public ResultCode deleteById(Integer id) {
        boolean bool = this.audioApplyDao.deleteById(id);
        if (bool) {
            return ResultCode.SUCCESS;
        } else {
            return ResultCode.FAILED;
        }
    }

    /**
     * 审核回调
     *
     * @param callbackParam 回调参数
     * @return ResponseVo
     */
    @Override
    public ResponseVo callback(CallbackParam callbackParam) {
        List<Integer> businessIds = callbackParam.getBusinessIds()
                .stream()
                .map(v -> Integer.parseInt(v))
                .collect(Collectors.toList());
        AudioApply audioApply = this.audioApplyDao.getById(businessIds.get(0));
        if (ObjectUtil.isNotEmpty(audioApply)) {
            boolean anyMatch = checkApproveState(callbackParam.getAction(), audioApply);
            if (anyMatch) {
                return ResponseVo.response(ResultCode.AUDIT_FAILED);
            }

            process(audioApply, callbackParam);

        }
        return ResponseVo.response(ResultCode.AUDIT_SUCCESS);
    }

    @Override
    public void process(AudioApply audioApply, CallbackParam callbackParam) {
        log.info("process start");
        String auditConfigCode = callbackParam.getAuditConfigCode();

        audioApply.setApproveStatus(AuditStatus.getValueByAction(callbackParam.getAction()));
        this.audioApplyDao.update(audioApply);

        if (AuditStatus.APPROVED.getAction().equals(callbackParam.getAction())) {
            saveOperationHistory(audioApply.getId(), ApproveCode.getByCode(auditConfigCode).getApproveType());

            Thread t = new Thread() {
                public void run() {
                    operateAuditData(audioApply.getId(), ApproveCode.getByCode(auditConfigCode).getApproveType());
                }
            };
            t.start();
        }
        log.info("process end");
    }

    private boolean checkApproveState(String action, AudioApply audioApply) {
        return (AuditStatus.IN_REVIEW.getAction().equals(action)
                        && AuditStatus.IN_REVIEW.getValue().equals(audioApply.getApproveStatus()))  //提交审核时，数据状态是审核中
                        || (!AuditStatus.IN_REVIEW.getAction().equals(action)
                        && !AuditStatus.IN_REVIEW.getValue().equals(audioApply.getApproveStatus())) //审核通过、审核拒绝、审核撤销时，数据状态不是审核中
                ;
    }

    @Override
    protected void saveOperationHistory(Integer id, String operation) {
        AudioApply bean = this.audioApplyDao.getById(id);
        JSONObject content = JSON.parseObject(JSON.toJSONString(bean));
        saveHistoryVersion(id, BizType.AUDIO_RECORD.getKey(), operation, content);
    }

    @Override
    protected void operateAuditData(Integer id, String operation) {
        long start = System.currentTimeMillis();
        log.info("operateAuditData start");
        AudioApply bean = this.audioApplyDao.getById(id);
        if (bean == null) return;
        bean.setStatus(AudioDownloadStatus.PROCESSING.getStatus());
        this.audioApplyDao.update(bean);
        //生成下载链接并邮件发送给用户
        Map map = new HashMap();
        map.put("ids", bean.getAudioIds());
        List<AudioRecord> list = audioRecordDao.getByIds(map);
        String url;
        if (ObjectUtil.isNotEmpty(list)) {
            List<File> fileList = new ArrayList<>();
            File file;
            //7天有效期
            long expire = 3600 * 1000 * 24 * 7L;

            for (AudioRecord audioRecord : list) {
                //归档文件需先解冻
//                if (ossService.isArchiveObject(audioRecord.getUrl())) {
//                    ossService.restoreObject(audioRecord.getUrl());
//                    ossService.waitRestoreCompleted(audioRecord.getUrl());
//                }
//                ossObject = ossService.getFile(audioRecord.getUrl());
                try {
                    if (!LocalDateTime.now().minusDays(1095l).isBefore(DateUtil.format(audioRecord.getStartTime(), null))) {
                        ossService.restoreByDocServer(audioRecord.getUrl());
                    }
                    file = FileUtil.getFileFromNet(ossService.getOssUrlByDocServer(audioRecord.getUrl(), expire));
                    fileList.add(file);
                } catch (Exception e) {
                    log.error("下载语音文件出错，ex:" + e.getMessage());
                }
            }
            try {
                File zipFile = FileUtil.generateZipFile(fileList);

//                String path = ossService.saveByteArray(BizType.AUDIO_RECORD.getKey(), bean.getApplier(), file.getName(), new FileInputStream(file));
                //7天有效期
//                Date expiration = new Date(System.currentTimeMillis() + 3600 * 1000 * 24 * 7);
//                url = ossService.getDocumentUrl(path, expiration);

                String ossKey = ossService.uploadByDocServer(BizType.AUDIO_RECORD.getKey(), bean.getApplier(), zipFile.getName(), new FileInputStream(zipFile), false);
                url = ossService.getOssUrlByDocServer(ossKey, expire);
                log.info("zip url: " + url);
            } catch (Exception e) {
                log.error("下载语音文件失败，生成zip异常, ex:" + e.getMessage());
                bean.setStatus(AudioDownloadStatus.FAIL.getStatus());
                this.audioApplyDao.update(bean);
                return;
            }

            //邮件发送
            UserDept userDept = userDeptService.getDeptByName(bean.getApplier());
            if (ObjectUtil.isEmpty(userDept) || StringUtil.isEmpty(userDept.getEmail())) {
                log.error("下载语音文件:" + url + "失败，收件人邮箱为空");
                bean.setStatus(AudioDownloadStatus.FAIL.getStatus());
                this.audioApplyDao.update(bean);
            } else {
                MessageInfo msg = new MessageInfo();
                msg.setTitle("下载语音通知");
                msg.setTopic("录音下载");
                msg.setContent("您本次申请下载的录音文件地址为：" + url + "  链接有效期为7天，请尽快下载");
                try {
                    log.info("msg: " + msg + ", sendTo:" + userDept.getEmail());
                    MailUtil.send(msg, Arrays.asList(userDept.getEmail()));
                    bean.setStatus(AudioDownloadStatus.FINISH.getStatus());
                } catch (Exception e) {
                    log.error("【" + msg + "】，邮件发送失败，ex:" + e.getMessage());
                    bean.setStatus(AudioDownloadStatus.FAIL.getStatus());
                }
                this.audioApplyDao.update(bean);
            }
        }
        log.info("operateAuditData end, used: " + (System.currentTimeMillis() - start));
    }
}
