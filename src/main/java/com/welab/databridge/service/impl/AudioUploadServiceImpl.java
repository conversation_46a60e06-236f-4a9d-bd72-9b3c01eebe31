package com.welab.databridge.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.welab.databridge.common.ResponseVo;
import com.welab.databridge.common.ResultCode;
import com.welab.databridge.dao.AudioFailDao;
import com.welab.databridge.dao.AudioRecordDao;
import com.welab.databridge.dao.AudioUploadDao;
import com.welab.databridge.dto.AudioUploadDto;
import com.welab.databridge.entity.AudioFail;
import com.welab.databridge.entity.AudioRecord;
import com.welab.databridge.entity.AudioUpload;
import com.welab.databridge.enums.*;
import com.welab.databridge.help.AudioHelper;
import com.welab.databridge.help.AgentAudioHelper;
import com.welab.databridge.oss.OssService;
import com.welab.databridge.service.AudioUploadService;
import com.welab.databridge.service.impl.common.BaseServiceImpl;
import com.welab.databridge.util.DateUtil;
import com.welab.databridge.util.StringUtil;
import com.welab.databridge.vo.audio.AudioVo;
import com.welab.databridge.vo.audit.CallbackParam;
import com.welab.databridge.vo.file.FileVo;
import com.welab.databridge.vo.audio.AgentAudioVo;
import com.welab.user.interfaces.dto.UserDTO;
import com.welab.user.interfaces.facade.UserServiceFacade;
import com.welab.usercenter.model.base.Profile;
import com.welab.usercenter.service.ProfileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 音频上传记录(AudioUpload)表服务实现类
 * (代码生成器自动生成)
 *
 * <AUTHOR>
 * @date 2023-01-03 14:29:40
 */
@Slf4j
@Service("audioUploadService")
public class AudioUploadServiceImpl extends BaseServiceImpl implements AudioUploadService {
    @Resource
    private AudioUploadDao audioUploadDao;
    @Resource
    private AudioRecordDao audioRecordDao;
    @Resource
    private AudioFailDao audioFailDao;
    @Resource
    private OssService ossService;
    @Reference(timeout = 8000)
    private UserServiceFacade userServiceFacade;
    @Reference(timeout = 8000)
    private ProfileService profileService;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public AudioUpload getById(Integer id) {
        return this.audioUploadDao.getById(id);
    }

    /**
     * 列表条件查询
     *
     * @param dto
     * @return
     */
    @Override
    public PageInfo<AudioUpload> queryByCondition(AudioUploadDto dto) {
        List<AudioUpload> list = new ArrayList<>();
        PageInfo<AudioUpload> pageInfo = new PageInfo<>(list);
        List<Integer> deptIds = getDeptList(dto.getDeptId());
        if (ObjectUtil.isNotEmpty(deptIds)) {
            dto.setDeptIds(deptIds);
            PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
            list = this.audioUploadDao.queryByCondition(dto);
            pageInfo = new PageInfo<>(list);
        }
        return pageInfo;
    }

    /**
     * 新增数据
     *
     * @param dto 转换对象
     */
    @Override
    public ResultCode insert(AudioUploadDto dto) {
        AudioUpload bean = dto.toAddBean();
        this.audioUploadDao.insert(bean);
        return ResultCode.SUCCESS;
    }

    /**
     * 修改数据
     *
     * @param dto 转换对象
     */
    @Override
    public ResultCode update(AudioUploadDto dto) {
        AudioUpload bean = this.audioUploadDao.getById(dto.getId());
        if (null == bean) {
            return ResultCode.DATA_NOT_EXIST;
        }
        AudioUpload updateBean = dto.toUpdateBean();
        updateBean.setId(bean.getId());
        this.audioUploadDao.update(updateBean);
        return ResultCode.SUCCESS;
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return ResultCode
     */
    @Override
    public ResponseVo deleteById(Integer id) {
        AudioUpload bean = this.audioUploadDao.getById(id);
        if (null == bean) {
            return ResponseVo.error(ResultCode.DATA_NOT_EXIST);
        }

        if (Operation.DELETE.getType().equals(bean.getOperation())) {
            return ResponseVo.error(ResultCode.DATA_DELETED);
        }

        if (!getOperatorName().equals(bean.getOperator())) {
            return ResponseVo.error(ResultCode.OPERATOR_CANNOT_DELETE);
        }

        if (AuditStatus.IN_REVIEW.getValue().equals(bean.getApproveStatus())) {
            return ResponseVo.error(ResultCode.IN_AUDIT);
        }

        List<AudioUpload> list = Arrays.asList(bean);
        List<String> conditionKeys = Arrays.asList("batchNo");
        return applyAudit(list, ApproveCode.AUDIO_UPLOAD_DELETE.getApproveCode(), Operation.DELETE.getType(), Operation.DELETE.getType(), conditionKeys);
    }

    /**
     * 上传音频
     *
     * @param operatorId 操作人Id
     * @param deptId 部门ID
     * @param manualAgentId 手动输入的坐席ID（可选）
     * @param files 上传文件
     */
    @Async("uploadExecutor")
    @Override
    public void upload(String operatorId, int deptId, String manualAgentId, List<FileVo> files) {
        AudioUpload audioUpload = new AudioUpload();
        String operator = getOperatorNameById(operatorId);
        audioUpload.setBatchNo(operator.replace(".", "") + DateUtil.format(LocalDateTime.now(), DateUtil.FORMAT_WHOLE_DATE));
        audioUpload.setUploadNum(files.size());
        audioUpload.setSuccessNum(0);
        audioUpload.setFailNum(0);
        audioUpload.setDeptId(deptId);
        audioUpload.setStatus(ProcessStatus.PROCESSING.getStatus());
        audioUpload.setOperation(Operation.ADD.getType());
        audioUpload.setOperator(operator);
        audioUpload.setApproveType(ApproveCode.DEFAULT.getApproveType());
        audioUpload.setApproveStatus(AuditStatus.DEFAULT.getValue());
        audioUpload.setAgentId(manualAgentId); // 设置手动输入的坐席ID
        audioUpload.setCreateTime(DateUtil.getCurrentDateTime());
        audioUpload.setUpdateTime(DateUtil.getCurrentDateTime());
        this.audioUploadDao.insert(audioUpload);

        List<Integer> deptIds = getDeptByOperatorId(operatorId);
        if (!deptIds.contains(deptId)) {
            audioUpload.setFailMsg(ResultCode.DEPARTMENT_ERROR.getMessage());
            this.audioUploadDao.update(audioUpload);
            return;
        }

        String url;
        AudioRecord audioRecord;
        AudioFail audioFail;
        List<AudioRecord> successList = new ArrayList<>();
        List<AudioFail> failList = new ArrayList<>();
        String failMsg;
        for (FileVo fileVo : files) {
            failMsg = "";
            try {
                // 首先尝试使用增强的坐席解析
                AgentAudioVo agentAudioVo = AgentAudioHelper.parseAgentFileName(fileVo.getFileName());

                // 如果增强解析失败，回退到原有解析方式
                if (StringUtil.isEmpty(agentAudioVo.getMobile()) || StringUtil.isEmpty(agentAudioVo.getStartTime())) {
                    AudioVo audioVo = AudioHelper.parseFileName(fileVo.getFileName());
                    if (ObjectUtil.isNotEmpty(audioVo) && StringUtil.isNotEmpty(audioVo.getMobile()) && StringUtil.isNotEmpty(audioVo.getStartTime())) {
                        agentAudioVo.setMobile(audioVo.getMobile());
                        agentAudioVo.setStartTime(audioVo.getStartTime());
                        agentAudioVo.setSource("legacy");
                    }
                }

                if (StringUtil.isEmpty(agentAudioVo.getMobile()) || StringUtil.isEmpty(agentAudioVo.getStartTime())) {
                    failMsg = ResultCode.FILE_NAME_ERROR.getMessage();
                    throw new RuntimeException(failMsg);
                }
//                url = ossService.saveByteArray(BizType.AUDIO_UPLOAD.getKey(), operator, fileVo.getFileName(), fileVo.getInputStream());
                boolean isArchive = false;
                if (!LocalDateTime.now().minusDays(1095l).isBefore(DateUtil.format(agentAudioVo.getStartTime(), null))) {
                    isArchive = true;
                }
                AudioHelper.changeAmrToMp3(fileVo);
                Integer duration = AudioHelper.getDuration(fileVo);
                url = ossService.uploadByDocServer(BizType.AUDIO_UPLOAD.getKey(), operator, fileVo.getFileName(), fileVo.getInputStream(), isArchive);
                fileVo.getInputStream().close();
                AudioHelper.deleteTempFile(fileVo.getFileName());
                AudioHelper.deleteAudioTempFile(fileVo.getFileName());
                audioRecord = new AudioRecord();
                audioRecord.setDuration(duration);
                audioRecord.setUrl(url);
                audioRecord.setDeptId(deptId);
                audioRecord.setUploadId(audioUpload.getId());
                audioRecord.setPhone(agentAudioVo.getMobile());
                audioRecord.setUserid(getUserid(audioRecord.getPhone()));
                audioRecord.setCnid(getCnid(audioRecord.getUserid()));
                audioRecord.setStartTime(agentAudioVo.getStartTime());

                // 设置坐席信息：优先使用从文件名解析的，其次使用手动输入的
                String finalAgentId = StringUtil.isNotEmpty(agentAudioVo.getAgentId()) ?
                    agentAudioVo.getAgentId() : manualAgentId;
                audioRecord.setAgentId(finalAgentId);
                audioRecord.setUploadTime(DateUtil.getCurrentDateTime());
                audioRecord.setCallType(CallType.CALL.getValue());
                audioRecord.setUploadMethod(UploadMethod.MANUAL.getValue());
                audioRecord.setStatus(ListenStatus.SUCCESS.getValue());
                audioRecord.setOperator(operator);
                audioRecord.setCreateTime(DateUtil.getCurrentDateTime());
                audioRecord.setSupplier("");
                audioRecord.setDept("");
                audioRecord.setOrg("");
                successList.add(audioRecord);
            } catch (Exception e) {
                log.error(operator + "上传音频失败"+ ", ex:" + e.getMessage());
                audioFail = new AudioFail();
                audioFail.setDeptId(deptId);
                audioFail.setUploadId(audioUpload.getId());
                audioFail.setName(fileVo.getFileName());
                audioFail.setUploadMethod(UploadMethod.MANUAL.getValue());
                if (StringUtil.isNotEmpty(failMsg)) {
                    audioFail.setFailMsg(failMsg);
                } else {
                    audioFail.setFailMsg(ResultCode.FILE_UPLOAD_FAIL.getMessage());
                }
                audioFail.setOperator(operator);
                audioFail.setCreateTime(DateUtil.getCurrentDateTime());
                failList.add(audioFail);
            }
        }

        if (successList.size() > 0) {
            this.audioRecordDao.insertBatch(successList);
        }

        if (failList.size() > 0) {
            this.audioFailDao.insertBatch(failList);
        }
        audioUpload.setSuccessNum(successList.size());
        audioUpload.setFailNum(failList.size());
        audioUpload.setUploadNum(successList.size() + failList.size());
        audioUpload.setStatus(ProcessStatus.FINISH.getStatus());
        audioUpload.setUpdateTime(DateUtil.getCurrentDateTime());
        this.audioUploadDao.update(audioUpload);
        return;
    }

    /**
     * 审核回调
     *
     * @param callbackParam 回调参数
     * @return ResponseVo
     */
    @Override
    public ResponseVo callback(CallbackParam callbackParam) {
        List<Integer> businessIds = callbackParam.getBusinessIds()
                .stream()
                .map(v -> Integer.parseInt(v))
                .collect(Collectors.toList());
        Map map = new HashMap();
        map.put("ids", businessIds);
        List<AudioUpload> list = this.audioUploadDao.getByIds(map);
        if (ObjectUtil.isNotEmpty(list)) {
            String auditConfigCode = callbackParam.getAuditConfigCode();
            boolean anyMatch = checkApproveState(auditConfigCode, callbackParam.getAction(), list);
            if (anyMatch) {
                return ResponseVo.response(ResultCode.AUDIT_FAILED);
            }

            list.forEach(v -> {
                if (AuditStatus.APPROVED.getAction().equals(callbackParam.getAction())) {
                    v.setOperation(ApproveCode.getByCode(auditConfigCode).getApproveType());
                }
                v.setApproveStatus(AuditStatus.getValueByAction(callbackParam.getAction()));
                v.setApproveType(ApproveCode.DEFAULT.getApproveType());
                this.audioUploadDao.update(v);

                if (AuditStatus.APPROVED.getAction().equals(callbackParam.getAction())) {
                    saveOperationHistory(v.getId(), ApproveCode.getByCode(auditConfigCode).getApproveType());
                    operateAuditData(v.getId(), ApproveCode.getByCode(auditConfigCode).getApproveType());
                }
            });
        }
        return ResponseVo.response(ResultCode.AUDIT_SUCCESS);
    }

    private boolean checkApproveState(String auditConfigCode, String action, List<AudioUpload> list) {
        return list.stream()
                .anyMatch(m -> (AuditStatus.IN_REVIEW.getAction().equals(action)
                            && AuditStatus.IN_REVIEW.getValue().equals(m.getApproveStatus()))  //提交审核时，数据状态是审核中
                        || (!AuditStatus.IN_REVIEW.getAction().equals(action)
                            && !AuditStatus.IN_REVIEW.getValue().equals(m.getApproveStatus())) //审核通过、审核拒绝、审核撤销时，数据状态不是审核中
                );
    }

    private Integer getUserid(String phone) {
        try {
            UserDTO userDTO = userServiceFacade.getUnblockedUserByMobile(phone);
            if (ObjectUtil.isNotEmpty(userDTO)) {
                Integer userid = userDTO.getId();
                if (userid != null) return userid;
            }
        } catch (Exception e) {
            log.error("获取userid失败，手机号：" + phone + "，ex:" + e.getMessage());
        }
        return -1;
    }

    private String getCnid(Integer userid) {
        try {
            if (userid.intValue() != -1) {
                Profile profile = profileService.getProfileByBorroweId(userid);
                if (ObjectUtil.isNotEmpty(profile)) {
                    String cnid = profile.getCnid();
                    if (StringUtil.isNotEmpty(cnid)) {
                        return cnid;
                    }
                }
            }
        }  catch (Exception e) {
            log.error("获取cnid失败，userid：" + userid + "，ex:" + e.getMessage());
        }
        return "";
    }

    @Override
    protected void saveOperationHistory(Integer id, String operation) {
        AudioUpload bean = this.audioUploadDao.getById(id);
        JSONObject content = JSON.parseObject(JSON.toJSONString(bean));
        saveHistoryVersion(id, BizType.AUDIO_UPLOAD.getKey(), operation, content);
    }

    @Override
    protected void operateAuditData(Integer id, String operation) {
        AudioUpload bean = this.audioUploadDao.getById(id);
        if (bean == null) return;
        bean.setOperation(Operation.DELETE.getType());
        bean.setUpdateTime(DateUtil.getCurrentDateTime());
        this.audioUploadDao.update(bean);
        this.audioRecordDao.deleteByUploadId(bean.getId());
    }
}
