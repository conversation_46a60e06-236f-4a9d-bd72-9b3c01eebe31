package com.welab.databridge.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.welab.databridge.common.ResponseVo;
import com.welab.databridge.common.ResultCode;
import com.welab.databridge.dao.MessageFailDao;
import com.welab.databridge.dao.MessageRecordDao;
import com.welab.databridge.dao.MessageUploadDao;
import com.welab.databridge.dto.MessageUploadDto;
import com.welab.databridge.entity.MessageFail;
import com.welab.databridge.entity.MessageRecord;
import com.welab.databridge.entity.MessageUpload;
import com.welab.databridge.enums.*;
import com.welab.databridge.help.MessageHelper;
import com.welab.databridge.service.MessageUploadService;
import com.welab.databridge.service.impl.common.BaseServiceImpl;
import com.welab.databridge.util.DateUtil;
import com.welab.databridge.util.ExcelUtil;
import com.welab.databridge.util.StringUtil;
import com.welab.databridge.vo.audit.CallbackParam;
import com.welab.user.interfaces.dto.UserDTO;
import com.welab.user.interfaces.facade.UserServiceFacade;
import com.welab.usercenter.model.base.Profile;
import com.welab.usercenter.service.ProfileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 短信上传记录(MessageUpload)表服务实现类
 * (代码生成器自动生成)
 *
 * <AUTHOR>
 * @date 2023-01-03 14:29:48
 */
@Slf4j
@Service("messageUploadService")
public class MessageUploadServiceImpl extends BaseServiceImpl implements MessageUploadService {
    @Resource
    private MessageUploadDao messageUploadDao;
    @Resource
    private MessageRecordDao messageRecordDao;
    @Resource
    private MessageFailDao messageFailDao;
    @Reference(timeout = 8000)
    private UserServiceFacade userServiceFacade;
    @Reference(timeout = 8000)
    private ProfileService profileService;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public MessageUpload getById(Integer id) {
        return this.messageUploadDao.getById(id);
    }

    /**
     * 列表条件查询
     *
     * @param dto
     * @return
     */
    @Override
    public PageInfo<MessageUpload> queryByCondition(MessageUploadDto dto) {
        List<MessageUpload> list = new ArrayList<>();
        PageInfo<MessageUpload> pageInfo = new PageInfo<>(list);
        List<Integer> deptIds = getDeptList(dto.getDeptId());
        if (ObjectUtil.isNotEmpty(deptIds)) {
            dto.setDeptIds(deptIds);
            PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
            list = this.messageUploadDao.queryByCondition(dto);
            pageInfo = new PageInfo<>(list);
        }
        return pageInfo;
    }

    /**
     * 新增数据
     *
     * @param dto 转换对象
     */
    @Override
    public ResultCode insert(MessageUploadDto dto) {
        MessageUpload bean = dto.toAddBean();
        this.messageUploadDao.insert(bean);
        return ResultCode.SUCCESS;
    }

    /**
     * 修改数据
     *
     * @param dto 转换对象
     */
    @Override
    public ResultCode update(MessageUploadDto dto) {
        MessageUpload bean = this.messageUploadDao.getById(dto.getId());
        if (null == bean) {
            return ResultCode.DATA_NOT_EXIST;
        }
        MessageUpload updateBean = dto.toUpdateBean();
        updateBean.setId(bean.getId());
        this.messageUploadDao.update(updateBean);
        return ResultCode.SUCCESS;
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return ResultCode
     */
    @Override
    public ResponseVo deleteById(Integer id) {
        MessageUpload bean = this.messageUploadDao.getById(id);
        if (null == bean) {
            return ResponseVo.error(ResultCode.DATA_NOT_EXIST);
        }

        if (Operation.DELETE.getType().equals(bean.getOperation())) {
            return ResponseVo.error(ResultCode.DATA_DELETED);
        }

        if (!getOperatorName().equals(bean.getOperator())) {
            return ResponseVo.error(ResultCode.OPERATOR_CANNOT_DELETE);
        }

        if (AuditStatus.IN_REVIEW.getValue().equals(bean.getApproveStatus())) {
            return ResponseVo.error(ResultCode.IN_AUDIT);
        }

        List<MessageUpload> list = Arrays.asList(bean);
        List<String> conditionKeys = Arrays.asList("batchNo");
        return applyAudit(list, ApproveCode.MESSAGE_UPLOAD_DELETE.getApproveCode(), Operation.DELETE.getType(), Operation.DELETE.getType(), conditionKeys);
    }

    /**
     * 下载模板
     *
     * @param request
     * @param response
     */
    @Override
    public void downloadTemplate(HttpServletRequest request, HttpServletResponse response) {
        String fileName = "template.xls";
        ExcelWriter writer = ExcelUtil.templateBuilder(MessageRecord.class);
        ExcelUtil.export(response, fileName, writer.getWorkbook());
    }

    /**
     * 上传短信
     *
     * @param operatorId 操作人id
     * @param deptId 部门ID
     * @param inputStream 上传文件
     */
    @Async("uploadExecutor")
    @Override
    public void upload(String operatorId, int deptId, InputStream inputStream) {
        MessageUpload messageUpload = new MessageUpload();
        String operator = getOperatorNameById(operatorId);
        messageUpload.setBatchNo(operator.replace(".", "") + DateUtil.format(LocalDateTime.now(), DateUtil.FORMAT_WHOLE_DATE));
        messageUpload.setUploadNum(0);
        messageUpload.setSuccessNum(0);
        messageUpload.setFailNum(0);
        messageUpload.setDeptId(deptId);
        messageUpload.setStatus(ProcessStatus.PROCESSING.getStatus());
        messageUpload.setOperation(Operation.ADD.getType());
        messageUpload.setOperator(operator);
        messageUpload.setApproveType(ApproveCode.DEFAULT.getApproveType());
        messageUpload.setApproveStatus(AuditStatus.DEFAULT.getValue());
        messageUpload.setCreateTime(DateUtil.getCurrentDateTime());
        messageUpload.setUpdateTime(DateUtil.getCurrentDateTime());
        this.messageUploadDao.insert(messageUpload);

        List<Integer> deptIds = getDeptByOperatorId(operatorId);
        if (!deptIds.contains(deptId)) {
            messageUpload.setFailMsg(ResultCode.DEPARTMENT_ERROR.getMessage());
            this.messageUploadDao.update(messageUpload);
            return;
        }

        try {
            ExcelReader reader = cn.hutool.poi.excel.ExcelUtil.getReader(inputStream);
            int lastRowNum = reader.getSheet().getLastRowNum();
            if (lastRowNum < 1) {
                messageUpload.setFailMsg(ResultCode.FILE_IS_EMPTY.getMessage());
                this.messageUploadDao.update(messageUpload);
                return;
            }

            List<Map<String, Object>> rowList = reader.read(0, 2, lastRowNum);
            reader.close();
            if (ObjectUtil.isEmpty(rowList)) {
                messageUpload.setFailMsg(ResultCode.FILE_IS_EMPTY.getMessage());
                this.messageUploadDao.update(messageUpload);
                return;
            }
            processData(rowList, messageUpload, operator);
            return;
        } catch (Exception e) {
            log.error("文件解析失败：" + e.getMessage());
        }
        messageUpload.setFailMsg(ResultCode.FILE_ANALYSIS_FAIL.getMessage());
        messageUpload.setStatus(ProcessStatus.FINISH.getStatus());
        this.messageUploadDao.update(messageUpload);
        return;
    }

    private void processData(List<Map<String, Object>> rowList, MessageUpload messageUpload, String operator) {
        List<MessageRecord> successList = new ArrayList<>();
        List<MessageFail> failList = new ArrayList<>();
        Map<String, Object> row;
        MessageRecord messageRecord;
        MessageFail messageFail;
        int successNum = 0;
        int failNum = 0;
        for (int i=0;i< rowList.size();i++) {
            row = rowList.get(i);
            if (ObjectUtil.isEmpty(row)) continue;
            List<String> errors = MessageHelper.checkData(row);
            if (ObjectUtil.isEmpty(errors)) {
                try {
                    messageRecord = new MessageRecord();
                    messageRecord.setUploadId(messageUpload.getId());
                    messageRecord.setDeptId(messageUpload.getDeptId());
                    messageRecord.setPhone(row.get("phone").toString().trim());
                    messageRecord.setSendTime(row.get("sendTime").toString().trim());
                    messageRecord.setContent(row.get("content").toString().trim());
                    messageRecord.setStatus(SendStatus.getValueByRemark(row.get("status").toString().trim()));
                    messageRecord.setSupplier("0");
                    messageRecord.setUserid(getUserid(messageRecord.getPhone()));
                    messageRecord.setCnid(getCnid(messageRecord.getUserid()));
                    messageRecord.setUploadMethod(UploadMethod.MANUAL.getValue());
                    messageRecord.setOperator(operator);
                    messageRecord.setCreateTime(DateUtil.getCurrentDateTime());
                    this.messageRecordDao.insert(messageRecord);
                    successNum++;
                } catch (Exception e) {
                    log.error(operator + "上传成功记录：" + row.toString() + " 入库失败，ex:" + e.getMessage());
                    errors.add(e.getMessage());
                }
            }

            if (ObjectUtil.isNotEmpty(errors)) {
                try {
                    messageFail = new MessageFail();
                    BeanUtil.copyProperties(row, messageFail);
                    messageFail.setUploadId(messageUpload.getId());
                    messageFail.setDeptId(messageUpload.getDeptId());
                    messageFail.setFailMsg(errors.stream().collect(Collectors.joining(";")));
                    messageFail.setCreateTime(DateUtil.getCurrentDateTime());
                    messageFail.setOperator(operator);
                    this.messageFailDao.insert(messageFail);
                    failNum++;
                } catch (Exception e) {
                    log.error(operator + "上传失败记录：" + row.toString() + " 入库失败，ex:" + e.getMessage());
                    failNum++;
                }
            }
        }
        messageUpload.setSuccessNum(successNum);
        messageUpload.setFailNum(failNum);
        messageUpload.setUploadNum(successNum + failNum);
        messageUpload.setUpdateTime(DateUtil.getCurrentDateTime());
        messageUpload.setStatus(ProcessStatus.FINISH.getStatus());
        this.messageUploadDao.update(messageUpload);
    }

    /**
     * 审核回调
     *
     * @param callbackParam 回调参数
     * @return ResponseVo
     */
    @Override
    public ResponseVo callback(CallbackParam callbackParam) {
        List<Integer> businessIds = callbackParam.getBusinessIds()
                .stream()
                .map(v -> Integer.parseInt(v))
                .collect(Collectors.toList());
        Map map = new HashMap();
        map.put("ids", businessIds);
        List<MessageUpload> list = this.messageUploadDao.getByIds(map);
        if (ObjectUtil.isNotEmpty(list)) {
            String auditConfigCode = callbackParam.getAuditConfigCode();
            boolean anyMatch = checkApproveState(auditConfigCode, callbackParam.getAction(), list);
            if (anyMatch) {
                return ResponseVo.response(ResultCode.AUDIT_FAILED);
            }

            list.forEach(v -> {
                if (AuditStatus.APPROVED.getAction().equals(callbackParam.getAction())) {
                    v.setOperation(ApproveCode.getByCode(auditConfigCode).getApproveType());
                }
                v.setApproveStatus(AuditStatus.getValueByAction(callbackParam.getAction()));
                v.setApproveType(ApproveCode.DEFAULT.getApproveType());
                this.messageUploadDao.update(v);

                if (AuditStatus.APPROVED.getAction().equals(callbackParam.getAction())) {
                    saveOperationHistory(v.getId(), ApproveCode.getByCode(auditConfigCode).getApproveType());
                    operateAuditData(v.getId(), ApproveCode.getByCode(auditConfigCode).getApproveType());
                }
            });
        }
        return ResponseVo.response(ResultCode.AUDIT_SUCCESS);
    }

    private boolean checkApproveState(String auditConfigCode, String action, List<MessageUpload> list) {
        return list.stream()
                .anyMatch(m -> (AuditStatus.IN_REVIEW.getAction().equals(action)
                            && AuditStatus.IN_REVIEW.getValue().equals(m.getApproveStatus())) //提交审核时，数据状态是审核中
                        || (!AuditStatus.IN_REVIEW.getAction().equals(action)
                            && !AuditStatus.IN_REVIEW.getValue().equals(m.getApproveStatus())) //审核通过、审核拒绝、审核撤销时，数据状态不是审核中
                );
    }

    private Integer getUserid(String phone) {
        try {
            UserDTO userDTO = userServiceFacade.getUnblockedUserByMobile(phone);
            if (ObjectUtil.isNotEmpty(userDTO)) {
                Integer userid = userDTO.getId();
                if (userid != null) return userid;
            }
        } catch (Exception e) {
            log.error("获取userid失败，手机号：" + phone + "，ex:" + e.getMessage());
        }
        return -1;
    }

    private String getCnid(Integer userid) {
        try {
            if (userid.intValue() != -1) {
                Profile profile = profileService.getProfileByBorroweId(userid);
                if (ObjectUtil.isNotEmpty(profile)) {
                    String cnid = profile.getCnid();
                    if (StringUtil.isNotEmpty(cnid)) {
                        return cnid;
                    }
                }
            }
        }  catch (Exception e) {
            log.error("获取cnid失败，userid：" + userid + "，ex:" + e.getMessage());
        }
        return "";
    }

    @Override
    public void saveOperationHistory(Integer id, String operation) {
        MessageUpload bean = this.messageUploadDao.getById(id);
        JSONObject content = JSON.parseObject(JSON.toJSONString(bean));
        saveHistoryVersion(id, BizType.MESSAGE_UPLOAD.getKey(), operation, content);
    }

    @Override
    public void operateAuditData(Integer id, String operation) {
        MessageUpload bean = this.messageUploadDao.getById(id);
        if (bean == null) return;
        bean.setOperation(Operation.DELETE.getType());
        bean.setUpdateTime(DateUtil.getCurrentDateTime());
        this.messageUploadDao.update(bean);
        this.messageRecordDao.deleteByUploadId(bean.getId());
    }
}
