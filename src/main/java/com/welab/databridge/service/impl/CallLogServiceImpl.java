package com.welab.databridge.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.welab.databridge.common.PageVo;
import com.welab.databridge.common.ResponseVo;
import com.welab.databridge.dto.CallLogDto;
import com.welab.databridge.service.impl.common.BaseServiceImpl;
import com.welab.databridge.util.HttpClientUtil;
import com.welab.databridge.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class CallLogServiceImpl extends BaseServiceImpl {
    @Value("${appId}")
    private String appId;
    @Value("${qingLuanUrl}")
    private String qingLuanUrl;
    @Value("${qingLuanReportLogURI}")
    private String qingLuanReportLogURI;
    @Value("${qingLuanGetLogURI}")
    private String qingLuanGetLogURI;

    @Async("callLogExecutor")
    public void saveCallLog(String eventName, String eventType, String pointSignature, String requestIp, String requestMethod,
                            String requestParam, String requestResult, String requestUrl, String resultCode, String userId, String username, String userToken) {

        CallLogDto callLogDto = new CallLogDto();
        callLogDto.setAppId(appId);
        callLogDto.setEventName(eventName);
        callLogDto.setEventType(eventType);
        callLogDto.setPointSignature(pointSignature);
        callLogDto.setRequestIp(requestIp);
        callLogDto.setRequestMethod(requestMethod);
        callLogDto.setRequestParam(requestParam);
        callLogDto.setRequestResult(requestResult);
        callLogDto.setRequestSource("PC");
        callLogDto.setRequestUrl(requestUrl);
        callLogDto.setResultCode(resultCode);
        callLogDto.setUserId(Integer.parseInt(userId));
        callLogDto.setUserName(username);
        Map<String, String> headers = new HashMap<>();
        headers.put("X-User-Token", userToken);
        headers.put("Content-Type", "application/json");
        try {
//            log.info("report log to qing luan : " + JSON.toJSONString(callLogDto));
            String result = HttpClientUtil.send(qingLuanUrl + qingLuanReportLogURI, "POST", headers, JSON.toJSONString(callLogDto));
            log.info("report log to qing luan result: " + result);
        } catch (IOException e) {
            log.error("report log to qing luan failed:" + e.getMessage());
        }
    }

    /**
     * 分页查询系统调用记录
     *
     * @param dto
     * @return
     */
    public ResponseVo listCallLog(CallLogDto dto) {
        int count = 0;
        List<JSONObject> list = new ArrayList<>();
        Map<String, String> headers = new HashMap<>();
        headers.put("X-User-Token", getUserToken());
        dto.setAppId(appId);
        String result = HttpClientUtil.sendGet(qingLuanUrl + qingLuanGetLogURI, headers, JSON.parseObject(JSON.toJSONString(dto)));
        if (StringUtil.isJSON(result)) {
            JSONObject obj = JSON.parseObject(result);
            if (obj != null && "0".equals(obj.getString("code"))) {
                JSONObject data = obj.getJSONObject("result");
                if (data != null) {
                    count = data.getInteger("totalRows");
                    data.getJSONArray("list").forEach(d -> {
                        JSONObject jsonObject = (JSONObject) d;
                        list.add(jsonObject);
                    });
                }
            }
        }
        PageVo<JSONObject> pageVo = new PageVo<>(dto.getPageNum(), dto.getPageSize(), count, list);
        return ResponseVo.success(pageVo);
    }

    @Override
    protected void saveOperationHistory(Integer id, String operation) {

    }

    @Override
    protected void operateAuditData(Integer id, String operation) {

    }


}

