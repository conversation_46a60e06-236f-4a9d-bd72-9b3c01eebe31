package com.welab.databridge.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.welab.databridge.common.ResponseVo;
import com.welab.databridge.common.ResultCode;
import com.welab.databridge.dao.MessageRecordDao;
import com.welab.databridge.dto.MessageRecordDto;
import com.welab.databridge.entity.MessageRecord;
import com.welab.databridge.enums.SendStatus;
import com.welab.databridge.service.MessageRecordService;
import com.welab.databridge.service.impl.common.BaseServiceImpl;
import com.welab.databridge.util.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.beans.IntrospectionException;
import java.util.ArrayList;
import java.util.List;

/**
 * 短信记录(MessageRecord)表服务实现类
 * (代码生成器自动生成)
 *
 * <AUTHOR>
 * @date 2023-01-03 14:29:51
 */
@Slf4j
@Service("messageRecordService")
public class MessageRecordServiceImpl extends BaseServiceImpl implements MessageRecordService {
    @Value("${exportLimit:10000}")
    private int exportLimit;

    @Resource
    private MessageRecordDao messageRecordDao;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public MessageRecord getById(Integer id) {
        return this.messageRecordDao.getById(id);
    }

    /**
     * 列表条件查询
     *
     * @param dto
     * @return
     */
    @Override
    public PageInfo<MessageRecord> queryByCondition(MessageRecordDto dto) {
        List<MessageRecord> list = new ArrayList<>();
        PageInfo<MessageRecord> pageInfo = new PageInfo<>(list);
        List<Integer> deptIds = getDeptList(dto.getDeptId());
        if (ObjectUtil.isNotEmpty(deptIds)) {
            dto.setDeptIds(deptIds);
            PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
            list = this.messageRecordDao.queryByCondition(dto);
            pageInfo = new PageInfo<>(list);
            if (ObjectUtil.isNotEmpty(list)) {
                list.stream()
                        .forEach(v -> {
                            v.setPhone(v.getPhone().replace(v.getPhone().substring(3, 7), "****"));
                            v.setCnid("");
                        });
            }
        }
        return pageInfo;
    }

    /**
     * 列表条件查询
     *
     * @param dto
     * @return
     */
    public List<MessageRecord> query(MessageRecordDto dto) {
        List<MessageRecord> list = new ArrayList<>();
        List<Integer> deptIds = getDeptList(dto.getDeptId());
        if (ObjectUtil.isNotEmpty(deptIds)) {
            dto.setDeptIds(deptIds);
            list = this.messageRecordDao.queryByCondition(dto);
            if (ObjectUtil.isNotEmpty(list)) {
                list.stream()
                        .forEach(v -> v.setPhone(v.getPhone().replace(v.getPhone().substring(3, 7), "****")));
            }
        }
        return list;
    }

    /**
     * 新增数据
     *
     * @param dto 转换对象
     */
    @Override
    public ResultCode insert(MessageRecordDto dto) {
        MessageRecord bean = dto.toAddBean();
        this.messageRecordDao.insert(bean);
        return ResultCode.SUCCESS;
    }

    /**
     * 修改数据
     *
     * @param dto 转换对象
     */
    @Override
    public ResultCode update(MessageRecordDto dto) {
        MessageRecord bean = this.messageRecordDao.getById(dto.getId());
        if (null == bean) {
            return ResultCode.DATA_NOT_EXIST;
        }
        MessageRecord updateBean = dto.toUpdateBean();
        updateBean.setId(bean.getId());
        this.messageRecordDao.update(updateBean);
        return ResultCode.SUCCESS;
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return ResultCode
     */
    @Override
    public ResultCode deleteById(Integer id) {
        boolean bool = this.messageRecordDao.deleteById(id);
        if (bool) {
            return ResultCode.SUCCESS;
        } else {
            return ResultCode.FAILED;
        }
    }

    /**
     * 导出筛选数据
     *
     * @param dto 导出条件
     * @return ResultCode
     */
    @Override
    public void export(MessageRecordDto dto) {
        List<MessageRecord> list = query(dto);
        list.stream()
                .forEach(v -> v.setStatusCn(SendStatus.getRemarkByValue(v.getStatus())));
        try {
            ExcelWriter writer;
            if (list.isEmpty()) {
                writer = ExcelUtil.exportEmptyBuilder(MessageRecord.class);
            } else {
                writer = ExcelUtil.exportBuilder(list);
            }
            ExcelUtil.export(response, "result.xls", writer.getWorkbook());
        } catch (IntrospectionException e) {
            log.error(" export error:" + e.getMessage());
        }
    }

    @Override
    protected void saveOperationHistory(Integer id, String operation) {

    }

    @Override
    protected void operateAuditData(Integer id, String operation) {

    }
}
