package com.welab.databridge.service;

import com.welab.anti.fraud.vo.VoiceSendVo;
import com.welab.databridge.common.ResultCode;
import com.welab.databridge.entity.AudioTemp;
import com.welab.databridge.dto.AudioTempDto;
import com.welab.databridge.vo.audio.CollectionRecordVo;
import com.wolaidai.approval.model.voice.VoiceSendDetail;

import java.util.List;

/**
 * (AudioTemp)表服务接口
 * (代码生成器自动生成)
 *
 * <AUTHOR>
 * @date 2023-03-17 14:46:35
 */
public interface AudioTempService {

    /**
     * 根据ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    AudioTemp getById(Integer id);

    /**
     * 根据IDs查询数据
     *
     * @param ids 主键
     * @return 实例对象
     */
    List<AudioTemp> getByIds(List<Integer> ids);

    /**
     * 列表条件查询
     *
     * @param dto
     * @return
     */
    List<AudioTemp> queryByCondition(AudioTempDto dto);

    /**
     * 新增数据
     *
     * @param bean 实例对象
     * @return ResultCode
     */
    ResultCode insert(AudioTemp bean);

    /**
     * 修改数据
     *
     * @param dto 实例对象
     * @return ResultCode
     */
    ResultCode update(AudioTempDto dto);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return ResultCode
     */
    ResultCode deleteById(Integer id);

    /**
     * 保存从审批获取的通话信息
     *
     * @param list
     */
    void saveApprovalData(List<VoiceSendDetail> list, String queryDate);

    /**
     * 保存从反欺诈获取的通话信息
     *
     * @param list
     */
    void saveFraudData(List<VoiceSendVo> list, String queryDate);

    /**
     * 保存从客服和催收获取的通话信息
     *
     * @param list
     */
    void saveCSAndCollectionData(List<CollectionRecordVo> list, String queryDate);

    void saveRecord(List<AudioTemp> audioTempList);

    void updateStatus(List<AudioTemp> audioTempList, String status);

    void updateStatus(String queryDate, String status);
}
