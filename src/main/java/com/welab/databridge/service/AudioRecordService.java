package com.welab.databridge.service;

import com.github.pagehelper.PageInfo;
import com.welab.databridge.common.ResponseVo;
import com.welab.databridge.common.ResultCode;
import com.welab.databridge.dto.AudioRecordDto;
import com.welab.databridge.entity.AudioRecord;
import com.welab.databridge.vo.audio.QueryVo;
import com.welab.databridge.vo.common.BatchVo;

import java.util.List;

/**
 * 音频记录(AudioRecord)表服务接口
 * (代码生成器自动生成)
 *
 * <AUTHOR>
 * @date 2023-01-03 14:29:53
 */
public interface AudioRecordService {

    /**
     * 根据ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    AudioRecord getById(Integer id);

    /**
     * 列表条件查询
     *
     * @param dto
     * @return
     */
    PageInfo<AudioRecord> queryByCondition(AudioRecordDto dto);

    /**
     * 新增数据
     *
     * @param dto 实例对象
     * @return ResultCode
     */
    ResultCode insert(AudioRecordDto dto);

    /**
     * 修改数据
     *
     * @param dto 实例对象
     * @return ResultCode
     */
    ResultCode update(AudioRecordDto dto);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return ResultCode
     */
    ResultCode deleteById(Integer id);

    /**
     * 下载音频
     *
     * @param batchVo
     * @return ResultCode
     */
    ResponseVo download(BatchVo batchVo);

    /**
     * 查询语音所属部门列表
     *
     * @return
     */
    List<String> listDept();

    /**
     * 查询语音所属机构列表
     *
     * @return
     */
    List<String> listOrg();

    /**
     * 获取语音连接
     *
     * @return
     */
    ResponseVo getUrl(Integer id, String userid);

    ResponseVo modifyAudioStatus(Integer id, String userid);

    /**
     * 手动触发单日录音数据查询
     * @param queryVo
     * @return
     */
    ResponseVo queryRecord(QueryVo queryVo);

    void processTodayData();

    void processHistoryData();

    /**
     * 脱敏导出
     *
     * @param dto
     */
    void export(AudioRecordDto dto);

    void saveRecord(List<AudioRecord> list);
}
