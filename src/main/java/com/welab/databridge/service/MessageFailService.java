package com.welab.databridge.service;

import com.welab.databridge.common.ResponseVo;
import com.welab.databridge.common.ResultCode;
import com.welab.databridge.dto.MessageFailDto;
import com.welab.databridge.entity.MessageFail;

import java.util.List;

/**
 * 短信上传失败记录(MessageFail)表服务接口
 * (代码生成器自动生成)
 *
 * <AUTHOR>
 * @date 2023-01-03 14:29:42
 */
public interface MessageFailService {

    /**
     * 根据ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    MessageFail getById(Integer id);

    /**
     * 列表条件查询
     *
     * @param dto
     * @return
     */
    List<MessageFail> queryByCondition(MessageFailDto dto);

    /**
     * 新增数据
     *
     * @param dto 实例对象
     * @return ResultCode
     */
    ResultCode insert(MessageFailDto dto);

    /**
     * 修改数据
     *
     * @param dto 实例对象
     * @return ResultCode
     */
    ResultCode update(MessageFailDto dto);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return ResultCode
     */
    ResultCode deleteById(Integer id);

    /**
     * 导出筛选数据
     *
     * @param dto 导出条件
     */
    void export(MessageFailDto dto);
}
