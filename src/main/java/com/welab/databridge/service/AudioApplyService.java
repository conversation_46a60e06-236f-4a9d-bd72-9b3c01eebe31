package com.welab.databridge.service;

import com.welab.databridge.common.ResponseVo;
import com.welab.databridge.common.ResultCode;
import com.welab.databridge.dto.AudioApplyDto;
import com.welab.databridge.entity.AudioApply;
import com.welab.databridge.vo.audit.CallbackParam;

import java.util.List;

/**
 * (AudioApply)表服务接口
 * (代码生成器自动生成)
 *
 * <AUTHOR>
 * @date 2023-03-01 18:07:58
 */
public interface AudioApplyService {

    /**
     * 根据ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    AudioApply getById(Integer id);

    /**
     * 列表条件查询
     *
     * @param dto
     * @return
     */
    List<AudioApply> queryByCondition(AudioApplyDto dto);

    /**
     * 新增数据
     *
     * @param dto 实例对象
     * @return ResultCode
     */
    ResultCode insert(AudioApplyDto dto);

    /**
     * 修改数据
     *
     * @param dto 实例对象
     * @return ResultCode
     */
    ResultCode update(AudioApplyDto dto);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return ResultCode
     */
    ResultCode deleteById(Integer id);

    /**
     * 审核回调
     *
     * @param callbackParam 回调参数
     * @return ResponseVo
     */
    ResponseVo callback(CallbackParam callbackParam);

    void process(AudioApply audioApply, CallbackParam callbackParam);
}
