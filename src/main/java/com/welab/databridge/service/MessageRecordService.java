package com.welab.databridge.service;

import com.github.pagehelper.PageInfo;
import com.welab.databridge.common.ResponseVo;
import com.welab.databridge.common.ResultCode;
import com.welab.databridge.dto.MessageRecordDto;
import com.welab.databridge.entity.MessageRecord;

import java.util.List;

/**
 * 短信记录(MessageRecord)表服务接口
 * (代码生成器自动生成)
 *
 * <AUTHOR>
 * @date 2023-01-03 14:29:50
 */
public interface MessageRecordService {

    /**
     * 根据ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    MessageRecord getById(Integer id);

    /**
     * 列表条件查询
     *
     * @param dto
     * @return
     */
    PageInfo<MessageRecord> queryByCondition(MessageRecordDto dto);

    /**
     * 新增数据
     *
     * @param dto 实例对象
     * @return ResultCode
     */
    ResultCode insert(MessageRecordDto dto);

    /**
     * 修改数据
     *
     * @param dto 实例对象
     * @return ResultCode
     */
    ResultCode update(MessageRecordDto dto);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return ResultCode
     */
    ResultCode deleteById(Integer id);

    /**
     * 导出筛选数据
     *
     * @param dto 导出条件
     * @return ResultCode
     */
    void export(MessageRecordDto dto);
}
