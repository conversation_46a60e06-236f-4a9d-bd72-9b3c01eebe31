package com.welab.databridge.service;

import com.welab.databridge.common.ResultCode;
import com.welab.databridge.entity.DataHistory;
import com.welab.databridge.dto.DataHistoryDto;

import java.util.List;

/**
 * 历史版本(DataHistory)表服务接口
 * (代码生成器自动生成)
 *
 * <AUTHOR>
 * @date 2023-02-02 15:53:02
 */
public interface DataHistoryService {

    /**
     * 根据ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    DataHistory getById(Integer id);

    /**
     * 列表条件查询
     *
     * @param dto
     * @return
     */
    List<DataHistory> queryByCondition(DataHistoryDto dto);

    /**
     * 新增数据
     *
     * @param dto 实例对象
     * @return ResultCode
     */
    ResultCode insert(DataHistoryDto dto);

    /**
     * 修改数据
     *
     * @param dto 实例对象
     * @return ResultCode
     */
    ResultCode update(DataHistoryDto dto);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return ResultCode
     */
    ResultCode deleteById(Integer id);

}
