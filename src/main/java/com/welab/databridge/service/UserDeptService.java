package com.welab.databridge.service;

import com.welab.databridge.common.ResultCode;
import com.welab.databridge.entity.UserDept;
import com.welab.databridge.dto.UserDeptDto;

import java.util.List;

/**
 * 用户部门关系表(UserDept)表服务接口
 * (代码生成器自动生成)
 *
 * <AUTHOR>
 * @date 2023-01-03 14:29:56
 */
public interface UserDeptService {

    /**
     * 根据ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    UserDept getById(Integer id);

    /**
     * 列表条件查询
     *
     * @param dto
     * @return
     */
    List<UserDept> queryByCondition(UserDeptDto dto);

    /**
     * 新增数据
     *
     * @param dto 实例对象
     * @return ResultCode
     */
    ResultCode insert(UserDeptDto dto);

    /**
     * 修改数据
     *
     * @param dto 实例对象
     * @return ResultCode
     */
    ResultCode update(UserDeptDto dto);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return ResultCode
     */
    ResultCode deleteById(Integer id);

    UserDept getDeptByName(String name);
}
