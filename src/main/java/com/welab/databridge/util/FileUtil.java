package com.welab.databridge.util;

import com.aliyun.oss.model.OSSObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;

import java.io.*;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.zip.*;

@Slf4j
public class FileUtil {
    /**
     * 生成zip文件
     *
     * @param ossObjectList
     * @return
     * @throws IOException
     */
    public static File generateZipFileByOSSObject(List<OSSObject> ossObjectList) throws IOException {
        File zipFile = File.createTempFile("download", ".zip");
        FileOutputStream f = new FileOutputStream(zipFile);
        /**
         * 作用是为任何OutputStream产生校验和
         * 第一个参数是制定产生校验和的输出流，第二个参数是指定Checksum的类型 （Adler32（较快）和CRC32两种）
         */
        CheckedOutputStream csum = new CheckedOutputStream(f, new Adler32());
        // 用于将数据压缩成Zip文件格式
        try (ZipOutputStream zos = new ZipOutputStream(csum)) {
            List<String> fileNames = new ArrayList<>();
            String fileName;
            for (OSSObject ossObject : ossObjectList) {
                fileName = ossObject.getKey().split("/")[5];
                if (fileNames.contains(fileName)) {
                    continue;
                }
                fileNames.add(fileName);
                // 对于每一个要被存放到压缩包的文件，都必须调用ZipOutputStream对象的putNextEntry()方法，确保压缩包里面文件不同名
                zos.putNextEntry(new ZipEntry(fileName));
                try (InputStream inputStream = ossObject.getObjectContent()) {
                    byte[] buffer = new byte[8192];
                    int bytesRead;
                    // 向压缩文件中输出数据
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        zos.write(buffer, 0, bytesRead);
                    }
                }

                zos.closeEntry(); // 当前文件写完，定位为写入下一条项目
            }
        } catch (Exception e) {
            log.error("generateZipFileByOSSObject error: {}", e.getMessage());
        }
        return zipFile;
    }

    /**
     * 生成zip文件
     *
     * @param fileList
     * @return
     * @throws IOException
     */
    public static File generateZipFile(List<File> fileList) throws IOException {
        File zipFile = File.createTempFile("download", ".zip");
        FileOutputStream f = new FileOutputStream(zipFile);
        /**
         * 作用是为任何OutputStream产生校验和
         * 第一个参数是制定产生校验和的输出流，第二个参数是指定Checksum的类型 （Adler32（较快）和CRC32两种）
         */
        CheckedOutputStream csum = new CheckedOutputStream(f, new Adler32());
        // 用于将数据压缩成Zip文件格式
        try (ZipOutputStream zos = new ZipOutputStream(csum)) {
            List<String> fileNames = new ArrayList<>();
            String fileName;
            for (File file : fileList) {
                fileName = file.getName();
                if (fileNames.contains(fileName)) {
                    continue;
                }
                fileNames.add(fileName);
                // 对于每一个要被存放到压缩包的文件，都必须调用ZipOutputStream对象的putNextEntry()方法，确保压缩包里面文件不同名
                zos.putNextEntry(new ZipEntry(fileName));

                try (InputStream inputStream = new FileInputStream(file)) {
                    byte[] buffer = new byte[8192];
                    int bytesRead;
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        zos.write(buffer, 0, bytesRead);
                    }
                }
                zos.closeEntry(); // 当前文件写完，定位为写入下一条项目
            }
        } catch (Exception e) {
            log.error("error: " + e.getMessage());
        }

        return zipFile;
    }

//    public static void unzip(String zipFilePath, String destPath) {
//        File destDir = new File(destPath);
//        if (!destDir.exists()) {
//            destDir.mkdir();
//        }
//
//        try (FileInputStream fis = new FileInputStream(zipFilePath);
//             BufferedInputStream bis = new BufferedInputStream(fis);
//             ZipInputStream zis = new ZipInputStream(bis)) {
//            ZipEntry entry;
//            while ((entry = zis.getNextEntry()) != null) {
//                String entryName = entry.getName();
//                String filePath = destPath + File.separator + entryName;
//
//                if (entry.isDirectory()) {
//                    File dir = new File(filePath);
//                    dir.mkdirs();
//                } else {
//                    try (BufferedOutputStream bos = new BufferedOutputStream(new FileOutputStream(filePath))) {
//                        byte[] buffer = new byte[1024];
//                        int bytesRead;
//                        while ((bytesRead = zis.read(buffer)) != -1) {
//                            bos.write(buffer, 0, bytesRead);
//                        }
//                    }
//                }
//                zis.closeEntry();
//            }
//        } catch (FileNotFoundException e) {
//            log.error("unzip error: {}", e.getMessage());
//        } catch (IOException e) {
//            log.error("unzip error: {}", e.getMessage());
//        }
//    }

    public static File getFileFromNet(String path) throws IOException {
        //对本地文件命名，path是http的完整路径，主要得到资源的名字
        String newUrl = path;
        newUrl = newUrl.split("[?]")[0];
        String[] bb = newUrl.split("/");
        //得到最后一个分隔符后的名字
        String fileName = bb[bb.length - 1];
        File file = new File(FilenameUtils.getName(fileName));

        //下载
        URL urlfile = new URL(path);
        try (
                InputStream inputStream = urlfile.openStream();
                OutputStream outputStream = new FileOutputStream(file);
                ) {
            int bytesRead = 0;
            byte[] buffer = new byte[8192];
            while ((bytesRead=inputStream.read(buffer,0,8192))!=-1) {
                outputStream.write(buffer, 0, bytesRead);
            }
        }catch (Exception e) {
            log.error("getFileFromNet error: {}", e.getMessage());
        }
        return file;
    }

}
