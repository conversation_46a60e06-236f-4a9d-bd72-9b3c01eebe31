package com.welab.databridge.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import java.net.URLDecoder;
import java.net.URLEncoder;
import java.security.SecureRandom;
import java.text.MessageFormat;
import java.util.Random;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @Description 字符工具类
 * @Date 2022/3/14 下午11:33
 */
@Slf4j
public class StringUtil {
    private static final Pattern MATCH_PHONENUMBER = Pattern.compile("^((\\+86)|(86))?1[3456789]\\d{9}$");
    /**
     * 匹配中文汉字
     */
    private static final Pattern PATTERN_MATCH_CHINESE_CHARACTER = Pattern.compile("[\\u4e00-\\u9fa5]");

    private static final Pattern NUMBER = Pattern.compile("[0-9]*");


    /**
     * 将%s占位符进行替换
     * @param str 举例: 您的验证码是%s,该验证码%s分钟内有效,请勿泄露与他人!
     * @param arguments 举例: 8888,3
     * @return 您的验证码是8888,该验证码3分钟内有效,请勿泄露与他人!
     */
    public static String formatStr(String str, Object ... arguments) {
        return String.format(str, arguments);
    }

    /**
     * 将{n}占位符进行替换
     * @param str 您的验证码是{0},该验证码{1}分钟内有效,请勿泄露与他人!
     * @param arguments 8888,5
     * @return 您的验证码是8888,该验证码5分钟内有效,请勿泄露与他人!
     */
    public static String format(String str, Object ... arguments) {
        return MessageFormat.format(str,arguments);
    }

    /**
     * 检测数字是否大于0
     * @param value
     * @return 大于返回true
     */
    public static boolean isGreaterThanZero (Integer value) {
       return isGreaterThanNumberB(value, 0);
    }

    /**
     * 检测数字numberA是否大于numberB
     * @param numberA 数字A
     * @param numberB 数字B
     * @return numberA > numberB 则返回true
     */
    public static boolean isGreaterThanNumberB (Integer numberA, Integer numberB) {
        if(null == numberA || null == numberB) {
            return false;
        }
        if(numberA > numberB) {
            return true;
        }
        return false;
    }

    /**
     * 比较2个数字是否相等
     * @param numberA
     * @param numberB
     * @return 相等返回true 不相等返回false
     */
    public static boolean isEqualsNumber(Integer numberA, Integer numberB) {
        if (null == numberA && null == numberB) {
            return true;
        }
        if((numberA+"").equals((numberB+"")) ) {
            return true;
        }
        return false;
    }

    /**
     * 比较2个数字不相等
     * @param numberA
     * @param numberB
     * @return 不相等返回true 相等返回false
     */
    public static boolean isNotEqualsNumber(Integer numberA, Integer numberB) {
        return !isEqualsNumber(numberA,numberB);
    }

	public static boolean isEmpty(String str) {
		if (null == str || str.trim().length() < 1) {
            return true;
        }
		return false;
	}

	public static boolean isNotEmpty(String str) {
		return !isEmpty(str);
	}
    
    /**
     * 检查手机号码格式正确性
     *
     * @param phoneNumber
     * @return 正确返回true 不正常返回false
     */
    public static boolean checkPhoneNumber(String phoneNumber) {
        if (phoneNumber != null && phoneNumber.length() > 10 && phoneNumber.length() < 15) {
            Matcher matcher = MATCH_PHONENUMBER.matcher(phoneNumber);
            if (matcher.find()) {
                return true;
            }
        }
        return false;
    }

    /**
     * 手机号加掩码
     * @param phone
     * @return
     */
    public static String maskedPhone(String phone) {
        if (isEmpty(phone) || phone.length() < 11) {
            return phone;
        }
        return phone.substring(0, 3) + "****" + phone.substring(7, phone.length());
    }

    public static String clearEmoji(String str) {
        return clearEmoji(str, "@");
    }

    public static String clearEmoji(String str, String sub) {
        if (isEmpty(str)) {
            return "";
        }
        str = str.replaceAll("[\ud83c\udc00-\ud83c\udfff]|[\ud83d\udc00-\ud83d\udfff]|[\u2600-\u27ff]", sub);
        try {
            str = new String(str.getBytes("GBK"), "GBK");
        } catch (Exception e) {
            log.error("clearEmoji error: {}", e.getMessage());
        }
        return str;
    }

    /**
     * 利用正则表达式判断字符串是否是整数(>=0的数)
     * @param str
     * @return true整数 false不是整数
     */
    public static boolean isNumeric(String str) {
        if(StringUtil.isEmpty(str)) {
            return false;
        }
        Pattern pattern = NUMBER;
        Matcher isNum = pattern.matcher(str);
        if (!isNum.matches()) {
            return false;
        }
        return true;
    }

    /**
     * 对中文字符进行转码
     *
     * @param s
     * @return String
     */
    public static String encodeString(String s) {
        try {
            return URLEncoder.encode(s, "UTF-8");
        } catch (Exception e) {
            return null;
        }
    }

    public static String decodeString(String s) {
        try {
            return URLDecoder.decode(s, "UTF-8");
        } catch (Exception e) {
            return null;
        }
    }

//    public static double toDouble(String str) {
//        return toDouble(str, 0);
//    }
//
//    public static double toDouble(String str, double defaultValue) {
//        double result = defaultValue;
//        try {
//            if (!isEmpty(str)) {
//                result = Double.parseDouble(str);
//            }
//        } catch (Exception e) {
//
//        }
//        return result;
//    }

    /**
     * 判断字符是否是汉字
     */
    public static boolean isChineseCharacter(char c) {
        Matcher matcher = PATTERN_MATCH_CHINESE_CHARACTER.matcher(String.valueOf(c));
        return matcher.matches();
    }

    /**
     * 生成UUID
     * @return
     */
    public static String getUUID() {
        return UUID.randomUUID().toString().replace("-","");
    }

    /**
     * 生成随机数
     * @param digit 位数
     * @return
     */
    public static String random(int digit){
        digit = digit < 1 ? 4 : digit;
        Random random = getRandomInstance();
        StringBuilder sb = new StringBuilder();
       for(int i = 0; i < digit; i++) {
            sb.append(random.nextInt(10));
       }
       return sb.toString();
    }

    /**
     * 随机生成正整数
     * @param digit 位数
     * @return
     */
    public static String randomPositiveInteger(int digit){
        digit = digit < 1 ? 4 : digit;
        Random random = getRandomInstance();
        StringBuilder sb = new StringBuilder();
        sb.append(random.nextInt(9)+1);
       for(int i = 1; i < digit; i++) {
            sb.append(random.nextInt(10));
       }
       return sb.toString();
    }

    @SneakyThrows
    public static Random getRandomInstance() {
        return SecureRandom.getInstanceStrong();
    }

    public static boolean isJSON(String str) {
        boolean result = false;
        try {
            JSONObject obj= JSON.parseObject(str);
            result = true;
        } catch (Exception e) {
            result=false;
        }
        return result;
    }

    public static String unicodeToString(String str) {
        Pattern pattern = Pattern.compile("(\\\\u(\\p{XDigit}{4}))");
        Matcher matcher = pattern.matcher(str);
        char ch;
        while (matcher.find()) {
            ch = (char) Integer.parseInt(matcher.group(2), 16);
            str = str.replace(matcher.group(1), ch + "");
        }
        return str;
    }
}
