package com.welab.databridge.util;

import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletRequest;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.Optional;

/**
 * ip地址工具类
 * <AUTHOR>
 * @date 2022/5/7 01:33
 */
@Slf4j
public class IpUtil {
    public final static String UNKNOWN = "unknown";
    public final static String LOCAL_HOST = "127.0.0.1";
    public final static String SPLIT_CHAR = ",";

    public static String getIpAddress(HttpServletRequest request) {
        String ipAddress = null;
        try {
            ipAddress = request.getHeader("x-forwarded-for");
            if (ipAddress == null || ipAddress.length() == 0 || UNKNOWN.equalsIgnoreCase(ipAddress)) {
                ipAddress = request.getHeader("Proxy-Client-IP");
            }
            if (ipAddress == null || ipAddress.length() == 0 || UNKNOWN.equalsIgnoreCase(ipAddress)) {
                ipAddress = request.getHeader("WL-Proxy-Client-IP");
            }
            if (ipAddress == null || ipAddress.length() == 0 || UNKNOWN.equalsIgnoreCase(ipAddress)) {
                ipAddress = request.getRemoteAddr();
                if (ipAddress.equals(LOCAL_HOST)) {
                    // 根据网卡取本机配置的IP
                    InetAddress inet = null;
                    try {
                        inet = InetAddress.getLocalHost();
                    } catch (UnknownHostException e) {
                        log.error("InetAddress getLocalHost error: {}", e.getMessage());
                    }
                    ipAddress = Optional.ofNullable(inet)
                            .map(i -> i.getHostAddress())
                            .orElse("");
                }
            }
            // 对于通过多个代理的情况，第一个IP为客户端真实IP,多个IP按照','分割
            if (ipAddress != null && ipAddress.length() > 15) { 
                if (ipAddress.indexOf(SPLIT_CHAR) > 0) {
                    ipAddress = ipAddress.substring(0, ipAddress.indexOf(SPLIT_CHAR));
                }
            }
        } catch (Exception e) {
            ipAddress = "";
        }
        return ipAddress;
    }
}
