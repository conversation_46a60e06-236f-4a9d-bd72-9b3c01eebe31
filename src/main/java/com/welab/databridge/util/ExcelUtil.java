package com.welab.databridge.util;

import cn.hutool.poi.excel.ExcelWriter;
import com.welab.databridge.annotation.ExportField;
import com.welab.databridge.annotation.TemplateField;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.util.ReflectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.beans.IntrospectionException;
import java.beans.PropertyDescriptor;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class ExcelUtil {
    private ExcelUtil() {

    }

    /**
     * 构建导出文件
     *
     * @param list 原数据
     * @param <T> 原数据类型
     * @return
     * @throws IntrospectionException
     */
    public static <T> ExcelWriter exportBuilder(List<T> list) throws IntrospectionException {
        List<List<Object>> header = new ArrayList<>();
        List<Map<String, Object>> rowList = new ArrayList<>();
        //构造表头
        buildExportHeader(list.get(0).getClass(), header);
        //构造数据
        buildExcelRow(list, rowList);
        // 生成Excel
        ExcelWriter writer = cn.hutool.poi.excel.ExcelUtil.getWriter();
        writer.getStyleSet().setAlign(HorizontalAlignment.LEFT, VerticalAlignment.CENTER);
        writer.setColumnWidth(-1, 20);
        writer.setRowHeight(-1, 20);

        // 明细
        List<Object> first = header.get(0);

        writer.writeHeadRow(first);

        writer.write(rowList.stream().map(Map::values).collect(Collectors.toList()));

        for (int i = 0; i < writer.getCurrentRow(); i++) {
            writer.getOrCreateRow(i).setHeightInPoints(20);
        }

        return writer;
    }

    /**
     * 构建导出文件
     *
     * @param clazz 数据类型
     * @return
     * @throws IntrospectionException
     */
    public static ExcelWriter exportEmptyBuilder(Class<?> clazz) {
        List<List<Object>> header = new ArrayList<>();
        //构造表头
        buildExportHeader(clazz, header);

        // 生成Excel
        ExcelWriter writer = cn.hutool.poi.excel.ExcelUtil.getWriter();
        writer.getStyleSet().setAlign(HorizontalAlignment.LEFT, VerticalAlignment.CENTER);
        writer.setColumnWidth(-1, 20);
        writer.setRowHeight(-1, 20);

        // 明细
        List<Object> first = header.get(0);

        writer.writeHeadRow(first);

        return writer;
    }

    public static ExcelWriter templateBuilder(Class<?> clazz) {
        List<List<Object>> headers = new ArrayList<>();
        buildTemplateHeader(clazz, headers);
        ExcelWriter writer = cn.hutool.poi.excel.ExcelUtil.getWriter();
        writer.getStyleSet().setAlign(HorizontalAlignment.LEFT, VerticalAlignment.CENTER);
        for (List<Object> header : headers) {
            writer.writeHeadRow(header);
        }
        writer.setRowHeight(-1, 20);
        writer.setColumnWidth(-1, 20);
        return writer;
    }

    /**
     * 构建导出文件的表头
     *
     * @param clazz 要导出的数据类型
     * @param headers 表头
     * @return
     * @throws IntrospectionException
     */
    public static void buildExportHeader(Class<?> clazz, List<List<Object>> headers) {
        Field[] fields = clazz.getDeclaredFields();
        List<Object> first = new ArrayList<>();
        for (Field field : fields) {
            if (field.isAnnotationPresent(ExportField.class)) {
                first.add(field.getAnnotation(ExportField.class).name());
            }
        }
        headers.add(first);
    }

    /**
     * 构建导出文件的表头
     *
     * @param clazz 要导出的数据类型
     * @param headers 表头，目前是中、文两行
     * @return
     * @throws IntrospectionException
     */
    public static void buildTemplateHeader(Class<?> clazz, List<List<Object>> headers) {
        Field[] fields = clazz.getDeclaredFields();
        List<Object> first = new ArrayList<>();
        List<Object> second = new ArrayList<>();
        for (Field field : fields) {
            if (field.isAnnotationPresent(TemplateField.class)) {
                first.add(field.getName());
                second.add(field.getAnnotation(TemplateField.class).name());
            }
        }
        headers.add(first);
        headers.add(second);
    }

    /**
     * 构建导出文件的数据
     *
     * @param list 要导出的原始数据
     * @param rowList 根据原始数据构造的行数据
     * @return
     * @throws IntrospectionException
     */
    public static <T> void buildExcelRow(List<T> list, List<Map<String, Object>> rowList) throws IntrospectionException {
        Field[] fields = list.get(0).getClass().getDeclaredFields();
        PropertyDescriptor pd;
        Method getMethod;

        for (int i=0; i<list.size(); i++) {
            T obj = list.get(i);
            Map<String, Object> row = new LinkedHashMap<>();
            for (Field field : fields) {
                if (field.isAnnotationPresent(ExportField.class)) {
                    pd = new PropertyDescriptor(field.getName(), obj.getClass());
                    getMethod = pd.getReadMethod();//获得get方法
                    Object fieldValue = ReflectionUtils.invokeMethod(getMethod, obj);
                    row.put(field.getName(), fieldValue == null ? null : fieldValue.toString());
                }
            }
            rowList.add(row);
        }
    }

    /**
     * 响应到客户端
     * @param response
     * @param fileName
     * @param wb
     */
    public static void export(HttpServletResponse response, String fileName, Workbook wb) {
        try {
            setResponseHeader(response, fileName);
            OutputStream os = response.getOutputStream();
            wb.write(os);
            os.flush();
            os.close();
        } catch (Exception e) {
            log.error("export error: {}", e.getMessage());
        }
    }

    /**
     * 设置响应头
     */
    public static void setResponseHeader(HttpServletResponse response, String fileName) {
        try {
            fileName = new String(fileName.getBytes(), StandardCharsets.ISO_8859_1);
            response.setContentType("application/octet-stream;charset=ISO8859-1");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            response.addHeader("Pargam", "no-cache");
            response.addHeader("Cache-Control", "no-cache");
        } catch (Exception ex) {
            log.error("setResponseHeader error: {}", ex.getMessage());
        }
    }
}
