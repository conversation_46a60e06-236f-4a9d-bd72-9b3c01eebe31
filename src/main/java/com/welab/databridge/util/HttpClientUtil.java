package com.welab.databridge.util;

import com.alibaba.fastjson.JSONObject;
import org.apache.http.Header;
import org.apache.http.HttpResponse;
import org.apache.http.client.fluent.Request;
import org.apache.http.entity.ContentType;
import org.apache.http.message.BasicHeader;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.nio.charset.StandardCharsets;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @Description Http工具类
 * @Date 2022/3/15 下午2:08
 */
public class HttpClientUtil {

    private static final Logger LOG = LoggerFactory.getLogger(HttpClientUtil.class);

    public static final int TIMEOUT = 10000;

    public static final String ENCODING = "UTF-8";

    private static boolean isNullStr(String value) {
        if (null == value || "".equals(value)) {
            return true;
        }
        else {
            return false;
        }
    }

    /**
     * 发送POST请求,参数值以body形式发送
     *
     * @param url      请求的服务器地址,如: http://localhost/xmlTest.do
     * @param bodyData 需发送的Request Body数据,如: 我们要以流发送的数据...
     * @return 返回服务器的响应
     * @throws IOException
     */
    public static String send(String url, String bodyData) throws IOException {
        return send(url, null, null, bodyData);
    }

    /**
     * 发送请求[设置请求头],参数值以body形式发送
     *
     * @param url            请求服务器地址
     * @param requestHeaders 请求头部值 key-value 如(头部有2个键值):
     *                       key=Content-Type value=application/json
     *                       key=Authorization value=bay dasfdfasddasf
     *                       为null默认为: "application/octet-stream"
     * @param bodyData       需发送的Request Body数据
     * @return URL所代表远程资源的响应结果
     * @throws IOException
     */
    public static String send(String url, Map<String, String> requestHeaders, String bodyData) throws IOException {
        return send(url, null, requestHeaders, bodyData);
    }

    /**
     * 发送请求[设置请求方式GET POST PUT DELETE|设置请求头],参数值以body形式发送
     *
     * @param url            请求服务器地址
     * @param requestMethod  请求方式: GET POST PUT DELETE HEAD OPTIONSTRACE
     * @param requestHeaders 请求头部值 key-value 如(头部有2个键值):
     *                       key=Content-Type value=application/json
     *                       key=Authorization value=bay dasfdfasddasf
     *                       为null默认为: "application/octet-stream"
     * @param bodyData       需发送的Request Body数据
     * @return URL所代表远程资源的响应结果
     * @throws IOException
     */
    public static InputStream sendWithReturnInputstream(String url, String requestMethod, Map<String, String> requestHeaders, String bodyData) throws IOException {
        LOG.debug("[发送请求]请求地址:" + url);
        long start = System.currentTimeMillis();
        if (null == url || "".equals(url)) {
            throw new NullPointerException("请求的地址不能为空!");
        }
        bodyData = (null == bodyData || "".equals(bodyData)) ? "0" : bodyData;
        HttpURLConnection httpURLConnection;
        // 建立链接
        URL gatewayUrl = new URL(url);
        httpURLConnection = (HttpURLConnection) gatewayUrl.openConnection();

        // 设置连接属性
        httpURLConnection.setDoOutput(true);
        httpURLConnection.setDoInput(true);
        httpURLConnection.setUseCaches(false);
        requestMethod = isNullStr(requestMethod) ? "POST" : requestMethod;
        httpURLConnection.setRequestMethod(requestMethod);
        // 获得数据字节数据，请求数据流的编码，必须和下面服务器端处理请求流的编码一致
        byte[] requestStringBytes = bodyData.getBytes(ENCODING);

        // 设置请求属性
        httpURLConnection.setRequestProperty("Content-length", "" + requestStringBytes.length);
        httpURLConnection.setRequestProperty("Content-Type", "application/json;charset=UTF-8");
        if (null != requestHeaders && requestHeaders.size() > 0) {
            Set<String> entries = requestHeaders.keySet();
            if (entries != null) {
                Iterator<String> iterator = entries.iterator();
                while (iterator.hasNext()) {
                    String key = iterator.next();
                    String value = requestHeaders.get(key);
                    httpURLConnection.setRequestProperty(key, value);
                }
            }
        }
        // 建立输出流，并写入数据
        OutputStream outputStream = httpURLConnection.getOutputStream();
        outputStream.write(requestStringBytes);
        outputStream.close();
        // 获取所有响应头字段
        Map<String, List<String>> map = httpURLConnection.getHeaderFields();
        // 遍历所有的响应头字段
        StringBuffer sb = new StringBuffer();
        for (String key : map.keySet()) {
            sb.append(key + "=>" + map.get(key) + "\r\t");
        }
        LOG.debug("[发送请求]响应头:" + sb);

        // 获得响应状态
        int responseCode = httpURLConnection.getResponseCode();
        LOG.info("[发送请求]响应状态码:" + responseCode);
        LOG.info("send url:{},spends:{}ms",url,System.currentTimeMillis()-start);
        if (HttpURLConnection.HTTP_OK == responseCode) {
            return httpURLConnection.getInputStream();
        }
        return null;
    }

    /**
     * 发送请求[设置请求方式GET POST PUT DELETE|设置请求头],参数值以body形式发送
     *
     * @param url            请求服务器地址
     * @param requestMethod  请求方式: GET POST PUT DELETE HEAD OPTIONSTRACE
     * @param requestHeaders 请求头部值 key-value 如(头部有2个键值):
     *                       key=Content-Type value=application/json
     *                       key=Authorization value=bay dasfdfasddasf
     *                       为null默认为: "application/octet-stream"
     * @param bodyData       需发送的Request Body数据
     * @return URL所代表远程资源的响应结果
     * @throws IOException
     */
    public static String send(String url, String requestMethod, Map<String, String> requestHeaders, String bodyData) throws IOException {
        LOG.debug("[发送请求]请求地址:" + url);
        long start = System.currentTimeMillis();
        if (null == url || "".equals(url)) {
            throw new NullPointerException("请求的地址不能为空!");
        }
        bodyData = (null == bodyData || "".equals(bodyData)) ? "0" : bodyData;
        HttpURLConnection httpURLConnection;
        // 建立链接
        URL gatewayUrl = new URL(url);
        httpURLConnection = (HttpURLConnection) gatewayUrl.openConnection();

        // 设置连接属性
        httpURLConnection.setDoOutput(true);
        httpURLConnection.setDoInput(true);
        httpURLConnection.setUseCaches(false);
        requestMethod = isNullStr(requestMethod) ? "POST" : requestMethod;
        httpURLConnection.setRequestMethod(requestMethod);
        // 获得数据字节数据，请求数据流的编码，必须和下面服务器端处理请求流的编码一致
        byte[] requestStringBytes = bodyData.getBytes(ENCODING);

        // 设置请求属性
        httpURLConnection.setRequestProperty("Content-length", "" + requestStringBytes.length);
        httpURLConnection.setRequestProperty("Content-Type", "application/json;charset=UTF-8");
        if (null != requestHeaders && requestHeaders.size() > 0) {
            Set<String> entries = requestHeaders.keySet();
            if (entries != null) {
                Iterator<String> iterator = entries.iterator();
                while (iterator.hasNext()) {
                    String key = iterator.next();
                    String value = requestHeaders.get(key);
                    httpURLConnection.setRequestProperty(key, value);
                }
            }
        }
        // 建立输出流，并写入数据
        OutputStream outputStream = httpURLConnection.getOutputStream();
        outputStream.write(requestStringBytes);
        outputStream.close();
        // 获取所有响应头字段
        Map<String, List<String>> map = httpURLConnection.getHeaderFields();
        // 遍历所有的响应头字段
        StringBuffer sb = new StringBuffer();
        for (String key : map.keySet()) {
            sb.append(key + "=>" + map.get(key) + "\r\t");
        }
        LOG.debug("[发送请求]响应头:" + sb);

        // 获得响应状态
        int responseCode = httpURLConnection.getResponseCode();
        StringBuffer responseBuffer = new StringBuffer();
        if (HttpURLConnection.HTTP_OK == responseCode) {
            String readLine;
            BufferedReader responseReader;
            // 处理响应流，必须与服务器响应流输出的编码一致
            responseReader = new BufferedReader(new InputStreamReader(
                    httpURLConnection.getInputStream(), ENCODING));
            while ((readLine = responseReader.readLine()) != null) {
                responseBuffer.append(readLine).append("\n");
            }
            responseReader.close();
        } else {
            responseBuffer.append(responseCode);
        }
        LOG.debug("[发送请求]响应状态码:" + responseCode + " 响应内容:" + responseBuffer);
        LOG.debug("send url:{},spends:{}ms",url,System.currentTimeMillis()-start);
        return responseBuffer.toString();
    }

    public static String postWithHeader(String url, String method, String data, int timeOut, Header... headers) {
        LOG.info("post url: " + url + ", method: " + method);
        if (StringUtil.isEmpty(url) || StringUtil.isEmpty(data)) {
            return null;
        }
        Request request;
        if ("post".equalsIgnoreCase(method)) {
            request = Request.Post(url).bodyString(data, ContentType.APPLICATION_JSON).addHeader(new BasicHeader("Accept", ContentType.APPLICATION_JSON.toString()));
        } else {
            request = Request.Put(url).bodyString(data, ContentType.APPLICATION_JSON).addHeader(new BasicHeader("Accept", ContentType.APPLICATION_JSON.toString()));
        }
        if (timeOut > 0) {
            request.connectTimeout(timeOut).socketTimeout(timeOut);
        }
        if (headers != null & headers.length > 0) {
            for (Header header : headers) {
                request.addHeader(header);
            }
        }
        try {
            HttpResponse returnResponse = request.execute().returnResponse();
            LOG.info("post successfully.");
            String result = StringUtil.unicodeToString(EntityUtils.toString(returnResponse.getEntity(), StandardCharsets.UTF_8));
            LOG.info("post return: " + result);
            return result;
        } catch (Exception e) {
            LOG.error("url:" + url + ", " + e.getMessage(), e);
        }
        return null;
    }

    /**
     * 向指定URL发送GET方法的请求
     *
     * @param url   发送请求的URL
     * @param param 请求参数(如果有中文参数必须转码且转一次仍有乱码,可转2次))，请求参数应该是 name1=value1&name2=value2 的形式。
     * @return URL所代表远程资源的响应结果
     */
    public static String sendGet(String url, String param) {
        return sendGet(url, null, param);
    }

    /**
     * 向指定URL发送GET方法的请求
     *
     * @param url   发送请求的URL
     * @param requestHeaders 请求头
     * @param paramObj 请求参数
     * @return URL所代表远程资源的响应结果
     */
    public static String sendGet(String url, Map<String, String> requestHeaders, JSONObject paramObj) {
        String param = null;
        StringBuffer arg = new StringBuffer();
        if (paramObj != null && paramObj.size() > 0) {
            Set<String> keys = paramObj.keySet();
            if (keys != null) {
                Iterator<String> iterator = keys.iterator();
                while (iterator.hasNext()) {
                    String key = iterator.next();
                    arg.append(key);
                    arg.append("=");
                    arg.append(paramObj.getString(key));
                    arg.append("&");
                }
            }
        }
        if (arg.length() > 0) {
            param = arg.substring(0, arg.length() - 1);
        }
        return sendGet(url, requestHeaders, param);
    }

    /**
     * 向指定URL发送GET方法的请求
     *
     * @param url   发送请求的URL
     * @param requestHeaders 请求头
     * @param param 请求参数(如果有中文参数必须转码且转一次仍有乱码,可转2次))，请求参数应该是 name1=value1&name2=value2 的形式。
     * @return URL所代表远程资源的响应结果
     */
    public static String sendGet(String url, Map<String, String> requestHeaders, String param) {
        long start = System.currentTimeMillis();
        StringBuffer result = new StringBuffer();
        BufferedReader in = null;
        try {
            String urlNameString = url;
            if(null != param && param.length() > 0) {
                urlNameString += "?" + param;
            }
            URL realUrl = new URL(urlNameString);
            // 打开和URL之间的连接
            URLConnection connection = realUrl.openConnection();
            // 设置通用的请求属性
            connection.setRequestProperty("accept", "*/*");
            connection.setRequestProperty("connection", "Keep-Alive");
            connection.setRequestProperty("user-agent", "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            if (null != requestHeaders && requestHeaders.size() > 0) {
                Set<String> entries = requestHeaders.keySet();
                if (entries != null) {
                    Iterator<String> iterator = entries.iterator();
                    while (iterator.hasNext()) {
                        String key = iterator.next();
                        String value = requestHeaders.get(key);
                        connection.setRequestProperty(key, value);
                    }
                }
            }
            // 建立实际的连接
            connection.connect();
			/*// 获取所有响应头字段
			Map<String, List<String>> map = connection.getHeaderFields();
			// 遍历所有的响应头字段
			for (String key : map.keySet()) {
				System.out.println(key + "--->" + map.get(key));
			}*/
            // 定义 BufferedReader输入流来读取URL的响应
            in = new BufferedReader(new InputStreamReader(connection.getInputStream()));
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line).append("\n");
            }
            LOG.info("sendGet url:{},cost:{}ms",url,System.currentTimeMillis()-start);
        } catch (IOException e) {
            LOG.error("发送GET请求出错! 请求地址:" + url + " 请求参数:" + param, e);
        } finally {
            try {
                if (in != null) {
                    in.close();
                }
            } catch (Exception ex) {
                LOG.error("发送GET请求出错! 请求地址:" + url + " 请求参数:" + param, ex);
            }
        }
        return result.toString();
    }
}