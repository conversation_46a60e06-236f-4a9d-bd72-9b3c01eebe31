package com.welab.databridge.util;

import com.alibaba.fastjson.JSON;
import com.welab.databridge.vo.common.MessageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import javax.activation.DataHandler;
import javax.mail.Message;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.*;
import java.net.URL;
import java.util.Date;
import java.util.List;
import java.util.Properties;
import java.util.stream.Collectors;


/**
 * Created by alvin.han on 2020/5/7.
 */
@Slf4j
public class MailUtil{

    /**
     * 邮件的服务器域名
     */
    private static String mail_host = "smtp.welab-inc.com";
    /**
     * 邮件内容类型及编码
     */
    private static String mimeType = "text/html;charset=utf-8";

    private static Properties props;

    static {
        props = new Properties();
        props.setProperty("mail.host", mail_host);
        props.setProperty("mail.transport.protocol", "smtp");
        props.setProperty("mail.smtp.auth", "true");
        props.setProperty("mail.smtp.port", "465");
        props.setProperty("mail.smtp.ssl.enable", "true");
    }

    /**
     * 创建邮件
     * @param session session
     * @param mailfrom 发件人
     * @param mailTo 收件人, 多个Email以英文逗号分隔
     * @param mailTittle 邮件标题
     * @param mailText 邮件内容
     * @return
     * @throws Exception
     */
    public static MimeMessage createSimpleMail(Session session, String mailfrom, String mailTo, String mailTittle,
                                               String mailText) throws Exception {
        // 创建邮件对象
        MimeMessage message = new MimeMessage(session);
        // 指明邮件的发件人
        message.setFrom(new InternetAddress(mailfrom));
        // 指明邮件的收件人
        InternetAddress[] list = InternetAddress.parse(mailTo);
        message.setRecipients(Message.RecipientType.TO, list);
        // 邮件的标题
        message.setSubject(mailTittle);
        // 邮件的文本内容
        message.setContent(mailText, mimeType);
        // 返回创建好的邮件对象
        return message;
    }

    public static MimeMessage createMimeMail(Session session, String mailfrom, String mailTo, MessageInfo messageInfo) throws Exception{
        MimeMessage msg = new MimeMessage(session);
        // 指明邮件的发件人
        msg.setFrom(new InternetAddress(mailfrom));
        // 指明邮件的收件人
        InternetAddress[] list = InternetAddress.parse(mailTo);
        msg.setRecipients(Message.RecipientType.TO, list);
        // 邮件的标题
        msg.setSubject(messageInfo.getTitle());

        MimeMultipart multipart = new MimeMultipart();
        //图片节点
        String pic = "";
        if (StringUtil.isNotEmpty(messageInfo.getPic())) {
            String[] pics = messageInfo.getPic().split(",");
            MimeBodyPart imagePart;
            DataHandler imageDataHandler;
            StringBuffer picBuffer = new StringBuffer();
            String imgId;
            for (int i=0; i<pics.length; i++) {
                // 创建图片节点
                imagePart = new MimeBodyPart();
                URL url = new URL(pics[i]);
                imageDataHandler = new DataHandler(url);
                imagePart.setDataHandler(imageDataHandler);
                //为图片节点添加id
                imgId = "img" + i;
                imagePart.setContentID(imgId);
                multipart.addBodyPart(imagePart);
                picBuffer.append("<img src='cid:" + imgId +"' />");
            }
            pic = picBuffer.toString();
        }

        //创建文本节点,将文本内容和图片内容组合
        MimeBodyPart textPart = new MimeBodyPart();
        textPart.setContent(messageInfo.getContent() + pic, mimeType);

        multipart.addBodyPart(textPart);
        //设置与正文的关系 ： 关联关系
        multipart.setSubType("related");

        //邮件正文,将复合节点变成单节点   因为在正文中只能出现单节点  不能出现复合节点
        MimeBodyPart mainContent = new MimeBodyPart();
        mainContent.setContent(multipart);

        //将正文与附件设置成一个混合节点
        MimeMultipart mm = new MimeMultipart();
        mm.addBodyPart(mainContent);

        //附件节点
        if (StringUtil.isNotEmpty(messageInfo.getAttachment())) {
            String[] attachments = messageInfo.getAttachment().split(",");
            MimeBodyPart attachment;
            URL fileUrl;
            DataHandler fileDataHandler;
            for (int j=0; j<attachments.length; j++) {
                attachment = new MimeBodyPart();
                fileUrl = new URL(attachments[j]);
                fileDataHandler = new DataHandler(fileUrl);
                attachment.setDataHandler(fileDataHandler);
                //设置一个名字
                attachment.setFileName(MimeUtility.encodeText(fileDataHandler.getName()));
                mm.addBodyPart(attachment);
            }
        }

        //附件节点与正文的关系是 混合关系
        mm.setSubType("mixed");

        msg.setContent(mm, mimeType);
        //设置发送时间
        msg.setSentDate(new Date());
        // 保存邮件
        msg.saveChanges();
        return msg;
    }

    public static String send(MessageInfo msg, List<String> accountList) {
        String mailTo;
        if (CollectionUtils.isEmpty(accountList)) {
            return "";
        } else {
            mailTo = accountList.stream()
                    .collect(Collectors.joining(","));
        }
        log.info("mailTo:" + mailTo);

        try {
            // 1、创建session
            Session session = Session.getInstance(props);
            // 开启Session的debug模式，这样就可以查看到程序发送Email的运行状态
            session.setDebug(true);
            // 2、通过session得到transport对象
            Transport ts = session.getTransport();
            // 3、使用邮箱的用户名和密码连上邮件服务器，发送邮件时，发件人需要提交邮箱的用户名和密码给smtp服务器，用户名和密码都通过验证之后才能够正常发送邮件给收件人。
            ts.connect(mail_host, ConfigUtil.config.getMailFrom(), ConfigUtil.config.getMailFromPassword());
            // 4、创建邮件
            Message message;
            if (StringUtil.isEmpty(msg.getPic()) && StringUtil.isEmpty(msg.getAttachment())) {
                message = createSimpleMail(session, ConfigUtil.config.getMailFrom(), mailTo, msg.getTitle(), msg.getContent());
            } else {
                message = createMimeMail(session, ConfigUtil.config.getMailFrom(), mailTo, msg);
            }

            // 5、发送邮件
            ts.sendMessage(message, message.getAllRecipients());
            ts.close();
        } catch (Exception e) {
            log.error("send mail error,msg :" + JSON.toJSONString(msg) + ", account :" + mailTo + ", ex: " + e.getMessage());
            return mailTo;
        }
        log.info("send mail msg sucess");
        return "";
    }
}
