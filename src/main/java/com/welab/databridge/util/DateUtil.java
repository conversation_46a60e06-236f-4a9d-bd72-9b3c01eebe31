package com.welab.databridge.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.validator.GenericValidator;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Locale;

/**
 *
 * @Description 日期工具类
 */
@Slf4j
public class DateUtil {

    public static String FORMAT_FULL_DATE = "yyyy-MM-dd HH:mm:ss";
    public static String FORMAT_FULL_DATE2 = "yyyy/MM/dd HH:mm:ss";
    public static String FORMAT_FULL_DATE3 = "MM/dd/yyyy HH:mm:ss";
    public static String FORMAT_SIMPLE_DATE = "yyyy-MM-dd";
    public static String FORMAT_SIMPLE_DATE2 = "yyyy/MM/dd";
    public static String FORMAT_SIMPLE_DATE3 = "MM/dd/yyyy";
    public static String FORMAT_WHOLE_DATE = "yyyyMMddHHmmssSSS";
    public static String FORMAT_WHOLE_DATE2 = "yyyy-MM-dd HH:mm:ss.S";
    public static String FORMAT_US_DATE = "EEE MMM dd HH:mm:ss zzz yyyy";

    /**
     * 将字符串转换为日期
     *
     * @param dateStr
     * @param pattern
     * @return
     */
    public static LocalDateTime format(String dateStr, String pattern) {
        pattern = StringUtil.isEmpty(pattern) ? FORMAT_FULL_DATE : pattern;
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern(pattern);
        LocalDateTime datetime = LocalDateTime.parse(dateStr, inputFormatter);
        return datetime;
    }

    /**
     * 将字符串转换为日期
     *
     * @param dateStr
     * @param pattern
     * @return
     */
    public static LocalDate formatDate(String dateStr, String pattern) {
        pattern = StringUtil.isEmpty(pattern) ? FORMAT_FULL_DATE : pattern;
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern(pattern);
        LocalDate date = LocalDate.parse(dateStr, inputFormatter);
        return date;
    }

    /**
     * 将日期转化为字符串
     *
     * @param datetime
     * @param pattern
     * @return
     */
    public static String format(LocalDateTime datetime, String pattern) {
        pattern = StringUtil.isEmpty(pattern) ? FORMAT_FULL_DATE : pattern;
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern(pattern);
        return datetime.format(outputFormatter);
    }

    /**
     * 将日期转化为字符串
     *
     * @param date
     * @param pattern
     * @return
     */
    public static String formatDate(LocalDate date, String pattern) {
        pattern = StringUtil.isEmpty(pattern) ? FORMAT_SIMPLE_DATE : pattern;
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern(pattern);
        return date.format(outputFormatter);
    }

    public static Date parse(String str, String pattern, Locale locale) {
        if(str == null || pattern == null) {
            return null;
        }
        try {
            return new SimpleDateFormat(pattern, locale).parse(str);
        } catch (ParseException e) {
            log.error("parse date error: {}", e.getMessage());
        }
        return null;
    }

    public static String format(Date date, String pattern) {
        pattern = StringUtil.isEmpty(pattern) ? FORMAT_FULL_DATE : pattern;
        DateFormat outputFormatter = new SimpleDateFormat(pattern);
        return outputFormatter.format(date);
    }

    /**
     * 获取当前时间
     *
     * @return
     */
    public static String getCurrentDateTime() {
        return format(LocalDateTime.now(), "");
    }

    /**
     * 获取当日开始时间
     *
     * @return
     */
    public static String getCurrentDateStart() {
        return format(LocalDateTime.now(), FORMAT_SIMPLE_DATE) + " 00:00:00";
    }

    /**
     * 获取当日结束时间
     *
     * @return
     */
    public static String getCurrentDateEnd() {
        return format(LocalDateTime.now(), FORMAT_SIMPLE_DATE) + " 23:59:59";
    }

    /**
     * 判断字符串是否为合法的日期格式
     *
     * @param date
     * @return
     */
    public static boolean dateValid(String date) {
        if (GenericValidator.isDate(date, FORMAT_FULL_DATE, false)) {
            return true;
        } else if (GenericValidator.isDate(date, FORMAT_FULL_DATE2, false)) {
            return true;
        } else if (GenericValidator.isDate(date, FORMAT_FULL_DATE3, false)) {
            return true;
        } else if (GenericValidator.isDate(date, FORMAT_SIMPLE_DATE, false)) {
            return true;
        } else if (GenericValidator.isDate(date, FORMAT_SIMPLE_DATE2, false)) {
            return true;
        } else if (GenericValidator.isDate(date, FORMAT_SIMPLE_DATE3, false)) {
            return true;
        }
        return false;
    }

    public static boolean earlyThanNow(String date) {
        String pattern = "";
        if (GenericValidator.isDate(date, FORMAT_FULL_DATE, false)) {
            pattern = FORMAT_FULL_DATE;
        } else if (GenericValidator.isDate(date, FORMAT_FULL_DATE2, false)) {
            pattern = FORMAT_FULL_DATE2;
        } else if (GenericValidator.isDate(date, FORMAT_FULL_DATE3, false)) {
            pattern = FORMAT_FULL_DATE3;
        } else if (GenericValidator.isDate(date, FORMAT_SIMPLE_DATE, false)) {
            pattern = FORMAT_SIMPLE_DATE;
        } else if (GenericValidator.isDate(date, FORMAT_SIMPLE_DATE2, false)) {
            pattern = FORMAT_SIMPLE_DATE2;
        } else if (GenericValidator.isDate(date, FORMAT_SIMPLE_DATE3, false)) {
            pattern = FORMAT_SIMPLE_DATE3;
        }

        if (StringUtil.isNotEmpty(pattern)) {
            LocalDateTime localDateTime = format(date, pattern);
            if (localDateTime.isBefore(LocalDateTime.now())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 将20230101121212格式的文本转换成合法的日期字符串
     *
     * @param text
     * @return
     */
    public static String processDateStr(String text) {
        if (StringUtil.isEmpty(text)) {
            return text;
        }
        if (text.trim().length() < 14) {
            if (text.trim().length() == 10) {
                text = "20" + text.trim() + "00";
            }
            if (text.trim().length() == 12) {
                text = text.trim() + "00";
            }
        }
        StringBuffer sb = new StringBuffer(19);
        return sb.append(text.substring(0,4))
                .append("-")
                .append(text.substring(4,6))
                .append("-")
                .append(text.substring(6,8))
                .append(" ")
                .append(text.substring(8,10))
                .append(":")
                .append(text.substring(10,12))
                .append(":")
                .append(text.substring(12,14)).toString();
    }
}
