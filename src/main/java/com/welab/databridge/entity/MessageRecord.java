package com.welab.databridge.entity;

import com.welab.databridge.annotation.ExportField;
import com.welab.databridge.annotation.TemplateField;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * 短信记录(MessageRecord)实体类
 *
 * <AUTHOR>
 * @since 2023-01-03 14:29:50
 */
@Data
public class MessageRecord implements Serializable {
    private static final long serialVersionUID = 194413480822853687L;
    /**
     * 主键
     */
    private Integer id;
    /**
     * 上传批次ID
     */
    private Integer uploadId;
    /**
     * 部门ID
     */
    private Integer deptId;
    /**
     * userid
     */
    private Integer userid;
    /**
     * 身份证号
     */
    private String cnid;
    /**
     * 手机号
     */
    @TemplateField(name = "手机号\r\n（用户11位手机号码）")
    @ExportField(name = "手机号")
    private String phone;
    /**
     * 发送时间
     */
    @TemplateField(name = "发送时间（年/月/日 小时:分钟:秒；\r\n举例：2023/3/2 20:10:20）")
    @ExportField(name = "发送时间")
    private String sendTime;
    /**
     * 发送内容
     */
    @TemplateField(name = "发送内容")
    @ExportField(name = "发送内容")
    private String content;
    /**
     * 发送状态，0-发送成功，1-发送失败、2-未知
     */
    @TemplateField(name = "发送状态（发送成功/发送失败）")
    private Integer status;
    @ExportField(name = "发送状态")
    private String statusCn;
    /**
     * 供应商
     */
    private String supplier;
    /**
     * 上传方式：0-接口，1-手动
     */
    private Integer uploadMethod;
    /**
     * 是否删除：0-否，1-是
     */
    private Integer deleted;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 上传时间
     */
    private String createTime;

    /**
     * 部门名称
     */
    private String deptName;
}

