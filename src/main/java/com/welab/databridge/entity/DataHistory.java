package com.welab.databridge.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * 历史版本(DataHistory)实体类
 *
 * <AUTHOR>
 * @since 2023-02-02 15:53:00
 */
@Data
public class DataHistory implements Serializable {
    private static final long serialVersionUID = -40727877663254271L;
    /**
     * 主键,自增长
     */
    private Integer id;
    /**
     * 业务类型
     */
    private String bizType;
    /**
     * 业务id
     */
    private Integer bizId;
    /**
     * 操作 add:新增 modify:修改 delete:删除 online:上线 offline下线 cancel:撤销 suspend:暂停 upload:上传
     */
    private String operation;
    /**
     * 版本号
     */
    private String version;
    /**
     * 内容 json
     */
    private Map content;
    /**
     * 操作人id
     */
    private String operatorId;
    /**
     * 操作人姓名
     */
    private String operatorName;
    /**
     * 添加时间
     */
    private String createTime;
    /**
     * 更新时间
     */
    private String updateTime;

}

