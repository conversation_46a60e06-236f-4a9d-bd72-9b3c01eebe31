package com.welab.databridge.entity;

import com.welab.databridge.annotation.ExportField;
import lombok.Data;

import java.io.Serializable;

/**
 * 短信上传失败记录(MessageFail)实体类
 *
 * <AUTHOR>
 * @since 2023-01-03 14:29:42
 */
@Data
public class MessageFail implements Serializable {
    private static final long serialVersionUID = -12301326157862313L;
    /**
     * 主键
     */
    private Integer id;
    /**
     * 上传批次ID
     */
    private Integer uploadId;
    /**
     * 部门ID
     */
    private Integer deptId;
    /**
     * 手机号
     */
    @ExportField(name = "手机号")
    private String phone;
    /**
     * 发送内容
     */
    @ExportField(name = "发送内容")
    private String content;
    /**
     * 供应商
     */
    private String supplier;
    /**
     * 发送时间
     */
    @ExportField(name = "发送时间")
    private String sendTime;
    /**
     * 发送状态，发送成功，发送失败、未知
     */
    @ExportField(name = "发送状态")
    private String status;
    /**
     * 失败原因
     */
    @ExportField(name = "失败原因")
    private String failMsg;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 上传时间
     */
    private String createTime;

    /**
     * 部门名称
     */
    @ExportField(name = "部门名称")
    private String deptName;
}

