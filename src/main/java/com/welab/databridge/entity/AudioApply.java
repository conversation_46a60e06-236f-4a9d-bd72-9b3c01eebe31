package com.welab.databridge.entity;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.io.Serializable;

import lombok.Data;

/**
 * (AudioApply)实体类
 *
 * <AUTHOR>
 * @since 2023-03-01 18:07:57
 */
@Data
public class AudioApply implements Serializable {
    private static final long serialVersionUID = 641186087931308290L;
    /**
     * 主键
     */
    private Integer id;
    /**
     * 申请下载的语音记录ID
     */
    private List<Integer> audioIds;
    /**
     * 申请下载原因
     */
    private String reason;
    /**
     * 状态：default-初始状态；processing-正在生成下载链接；finish-下载链接已邮件发送；fail-下载失败
     */
    private String status;
    /**
     * 审核状态：default-默认，in_review-审核中，audit_reject-审核拒绝，approved-审核通过
     */
    private String approveStatus;
    /**
     * 申请人
     */
    private String applier;
    /**
     * 申请时间
     */
    private String createTime;

}

