package com.welab.databridge.entity;

import java.io.Serializable;

import lombok.Data;

/**
 * 音频记录临时(AudioRecordTemp)实体类
 *
 * <AUTHOR>
 * @since 2024-03-14 10:50:15
 */
@Data
public class AudioRecordTemp implements Serializable {
    private static final long serialVersionUID = 874039423981700888L;
    /**
     * 主键
     */
    private Integer id;
    /**
     * 部门ID
     */
    private Integer deptId;
    /**
     * userid
     */
    private Integer userid;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 身份证号
     */
    private String cnid;

    private String url;
    /**
     * 接听时间
     */
    private String startTime;
    /**
     * 手动上传时间，接口传输时为null
     */
    private String uploadTime;
    /**
     * 上传方式：0-接口，1-手动
     */
    private Integer uploadMethod;
    /**
     * 服务商
     */
    private String supplier;
    /**
     * 是否入库：0-否，1-是
     */
    private Integer flag;

    private String dir;

    private String operator;

}

