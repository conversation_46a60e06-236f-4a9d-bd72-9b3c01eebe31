package com.welab.databridge.entity;

import com.welab.databridge.annotation.ExportField;
import lombok.Data;

import java.io.Serializable;

/**
 * 音频记录(AudioRecord)实体类
 *
 * <AUTHOR>
 * @since 2023-01-03 14:29:52
 */
@Data
public class AudioRecord implements Serializable {
    private static final long serialVersionUID = -61867872616107633L;
    /**
     * 主键
     */
    private Integer id;
    /**
     * 上传批次ID
     */
    private Integer uploadId;
    /**
     * 部门ID
     */
    private Integer deptId;
    /**
     * userid
     */
    @ExportField(name = "userid")
    private Integer userid;
    /**
     * 手机号
     */
    @ExportField(name = "手机号")
    private String phone;
    /**
     * 身份证号
     */
    private String cnid;
    /**
     * oss地址
     */
    private String url;
    /**
     * 接听时间
     */
    @ExportField(name = "接听时间")
    private String startTime;
    /**
     * 结束时间
     */
    @ExportField(name = "结束时间")
    private String endTime;
    /**
     * 通话类型，0-外呼，1-来电
     */
    private Integer callType;
    /**
     * 手动上传时间，接口传输时为null
     */
    @ExportField(name = "上传时间")
    private String uploadTime;
    /**
     * 接听状态，0-接听成功，1-接听失败
     */
    private Integer status;
    /**
     * 上传方式：0-接口，1-手动
     */
    private Integer uploadMethod;
    /**
     * 服务商
     */
    private String supplier;
    /**
     * 所属部门
     */
    private String dept;
    /**
     * 所属机构
     */
    private String org;
    /**
     * 是否已回听：0-否，1-是
     */
    private Integer listened;
    /**
     * 录音时长
     */
    private Integer duration;
    /**
     * 是否删除：0-否，1-是
     */
    private Integer deleted;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 坐席/代理人ID
     */
    @ExportField(name = "坐席")
    private String agentId;
    /**
     * 创建时间
     */
    private String createTime;

    @ExportField(name = "组织名称")
    private String deptName;

    /**
     * 回听人
     */
    @ExportField(name = "回听人")
    private String listener;

    @ExportField(name = "回听状态")
    private String listenedRemark;

    /**
     * 回听时间
     */
    @ExportField(name = "回听时间")
    private String listenTime;

    private String exportTime;

    /**
     * 评语建议
     */
    @ExportField(name = "评语建议")
    private String comment;
}

