package com.welab.databridge.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * 用户部门关系表(UserDept)实体类
 *
 * <AUTHOR>
 * @since 2023-01-03 14:29:55
 */
@Data
public class UserDept implements Serializable {
    private static final long serialVersionUID = 734687456644535598L;
    /**
     * 主键
     */
    private Integer id;
    /**
     * 部门ID
     */
    private Integer deptId;
    /**
     * userid
     */
    private String userid;
    /**
     * 用户名
     */
    private String name;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 创建时间
     */
    private String createTime;

}

