package com.welab.databridge.entity;

import java.util.Date;
import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * 部门表(Department)实体类
 *
 * <AUTHOR>
 * @since 2023-01-03 14:29:45
 */
@Data
public class Department implements Serializable {
    private static final long serialVersionUID = -21328315070611254L;
    /**
     * 主键
     */
    private Integer id;
    /**
     * 父部门ID
     */
    private Integer parentId;
    /**
     * 部门名称
     */
    private String name;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 修改时间
     */
    private String updateTime;

    private List<Department> children;
}

