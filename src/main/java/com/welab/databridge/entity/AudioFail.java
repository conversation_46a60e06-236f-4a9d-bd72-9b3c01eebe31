package com.welab.databridge.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * 音频上传失败记录记录(AudioFail)实体类
 *
 * <AUTHOR>
 * @since 2023-01-03 14:29:58
 */
@Data
public class AudioFail implements Serializable {
    private static final long serialVersionUID = 708846284710518870L;
    /**
     * 主键
     */
    private Integer id;
    /**
     * 上传批次ID
     */
    private Integer uploadId;
    /**
     * 部门ID
     */
    private Integer deptId;
    /**
     * 文件名
     */
    private String name;
    /**
     * 上传方式：0-接口，1-手动
     */
    private Integer uploadMethod;
    /**
     * 失败原因
     */
    private String failMsg;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 创建时间
     */
    private String createTime;

}

