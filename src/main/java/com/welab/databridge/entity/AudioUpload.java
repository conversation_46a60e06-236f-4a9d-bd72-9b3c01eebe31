package com.welab.databridge.entity;

import com.welab.databridge.annotation.AuditShowField;
import lombok.Data;

import java.io.Serializable;

/**
 * 音频上传记录(AudioUpload)实体类
 *
 * <AUTHOR>
 * @since 2023-01-03 14:29:39
 */
@Data
public class AudioUpload implements Serializable {
    private static final long serialVersionUID = 637591175821764272L;
    /**
     * 主键
     */
    private Integer id;
    /**
     * 部门ID
     */
    private Integer deptId;
    /**
     * 批次号
     */
    @AuditShowField(name = "批次号")
    private String batchNo;
    /**
     * 提交数量
     */
    @AuditShowField(name = "提交数量")
    private Integer uploadNum;
    /**
     * 上传成功数量
     */
    @AuditShowField(name = "上传成功数量")
    private Integer successNum;
    /**
     * 上传失败数量
     */
    @AuditShowField(name = "上传失败数量")
    private Integer failNum;
    /**
     * 操作类型：add-增加，delete-删除
     */
    private String operation;
    /**
     * 状态：proccessing-处理中，finish-处理完成,fail-处理失败
     */
    private String status;
    /**
     * 失败原因
     */
    private String failMsg;
    /**
     * 审核状态：default-默认，in_review-审核中，audit_reject-审核拒绝，approved-审核通过
     */
    private String approveStatus;
    /**
     * 审核类型：online-上线，offline-下线，suspend-暂停，delete-删除
     */
    private String approveType;
    /**
     * 操作人
     */
    @AuditShowField(name = "操作人")
    private String operator;
    /**
     * 坐席/代理人ID
     */
    @AuditShowField(name = "坐席")
    private String agentId;
    /**
     * 上传时间
     */
    @AuditShowField(name = "上传时间")
    private String createTime;
    /**
     * 操作时间
     */
    private String updateTime;

    @AuditShowField(name = "部门名称")
    private String deptName;
}

