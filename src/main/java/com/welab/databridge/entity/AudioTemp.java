package com.welab.databridge.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * (AudioTemp)实体类
 *
 * <AUTHOR>
 * @since 2023-03-17 14:46:34
 */
@Data
public class AudioTemp implements Serializable {
    private static final long serialVersionUID = 109383050635415885L;
    /**
     * id
     */
    private Integer id;
    /**
     * 坐席工号
     */
    private String cno;
    /**
     * 客户手机号
     */
    private String mobile;
    /**
     * 呼叫中心编码对应的token
     */
    private String sign;
    /**
     * 录音文件地址
     */
    private String recordFile;
    /**
     * 版本
     */
    private String version;
    /**
     * 通话类型
     */
    private String callType;
    /**
     * 供应商名称，TR 或者 JS
     */
    private String provider;
    /**
     * 接听状态
     */
    private String callStatus;
    /**
     * 坐席名称
     */
    private String staffName;
    /**
     * 拨打时间
     */
    private String startTime;
    /**
     * 结束时间
     */
    private String endTime;
    /**
     * 呼叫中心编码
     */
    private String enterpriseId;
    /**
     * 地区编号
     */
    private String region;
    /**
     * 部门名称
     */
    private String enterpriseName;
    /**
     * 催收组
     */
    private String groupCode;
    /**
     * 状态：proccessing-处理中，finish-处理完成,fail-处理失败
     */
    private String status;

    private String queryDate;
    /**
     * 创建时间
     */
    private String createTime;

}

