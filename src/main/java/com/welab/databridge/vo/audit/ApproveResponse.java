package com.welab.databridge.vo.audit;

import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class ApproveResponse {
    private boolean success;
    private int code;
    private String message;
    private ResponseData data;

    public ApproveResponse(boolean success) {
        this.success = success;
    }

    public ApproveResponse(boolean success, JSONObject data) {
        this.success = success;
        this.data = new ResponseData(data.getString("auditRequestId"), data.getBooleanValue("needAudit"));
    }

    public ApproveResponse(boolean success, int code, String message) {
        this.success = success;
        this.code = code;
        this.message = message;
    }

    @Data
    @AllArgsConstructor
    public class ResponseData {
        private String auditRequestId;
        private boolean needAudit;
    }
}
