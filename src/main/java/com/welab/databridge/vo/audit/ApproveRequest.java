package com.welab.databridge.vo.audit;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.welab.databridge.common.ResultCode;
import com.welab.databridge.util.ConfigUtil;
import com.welab.databridge.util.HttpClientUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class ApproveRequest {
    private String auditConfigCode;
    private List<String> businessIds;
    private String remark;
    private JSONObject param;
    private String auditRequestId;
    private Map<String, String> headers;

    private ApproveRequest() {
    }

    private ApproveRequest(String auditConfigCode) {
        this.auditConfigCode = auditConfigCode;
    }

    public static ApproveRequest create(String auditConfigCode) {
        return new ApproveRequest(auditConfigCode);
    }

    public static ApproveRequest createCancel() {
        return new ApproveRequest();
    }


    /**
     * 设置备注
     */
    public ApproveRequest remark(String remark) {
        this.remark = remark;
        return this;
    }

    /**
     * 设置 头信息
     */
    public ApproveRequest headers(Map<String, String> headers) {
        this.headers = headers;
        return this;
    }

    /**
     * 设置 查看详情需要的请求参数(提交审核时由前端传过来)
     */
    public ApproveRequest param(JSONObject param) {
        this.param = param;
        return this;
    }

    /**
     * 设置 子系统提交审核的业务id
     */
    public ApproveRequest businessIds(List<String> businessIds) {
        this.businessIds = businessIds;
        return this;
    }

    /**
     * 设置 审核列表id
     */
    public ApproveRequest auditRequestId(String auditRequestId) {
        this.auditRequestId = auditRequestId;
        return this;
    }

    /**
     * 提交审核
     */
    public ApproveResponse executeSubmit() {
        checkSubmitParam();

        JSONObject bodyObj = new JSONObject();
        bodyObj.put("auditConfigCode", auditConfigCode);
        bodyObj.put("businessIds", businessIds);
        bodyObj.put("param", param);
        bodyObj.put("remark", remark);
        String body = bodyObj.toJSONString();
        return call(ConfigUtil.config.getAuditServerUrl() + ConfigUtil.config.getAuditSubmitUri(), body);
    }

    /**
     * 撤销审核
     */
    public ApproveResponse executeCancel() {
        checkCancelParam();

        JSONObject bodyObj = new JSONObject();
        bodyObj.put("businessIds", businessIds);
        bodyObj.put("auditRequestId", auditRequestId);
        String body = bodyObj.toJSONString();
        return call(ConfigUtil.config.getAuditServerUrl() + ConfigUtil.config.getAuditCancelUri(), body);
    }

    private ApproveResponse call(String url, String body) {
        String response;
        try {
            System.out.println(url);
            response = HttpClientUtil.send(url, "POST", headers, body);
            log.info("call audit success, result【 " + response + " 】，param【 " + body + " 】");
        } catch (IOException e) {
            log.error("call audit fail, ex【 " + e.getMessage() + " 】，param【 " + body + " 】");
            return new ApproveResponse(false, -1, "请求审核系统异常");
        }
        JSONObject resultObj = JSON.parseObject(response);
        if (resultObj == null) {
            return new ApproveResponse(false, -1, "请求审核系统异常");
        }
        int code = resultObj.getIntValue("code");
        if (ResultCode.SUCCESS.getCode() != code) {
            return new ApproveResponse(false, code, "审核系统：" + resultObj.getString("message"));
        }

        return new ApproveResponse(true, resultObj.getJSONObject("data"));
    }

    private void checkSubmitParam() {
        if (StrUtil.isBlank(auditConfigCode)) {
            throw new RuntimeException("审核配置Code不能为空");
        }
        if (ObjectUtil.isEmpty(businessIds)) {
            throw new RuntimeException("子系统业务id不能为空");
        }
        if (ObjectUtil.isEmpty(headers)) {
            throw new RuntimeException("请求头部信息不能为空");
        }
    }

    private void checkCancelParam() {
        if (StrUtil.isBlank(auditRequestId)) {
            throw new RuntimeException("审核列表id不能为空");
        }
        if (ObjectUtil.isEmpty(headers)) {
            throw new RuntimeException("请求头部信息不能为空");
        }
    }
}
