package com.welab.databridge.vo.audit;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.util.List;

@Data
public class AuditParam {
    private JSONObject columns;
    private JSONObject detail;

    @Data
    public class Column {
        private String key;
        private String value;
    }

    @Data
    public class Detail {
        private String tabName;
        private String tabTitle;
        private List<TabContent> tabContent;
    }

    @Data
    public class TabContent {
        private String label;
        private Object value;
        private String type;
        private List<TableColumn> tableColumns;
    }

    @Data
    public class TableColumn {
        private String key;
        private String title;
    }
}
