package com.welab.databridge.vo.audit;

import cn.hutool.core.util.ObjectUtil;
import com.welab.databridge.util.StringUtil;
import lombok.Data;

import java.util.List;

@Data
public class CallbackParam {
    private String action;
    private String auditConfigCode;
    private List<String> businessIds;
    private Result result;

    @Data
    public class Result {
        private Boolean isNeedAudit;
        private List<String> nextToReviewerPerson;
        private String auditRequestId;
        private String remark;
    }

    /**
     * 参数检测
     */
    public boolean checkCallbackParam() {
        if (ObjectUtil.isEmpty(businessIds)) {
            return false;
        }
        if (StringUtil.isEmpty(action)) {
            return false;
        }
        if (StringUtil.isEmpty(auditConfigCode)) {
            return false;
        }
        return true;
    }
}
