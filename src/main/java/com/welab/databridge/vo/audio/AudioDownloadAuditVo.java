package com.welab.databridge.vo.audio;

import com.welab.databridge.annotation.AuditShowField;
import lombok.Data;

import java.util.List;

@Data
public class AudioDownloadAuditVo {

    private Integer id;
    @AuditShowField(name = "批次号")
    private String batchNo;
    @AuditShowField(name = "申请人")
    private String operator;
    @AuditShowField(name = "申请时间")
    private String applyTime;
    @AuditShowField(name = "申请原因")
    private String reason;
    @AuditShowField(name = "下载数量")
    private Integer downloadNum;
    @AuditShowField(name = "下载列表", type = "table")
    private List<AudioRecordVo> detailList;
}
