package com.welab.databridge.vo.audio;

import lombok.Data;

@Data
public class CollectionRecordVo {
    /**
     * 坐席工号
     */
    private String cno;
    /**
     * 客户手机号
     */
    private String mobile;
    /**
     * 呼叫中心编码对应的token
     */
    private String sign;
    /**
     * 录音文件地址
     */
    private String recordFile;
    /**
     * 接口版本
     */
    private String version;
    /**
     * 通话类型
     */
    private String callType;
    /**
     * 供应商名称，TR-天润 或者 JS-94 或者 LB-灵伴
     */
    private String provider;
    /**
     * 接听状态
     */
    private String callStatus;
    /**
     * 坐席名称
     */
    private String staffName;
    /**
     * 拨打时间
     */
    private String startTime;
    /**
     * 结束时间
     */
    private String endTime;
    /**
     * 呼叫中心编码
     */
    private String enterpriseId;
    /**
     * 地区编号
     */
    private String region;
    /**
     * 部门名称
     */
    private String enterpriseName;

    private String queryDate;
}
