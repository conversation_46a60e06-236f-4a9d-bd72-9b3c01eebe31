package com.welab.databridge.vo.audio;

import com.welab.databridge.annotation.AuditShowField;
import lombok.Data;

import java.io.Serializable;

/**
 * 音频记录(AudioRecord)实体类
 *
 * <AUTHOR>
 * @since 2023-01-03 14:29:52
 */
@Data
public class AudioRecordVo {
    @AuditShowField(name = "批次号")
    private String batchNo;
    /**
     * userid
     */
    @AuditShowField(name = "userid")
    private Integer userid;
    /**
     * 手机号
     */
    @AuditShowField(name = "手机号")
    private String phone;
    /**
     * 接听时间
     */
    @AuditShowField(name = "接听时间")
    private String startTime;
    /**
     * 上传时间
     */
    @AuditShowField(name = "上传时间")
    private String uploadTime;
    /**
     * 上传组织名称
     */
    @AuditShowField(name = "上传组织名称")
    private String deptName;
    /**
     * 上传操作人
     */
    @AuditShowField(name = "上传操作人")
    private String operator;

}

