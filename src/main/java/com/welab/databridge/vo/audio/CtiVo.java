package com.welab.databridge.vo.audio;

import lombok.Data;

@Data
public class CtiVo {
    /**
     * 验证类型
     * 取值1，2； 取值说明：
     * validateType=1时使用部门编号(departmentId)进行验证；
     * validateType=2时使用呼叫中心编号(enterpriseId)进行验证；
     */
    private Integer validateType;
    /**
     * 部门编号
     * 说明：validateType=1时，此参数为必选参数，例如：departmentId=BM0000001；
     */
    private String departmentId;
    /**
     * 呼叫中心编号
     * 说明：validateType=2时，此参数为必选参数，例如：enterpriseId=7000101；
     */
    private Integer enterpriseId;
    /**
     * 当前时间戳,精确到s，时间戳有效期为30分钟
     */
    private Long timestamp;
    /**
     * 权限验证值
     * 取值：
     * validateType=1时，MD5({departmentId}+{timestamp}+{部门token})；
     * validateType=2时，MD5({enterpriseId}+{timestamp}+{部门token})；
     * 格式：32位全小写
     */
    private String sign;
    /**
     * 录音类型
     * record:录音
     * voicemail:留言
     * tsi:彩铃、当开启号码录音状态识别，发起预览外呼，客户号码是手机且客户未接听时返回该地址
     * rasr:语音机器人客户侧录音
     * transfer:转接
     * consult:咨询
     * threeway:三方
     */
    private String recordType;
    /**
     * 录音文件名,多个文件名采用英文逗号分割 最大同时处理50个录音文件名
     */
    private String recordFile;
    /**
     * 录音类型,取值说明：0为mp3，1为wav，默认为mp3类型
     */
    private Integer recordFormat;
    /**
     * 呼叫类型,说明：开启分线录音时，获取客户侧或座席侧录音需要，recordFormat=1时有效，recordFormat=0时忽略。
     * 取值范围：1,2,4,5（数字含义：呼入,webcall,预览外呼,预测外呼）
     */
    private Integer callType;
    /**
     * 分线录音录音侧,说明：开启分线录音时，获取客户侧或座席侧录音需要，recordFormat=1时有效，recordFormat=0时忽略。
     * 取值范围：1,2 (数字含义：客户侧，座席侧)
     * recordSide不为空时，callType必选
     */
    private Integer recordSide;
    /**
     * 是否下载,１为下载，空或０表示试听
     */
    private Integer download;
}
