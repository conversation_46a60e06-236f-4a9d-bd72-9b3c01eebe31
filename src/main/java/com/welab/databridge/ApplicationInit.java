package com.welab.databridge;

import com.welab.databridge.component.ConfigProperties;
import com.welab.databridge.util.ConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 配置初始化
 * <AUTHOR>
 * @date 2022年3月16日 上午10:53:42
 */
@Component
@Slf4j
public class ApplicationInit implements ApplicationListener<ContextRefreshedEvent> {

    @Resource
    ConfigProperties config;

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        //初始化config
        ConfigUtil.init(config);
        log.info("初始化config成功!");
    }
}
