package com.welab.databridge.component;

import com.welab.databridge.service.AudioRecordService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 拉取数据定时器
 */
@Component
public class AudioTask {
    private Logger log = LoggerFactory.getLogger(AudioTask.class);

    @Resource
    private AudioRecordService audioRecordService;

    @Async("todayRecordScheduler")
    @Scheduled(cron = "0 10 0 * * *")
    public void todayDataLoad() {
        try {
            long start = System.currentTimeMillis();
            log.info("Today AudioTask start");
            audioRecordService.processTodayData();
            log.info("Today AudioTask end, used: " + (System.currentTimeMillis() - start));
        }catch (Exception e) {
            log.error("Today AudioTask process error", e);
        }
    }

    @Async("historyRecordScheduler")
//    @Scheduled(cron = "* * 20-23,0-8 * * *")
    public void historyDataLoad() {
        try {
            long start = System.currentTimeMillis();
            log.info("History AudioTask start");
            audioRecordService.processHistoryData();
            log.info("History AudioTask  end: " + (System.currentTimeMillis() - start));
        } catch (Exception e) {
            log.error("History AudioTask process error", e);
        }
    }
}
