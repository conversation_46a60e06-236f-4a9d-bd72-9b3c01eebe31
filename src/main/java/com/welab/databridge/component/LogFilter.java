package com.welab.databridge.component;

import com.welab.databridge.util.IpUtil;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Order(Ordered.HIGHEST_PRECEDENCE)
@Component
@Slf4j
public class LogFilter implements Filter {

    @Override
    public void doFilter(final ServletRequest req, final ServletResponse res, final FilterChain chain) throws IOException, ServletException {
        final HttpServletResponse response = (HttpServletResponse) res;
        final HttpServletRequest request = (HttpServletRequest) req;
        String traceId = request.getHeader("x-trace-id");
        String remoteHost = IpUtil.getIpAddress(request);
        String requestURI = request.getRequestURI();
        MDC.put("traceId", traceId);
        MDC.put("req.remoteHost", remoteHost);
        MDC.put("req.requestURI", requestURI);
        log.info("LogFilter触发,traceId:{},req.remoteHost:{},req.requestURI:{}", MDC.get("traceId"), MDC.get("req.remoteHost"), MDC.get("req.requestURI"));
        try{
            chain.doFilter(req, res);
        }finally {
            MDC.remove("traceId");
            MDC.remove("req.remoteHost");
            MDC.remove("req.requestURI");
        }
    }

    @Override
    public void init(final FilterConfig filterConfig) {
        log.info("LogFilter初始化");
    }

    @Override
    public void destroy() {
        MDC.remove("traceId");
        MDC.remove("req.remoteHost");
        MDC.remove("req.requestURI");
    }
}
