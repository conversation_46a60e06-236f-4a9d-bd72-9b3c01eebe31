package com.welab.databridge.component;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 *
 */
@Configuration
@ConfigurationProperties("query")
@Data
@RefreshScope
public class ConfigProperties {
    //审核系统
    private String auditServerUrl;
    //提交审核
    private String auditSubmitUri;
    //撤销审核
    private String auditCancelUri;
    //文件名正则
    private String fileNameRegex;
    //发件邮箱
    private String mailFrom;
    //邮箱密码
    private String mailFromPassword;
    private String callCSUrl;
    private String callCollectionUrl;
    private String callCtiUrl;
    private String call94AiUrl;
    private String appKey;
    private String appSecret;
    private String taskIds94Ai;
    private String approvalEnterpriseId;
    private String approvalToken;
    private String fraudEnterpriseId;
    private String fraudToken;
    private Integer approvalDeptId;
    private Integer fraudDeptId;
    private Integer csDeptId;
    private Integer collectionCtiDeptId;
    private Integer collection94DeptId;
    private Integer collectionLBDeptId;
    private String ffmpegPath;
    private String callUniqueIdUrl;
    private String callCtiFileNameUrl;
    private String lbAppId;
    private String lbAppKey;
}
