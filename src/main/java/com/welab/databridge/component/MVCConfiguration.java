package com.welab.databridge.component;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Created by <PERSON> on 2018/5/31.
 */
@Configuration
public class MVCConfiguration implements WebMvcConfigurer {

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOrigins("*")
                .allowedHeaders("*")
                .allowedMethods("GET","PUT","POST","OPTIONS","DELETE")
                .maxAge(3600)
                .allowCredentials(true);
    }
}
