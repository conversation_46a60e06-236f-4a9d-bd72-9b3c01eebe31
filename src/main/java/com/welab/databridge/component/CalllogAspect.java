package com.welab.databridge.component;


import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.welab.databridge.common.ResponseVo;
import com.welab.databridge.service.impl.CallLogServiceImpl;
import com.welab.databridge.util.IpUtil;
import com.welab.databridge.util.StringUtil;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.support.AopUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import java.util.Optional;

/**
 * @Description: 统一在返回结果中添加 bizType, 即controller中 RequestMapping 注解的值
 * 前端查询历史版本时需要
 */
@Aspect
@Component
public class CalllogAspect {
    private static final Logger logger = LoggerFactory.getLogger(CalllogAspect.class);
    @Resource
    private CallLogServiceImpl callLogService;

    @AfterReturning(value = "within(@org.springframework.web.bind.annotation.RequestMapping *)", returning = "obj")
    public void addBizType(final JoinPoint joinPoint, Object obj) {
        try {
            RequestMapping rm = AopUtils.getTargetClass(joinPoint.getTarget()).getAnnotation(RequestMapping.class);
            String[] value = rm.value();
            ResponseVo responseVo = (ResponseVo) obj;
            String bizType = value[0].replace("/", "");
            if (ObjectUtil.isNotEmpty(responseVo)) {
                responseVo.setBizType(bizType);
            }
            String methodName = joinPoint.getSignature().getName();
            ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            Optional.ofNullable(requestAttributes)
                    .ifPresent(v -> {
                        HttpServletRequest request = requestAttributes.getRequest();
                        String url = request.getRequestURL().toString();
                        String ip = IpUtil.getIpAddress(request);
                        String method = request.getMethod();
                        String userId = request.getHeader("x-user-id");
                        String username = request.getHeader("x-user-name");
                        String userToken = request.getHeader("x-user-token");
                        if (StringUtil.isNotEmpty(userId) && ObjectUtil.isNotEmpty(responseVo)) {
                            Object[] args = joinPoint.getArgs();
                            Object[] arguments = new Object[args.length];
                            for (int i = 0; i < args.length; i++) {
                                if (args[i] instanceof ServletRequest || args[i] instanceof ServletResponse || args[i] instanceof MultipartFile) {
                                    //ServletRequest不能序列化，从入参里排除，否则报异常：java.lang.IllegalStateException: It is illegal to call this method if the current request is not in asynchronous mode (i.e. isAsyncStarted() returns false)
                                    //ServletResponse不能序列化 从入参里排除，否则报异常：java.lang.IllegalStateException: getOutputStream() has already been called for this response
                                    continue;
                                }
                                arguments[i] = args[i];
                            }
                            callLogService.saveCallLog(methodName, bizType, methodName, ip, method,
                                    JSON.toJSONString(arguments), responseVo.toString(), url, responseVo.getCode() + "", userId, username, userToken);
                        }
                    });
        } catch (Exception e) {
            logger.error("发生异常: ", e);
        }
    }
}
