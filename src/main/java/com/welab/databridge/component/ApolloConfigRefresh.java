package com.welab.databridge.component;

import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfigChangeListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.scope.refresh.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * Created by seven.zeng on 2022/7/21.
 */
@Component
@Slf4j
public class ApolloConfigRefresh {
    private final ConfigProperties configProperties;
    private final RefreshScope refreshScope;

    public ApolloConfigRefresh(
            final ConfigProperties configProperties,
            final RefreshScope refreshScope) {
        this.configProperties = configProperties;
        this.refreshScope = refreshScope;
    }

    @ApolloConfigChangeListener
    public void onChange(ConfigChangeEvent changeEvent) {
        log.info("before refresh {}", configProperties.toString());
        refreshScope.refresh("configProperties");
        log.info("after refresh {}", configProperties);
    }
}

