package com.welab.databridge.help;

import cn.hutool.core.util.ObjectUtil;
import com.welab.databridge.common.ResultCode;
import com.welab.databridge.enums.SendStatus;
import com.welab.databridge.util.DateUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class MessageHelper {

    public static List<String> checkData(Map<String, Object> row) {
        List<String> errors = new ArrayList<>();
        //手机号校验
        if (ObjectUtil.isEmpty(row.get("phone")) || ObjectUtil.isEmpty(row.get("phone").toString().trim())) {
            errors.add(ResultCode.PHONE_EMPTY.getMessage());
        } else {
            String phone = row.get("phone").toString().trim();
            if (phone.startsWith("+86")) {
                phone = phone.substring(3);
            }
            if (!phone.matches("^1[3456789]\\d{9}$")) {
                errors.add(ResultCode.PHONE_ERROR.getMessage());
            }
        }

        //发送时间校验
        if (ObjectUtil.isEmpty(row.get("sendTime")) || ObjectUtil.isEmpty(row.get("sendTime").toString().trim())) {
            errors.add(ResultCode.SEND_TIME_EMPTY.getMessage());
        } else {
            String sendTime = row.get("sendTime").toString().trim();
            if (!DateUtil.dateValid(sendTime)) {
                errors.add(ResultCode.SEND_TIME_ERROR.getMessage());
            }
            if (!DateUtil.earlyThanNow(sendTime)) {
                errors.add(ResultCode.SEND_TIME_LATER.getMessage());
            }
        }

        //发送内容校验
        if (ObjectUtil.isEmpty(row.get("content")) || ObjectUtil.isEmpty(row.get("content").toString().trim())) {
            errors.add(ResultCode.SEND_CONTENT_EMPTY.getMessage());
        } else {
            String content = row.get("content").toString().trim();
            if (content.length() > 512) {
                errors.add(ResultCode.SEND_CONTENT_ERROR.getMessage());
            }
        }

        //发送状态校验
        if (ObjectUtil.isEmpty(row.get("status")) || ObjectUtil.isEmpty(row.get("status").toString().trim())) {
            errors.add(ResultCode.STATUS_EMPTY.getMessage());
        } else {
            String status = row.get("status").toString().trim();
            if (SendStatus.getValueByRemark(status) == null) {
                errors.add(ResultCode.STATUS_UNKNOWN.getMessage());
            }
        }

        //供应商校验
//        if (ObjectUtil.isEmpty(row.get("supplier")) || ObjectUtil.isEmpty(row.get("supplier").toString().trim())) {
//            errors.add(ResultCode.SUPPLIER_EMPTY.getMessage());
//        }
        return errors;
    }

    public static void main(String[] args) {
        System.out.println("1342323323".matches("^1[3456789]\\d{9}$"));
    }
}
