package com.welab.databridge.help;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.dubbo.config.annotation.Reference;
import com.welab.anti.fraud.dto.voice.VoiceSendDetailReq;
import com.welab.anti.fraud.service.FraudFacadeService;
import com.welab.anti.fraud.vo.VoiceSendVo;
import com.welab.common.response.Response;
import com.welab.databridge.util.DateUtil;
import com.wolaidai.approval.Utils.PageUtils;
import com.wolaidai.approval.model.voice.VoiceSendDetail;
import com.wolaidai.approval.service.IVoiceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class AudioHelpService {
    @Reference(timeout = 20000)
    private static FraudFacadeService fraudService;
    @Reference(timeout = 20000)
    private static IVoiceService voiceService;

    /**
     * 按日期查询审批通话记录
     *
     * @param queryDate
     * @return
     */
    public static List<VoiceSendDetail> callApprovalRecord(String queryDate) {
        List<VoiceSendDetail> list = new ArrayList<>();
        com.wolaidai.approval.model.request.VoiceSendDetailReq voiceSendDetailReq = new com.wolaidai.approval.model.request.VoiceSendDetailReq();
        voiceSendDetailReq.setStartDate(queryDate);
        voiceSendDetailReq.setEndDate(DateUtil.formatDate(DateUtil.formatDate(queryDate, DateUtil.FORMAT_SIMPLE_DATE).plusDays(1), DateUtil.FORMAT_SIMPLE_DATE));
        voiceSendDetailReq.setPage(1);
        voiceSendDetailReq.setSize(10);
        PageUtils<VoiceSendDetail> voiceSendDetailPage = voiceService.getVoiceRecordList(voiceSendDetailReq);
        if (ObjectUtil.isNotEmpty(voiceSendDetailPage)) {
            list.addAll(voiceSendDetailPage.getContent());
            int total = voiceSendDetailPage.getTotalElements().intValue();
            if (total > 10) {
                int page = total%10 == 0 ? total/10 : (total/10 + 1);
                for (int i = 2; i <= page; i++) {
                    voiceSendDetailReq.setPage(i);
                    voiceSendDetailReq.setSize(10);
                    voiceSendDetailPage = voiceService.getVoiceRecordList(voiceSendDetailReq);
                    list.addAll(voiceSendDetailPage.getContent());
                }
            }
        }
        return list;
    }

    /**
     * 按日期查询反欺诈通话记录
     *
     * @param queryDate
     * @return
     */
    public static List<VoiceSendVo> callFraudRecord(String queryDate) {
        List<VoiceSendVo> list = new ArrayList<>();
        VoiceSendDetailReq voiceSendDetailReq = new VoiceSendDetailReq();
        voiceSendDetailReq.setStartDate(queryDate);
        voiceSendDetailReq.setEndDate(DateUtil.formatDate(DateUtil.formatDate(queryDate, DateUtil.FORMAT_SIMPLE_DATE).plusDays(1), DateUtil.FORMAT_SIMPLE_DATE));
        voiceSendDetailReq.setPage(1);
        voiceSendDetailReq.setSize(10);
        Response response = fraudService.getVoiceSendPage(voiceSendDetailReq);
        log.info("callFraudRecord: " + response);
        if (Response.isSuccess(response)) {
            Map result = (Map) response.getResult();
            list.addAll((List<VoiceSendVo>) result.get("list"));
            int total = Integer.parseInt(result.get("total").toString());
            if (total > 10) {
                int page = total%10 == 0 ? total/10 : (total/10 + 1);
                for (int i = 2; i <= page; i++) {
                    voiceSendDetailReq.setPage(i);
                    voiceSendDetailReq.setSize(10);
                    response = fraudService.getVoiceSendPage(voiceSendDetailReq);
                    result = (Map) response.getResult();
                    list.addAll((List<VoiceSendVo>) result.get("list"));
                }
            }

        }
        return list;
    }
}
