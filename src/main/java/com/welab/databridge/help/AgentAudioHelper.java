package com.welab.databridge.help;

import com.alibaba.fastjson.JSON;
import com.welab.databridge.util.DateUtil;
import com.welab.databridge.vo.audio.AgentAudioVo;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 坐席音频解析助手类
 * 支持15种不同的文件名格式解析
 * 
 * <AUTHOR>
 */
@Slf4j
public class AgentAudioHelper {
    
    private static final List<AgentPattern> AGENT_PATTERNS = new ArrayList<>();
    
    static {
        initializePatterns();
    }
    
    /**
     * 初始化15种文件名格式的正则表达式
     * 坐席ID格式：[字母数字组合]-[纯数字] (如: M2DD-035, WWJY-001)
     */
    private static void initializePatterns() {
        // Format 1: M2DD-035 15768330944 2023-10-16 11.24.11.mp3
        AGENT_PATTERNS.add(new AgentPattern(
            "^([A-Z0-9]+-\\d+)\\s+(1[3456789]\\d{9})\\s+(\\d{4}-\\d{2}-\\d{2}\\s+\\d{2}\\.\\d{2}\\.\\d{2}).*",
            1, 2, 3
        ));

        // Format 2: M1YY-003 13303401113-20250201154900.mp3
        AGENT_PATTERNS.add(new AgentPattern(
            "^([A-Z0-9]+-\\d+)\\s+(1[3456789]\\d{9})-(\\d{8}\\d{6}).*",
            1, 2, 3
        ));

        // Format 3: M2ZM-006_13962472850_20250707-110609.amr
        AGENT_PATTERNS.add(new AgentPattern(
            "^([A-Z0-9]+-\\d+)_(1[3456789]\\d{9})_(\\d{8}-\\d{6}).*",
            1, 2, 3
        ));

        // Format 4: M1ZM-002 13787090043-20250630 154505.mp3
        AGENT_PATTERNS.add(new AgentPattern(
            "^([A-Z0-9]+-\\d+)\\s+(1[3456789]\\d{9})-(\\d{8}\\s+\\d{6}).*",
            1, 2, 3
        ));

        // Format 5: M2ZM-056 13548624887 2025-06-29 15-50-38.m4a
        AGENT_PATTERNS.add(new AgentPattern(
            "^([A-Z0-9]+-\\d+)\\s+(1[3456789]\\d{9})\\s+(\\d{4}-\\d{2}-\\d{2}\\s+\\d{2}-\\d{2}-\\d{2}).*",
            1, 2, 3
        ));

        // Format 6: M2ZM-027 184 3941 7638_ 20250610175057.m4a
        AGENT_PATTERNS.add(new AgentPattern(
            "^([A-Z0-9]+-\\d+)\\s+(1\\d{2}\\s+\\d{4}\\s+\\d{4})_\\s+(\\d{14}).*",
            1, 2, 3
        ));

        // Format 7: WWJY-001-13345698752-20250708-142552-13345698752.MP3
        AGENT_PATTERNS.add(new AgentPattern(
            "^([A-Z0-9]+-\\d+)-(1[3456789]\\d{9})-(\\d{8}-\\d{6}).*",
            1, 2, 3
        ));

        // Format 8: WWJY-001-20250708-111811-13604352336.mp3
        AGENT_PATTERNS.add(new AgentPattern(
            "^([A-Z0-9]+-\\d+)-(\\d{8}-\\d{6})-(1[3456789]\\d{9}).*",
            1, 3, 2
        ));

        // Format 9: WWJLXTJ-003 1948-20250707-182046-18185266018-45110022
        AGENT_PATTERNS.add(new AgentPattern(
            "^([A-Z0-9]+-\\d+)\\s+\\d+-(\\d{8}-\\d{6})-(1[3456789]\\d{9})-.*",
            1, 3, 2
        ));

        // Format 10: WWJLXTJ-003 20250707100544_15851381861_60000008141016
        AGENT_PATTERNS.add(new AgentPattern(
            "^([A-Z0-9]+-\\d+)\\s+(\\d{14})_(1[3456789]\\d{9})_.*",
            1, 3, 2
        ));

        // Format 11: WWJLXTJ-003 13671837968-20250702143046-
        AGENT_PATTERNS.add(new AgentPattern(
            "^([A-Z0-9]+-\\d+)\\s+(1[3456789]\\d{9})-(\\d{12})-.*",
            1, 2, 3
        ));

        // Format 12: M2JLXTJ-003 15684099986-2507051644.awb
        AGENT_PATTERNS.add(new AgentPattern(
            "^([A-Z0-9]+-\\d+)\\s+(1[3456789]\\d{9})-(\\d{10})",
            1, 2, 3
        ));

        // Format 13: WWZM-213 15861171031-20231017 121240.wav
        AGENT_PATTERNS.add(new AgentPattern(
            "^([A-Z0-9]+-\\d+)\\s+(1[3456789]\\d{9})-(\\d{8}\\s+\\d{6}).*",
            1, 2, 3
        ));

        // Format 14: WWZM-213 13413021083-20231016 0911.wav
        AGENT_PATTERNS.add(new AgentPattern(
            "^([A-Z0-9]+-\\d+)\\s+(1[3456789]\\d{9})-(\\d{8}\\s+\\d{4}).*",
            1, 2, 3
        ));

        // Format 15: WWYXTJ-003 17665271076 2023-10-17-14-41-25
        AGENT_PATTERNS.add(new AgentPattern(
            "^([A-Z0-9]+-\\d+)\\s+(1[3456789]\\d{9})\\s+(\\d{4}-\\d{2}-\\d{2}-\\d{2}-\\d{2}-\\d{2}).*",
            1, 2, 3
        ));


        // Format 16: M1JLXTJ-009 13220512218-202508041655.mp3
        AGENT_PATTERNS.add(new AgentPattern(
                "^([A-Z0-9]+-\\d+)\\s+(1[3456789]\\d{9})-(\\d{12})",
                1, 2, 3
        ));
    }
    
    /**
     * 解析文件名提取坐席、手机号和时间信息
     * 
     * @param fileName 文件名
     * @return AgentAudioVo 包含解析结果
     */
    public static AgentAudioVo parseAgentFileName(String fileName) {
        AgentAudioVo agentAudioVo = new AgentAudioVo();
        agentAudioVo.setSource("filename");
        
        if (fileName == null || fileName.trim().isEmpty()) {
            return agentAudioVo;
        }
        
        // 移除文件扩展名并清理特殊字符
        String cleanFileName = fileName.trim();
        if (cleanFileName.contains(".")) {
            cleanFileName = cleanFileName.substring(0, cleanFileName.lastIndexOf("."));
        }
        cleanFileName = cleanFileName.replace("：", ":").replace("\\u00A0", " ");
        
        log.debug("Parsing filename: {}", cleanFileName);
        
        for (AgentPattern agentPattern : AGENT_PATTERNS) {
            Matcher matcher = agentPattern.pattern.matcher(cleanFileName);
            if (matcher.matches()) {
                try {
                    String agentId = matcher.group(agentPattern.agentGroup);
                    String mobile = matcher.group(agentPattern.mobileGroup);
                    String timeStr = matcher.group(agentPattern.timeGroup);
                    
                    agentAudioVo.setAgentId(agentId);
                    agentAudioVo.setMobile(cleanMobile(mobile));
                    agentAudioVo.setStartTime(processTimeString(timeStr));
                    
                    log.debug("Successfully parsed - Agent: {}, Mobile: {}, Time: {}", 
                             agentId, agentAudioVo.getMobile(), agentAudioVo.getStartTime());
                    return agentAudioVo;
                } catch (Exception e) {
                    log.warn("Error parsing filename with pattern: {}, error: {}", 
                            agentPattern.pattern.pattern(), e.getMessage());
                }
            }
        }
        
        log.debug("No pattern matched for filename: {}", cleanFileName);
        return agentAudioVo;
    }
    
    /**
     * 清理手机号格式
     */
    private static String cleanMobile(String mobile) {
        if (mobile == null) return null;
        return mobile.replace(" ", "").replace("-", "").replace("_", "");
    }
    
    /**
     * 处理时间字符串，转换为标准格式
     */
    private static String processTimeString(String timeStr) {
        if (timeStr == null || timeStr.trim().isEmpty()) {
            return null;
        }
        
        try {
            // 清理时间字符串
            String cleanTime = timeStr.replace(" ", "").replace("-", "").replace("_", "")
                    .replace(":", "").replace("：", "").replace(".", "");
            
            return DateUtil.processDateStr(cleanTime);
        } catch (Exception e) {
            log.warn("Error processing time string: {}, error: {}", timeStr, e.getMessage());
            return null;
        }
    }
    
    /**
     * 内部类：坐席模式定义
     */
    private static class AgentPattern {
        final Pattern pattern;
        final int agentGroup;
        final int mobileGroup;
        final int timeGroup;
        
        AgentPattern(String regex, int agentGroup, int mobileGroup, int timeGroup) {
            this.pattern = Pattern.compile(regex);
            this.agentGroup = agentGroup;
            this.mobileGroup = mobileGroup;
            this.timeGroup = timeGroup;
        }
    }

    public static void main(String[] args) {
        AgentAudioVo agentAudioVo = AgentAudioHelper.parseAgentFileName("M1JLXTJ-009 13220512218-202508041655.mp3");
        System.out.println("agentAudioVo = " + JSON.toJSONString(agentAudioVo));
    }
}
