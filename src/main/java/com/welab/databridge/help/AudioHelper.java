package com.welab.databridge.help;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.welab.databridge.entity.AudioRecord;
import com.welab.databridge.entity.AudioTemp;
import com.welab.databridge.enums.CallStatus;
import com.welab.databridge.enums.SupplierType;
import com.welab.databridge.util.*;
import com.welab.databridge.vo.audio.AudioVo;
import com.welab.databridge.vo.audio.CollectionRecordVo;
import com.welab.databridge.vo.file.FileVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import ws.schild.jave.MultimediaObject;
import ws.schild.jave.info.MultimediaInfo;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.nio.file.attribute.PosixFilePermission;
import java.time.Instant;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
public class AudioHelper {
    public static List<Pattern> patternList = new ArrayList<>();

    static {
        for (String fileNameRegex : ConfigUtil.config.getFileNameRegex().split(";")) {
            Pattern pattern = Pattern.compile(fileNameRegex);
            patternList.add(pattern);
        }
    }
    public static AudioVo parseFileName(String fileName) {
        AudioVo audioVo = new AudioVo();

        String str = fileName.trim().substring(0, fileName.lastIndexOf(".")).replace("：", ":").replace("\\u00A0", " ");
        Matcher matcher;
        for (Pattern p : patternList) {
            matcher = p.matcher(str);
            if (matcher.matches()) {
                setValue(audioVo, matcher.group(1), matcher.group(2));
                return audioVo;
            }
        }

        return audioVo;
    }

    public static void setValue(AudioVo audioVo, String group1, String group2) {
        group1 = group1.replace(" ", "").replace("-", "").replace("_", "")
                .replace(":", "").replace("：", "").replace(".", "");
        group2 = group2.replace(" ", "").replace("-", "").replace("_", "")
                .replace(":", "").replace("：", "").replace(".", "");
        if (group1.length() == 11) {
            audioVo.setMobile(group1);
            audioVo.setStartTime(DateUtil.processDateStr(group2));
        } else if (group2.length() == 11) {
            audioVo.setMobile(group2);
            audioVo.setStartTime(DateUtil.processDateStr(group1));
        }
    }

    public static JSONObject processCtiData(List<AudioTemp> audioTemps) {
        JSONObject data = new JSONObject();
        if (ObjectUtil.isNotEmpty(audioTemps)) {
            List<AudioTemp> list = audioTemps.stream()
                    .filter(at -> StringUtil.isNotEmpty(at.getRecordFile())
                            && CallStatus.SUCCESS.getRemark().equals(at.getCallStatus()))
                    .collect(Collectors.toList());
            if (ObjectUtil.isEmpty(list)) {
                return data;
            }
            String region = list.get(0).getRegion();
            String enterpriseId = list.get(0).getEnterpriseId();
            String token = list.get(0).getSign();
            String recordFile;
            if (list.size() > 50) {
                for (int i=0; i < (list.size()/50) + 1; i++) {
                    List<AudioTemp> subList;
                    if ((i + 1) * 50 > list.size()) {
                        subList = list.subList(i * 50, list.size());
                    } else {
                        subList = list.subList(i * 50, (i + 1) * 50);
                    }
                    recordFile = subList.stream().map(AudioTemp::getRecordFile).collect(Collectors.joining(","));
                    JSONObject result = callCtiCloud(region, enterpriseId, token, recordFile);
                    BeanUtil.copyProperties(result, data);
                }
            } else {
                recordFile = list.stream().map(AudioTemp::getRecordFile).collect(Collectors.joining(","));
                data = callCtiCloud(region, enterpriseId, token, recordFile);
            }
        }
        return data;
    }

    public static JSONObject callCtiCloud(String region, String enterpriseId, String token, String recordFile) {
        JSONObject data = new JSONObject();
        Long timestamp = System.currentTimeMillis() / 1000;
        StringBuilder urlBuf = new StringBuilder();
        urlBuf.append(ConfigUtil.config.getCallCtiUrl().replace("region", region))
                .append("?")
                .append("validateType=2")
                .append("&enterpriseId=").append(enterpriseId)
                .append("&timestamp=").append(timestamp)
                .append("&sign=").append(Md5Util.md5(enterpriseId + timestamp.toString() + token))
                .append("&recordType=record")
                .append("&recordFile=").append(recordFile)
                .append("&download=1");
        String result = HttpClientUtil.sendGet(urlBuf.toString(), null);
        if (ObjectUtil.isNotEmpty(result)) {
            JSONObject resultObj = JSON.parseObject(result);
            if (ObjectUtil.isNotEmpty(resultObj) && "0".equals(resultObj.getString("result"))) {
                data = resultObj.getJSONObject("data");
            }
        }
        return data;
    }

    public static JSONArray pullCtiRecord(String url, String region, String enterpriseId, String token, String month, long id, String field) {
        JSONArray arr = new JSONArray();
        Long timestamp = System.currentTimeMillis() / 1000;
        StringBuilder urlBuf = new StringBuilder();
        urlBuf.append(url.replace("region", region))
                .append("?")
                .append("validateType=2")
                .append("&enterpriseId=").append(enterpriseId)
                .append("&timestamp=").append(timestamp)
                .append("&sign=").append(Md5Util.md5(enterpriseId + timestamp.toString() + token))
                .append("&month=").append(month)
                .append("&id=").append(id)
                .append("&limit=3000");
        String result = HttpClientUtil.sendGet(urlBuf.toString(), null);
        if (ObjectUtil.isNotEmpty(result)) {
            JSONObject resultObj = JSON.parseObject(result);
            if (ObjectUtil.isNotEmpty(resultObj) && "0".equals(resultObj.getString("result"))) {
                JSONObject data = resultObj.getJSONObject("data");
                if (ObjectUtil.isNotEmpty(data) && data.containsKey(field)) {
                    arr = data.getJSONArray(field);
                }
            }
        }
        return arr;
    }

    public static CollectionRecordVo buildCollectionRecordVo(JSONObject data, String token, String enterpriseId, String enterpriseName, String region) {
        if (StringUtil.isEmpty(data.getString("customer_number")) || data.getString("customer_number").length() > 99) {
            return null;
        }
        CollectionRecordVo collectionRecordVo = new CollectionRecordVo();
        collectionRecordVo.setMobile(data.getString("customer_number"));
        if (StringUtil.isNotEmpty(data.getString("callee_cno"))) {
            collectionRecordVo.setCno(data.getString("callee_cno"));
        } else {
            collectionRecordVo.setCno(data.getString("cno"));
        }

        collectionRecordVo.setSign(token);
        JSONArray record_files = data.getJSONArray("record_file");
        if (ObjectUtil.isNotEmpty(record_files)) {
            collectionRecordVo.setRecordFile(record_files.getJSONObject(0).getString("file"));
        }
        collectionRecordVo.setVersion("v10");
        collectionRecordVo.setProvider("TR");
        String status = data.getString("status");
        List<String> successCode = Arrays.asList("1", "24", "33", "41", "43", "53");
        if (successCode.contains(status)) {
            collectionRecordVo.setCallStatus(CallStatus.SUCCESS.getRemark());
        } else {
            collectionRecordVo.setCallStatus(CallStatus.FAIL.getRemark());
        }
        collectionRecordVo.setStaffName(data.getString("agent_name"));
        if (StringUtil.isNotEmpty(data.getString("bridge_time"))
                && !"0".equals(data.getString("bridge_time"))) {
            collectionRecordVo.setStartTime(DateUtil.format(new Date(Long.parseLong(data.getString("bridge_time")) * 1000), null));
        } else if (StringUtil.isNotEmpty(data.getString("start_time"))
                && !"0".equals(data.getString("start_time"))) {
            collectionRecordVo.setStartTime(DateUtil.format(new Date(Long.parseLong(data.getString("start_time")) * 1000), null));
        }
        if (StringUtil.isNotEmpty(data.getString("end_time"))
                && !"0".equals(data.getString("end_time"))) {
            collectionRecordVo.setEndTime(DateUtil.format(new Date(Long.parseLong(data.getString("end_time")) * 1000), null));
        }
        collectionRecordVo.setEnterpriseId(enterpriseId);
        collectionRecordVo.setEnterpriseName(enterpriseName);
        collectionRecordVo.setRegion(region);
        collectionRecordVo.setQueryDate(DateUtil.format(DateUtil.format(collectionRecordVo.getStartTime(), null), DateUtil.FORMAT_SIMPLE_DATE));
        return collectionRecordVo;
    }

    public static List<JSONObject> process94AiData(List<AudioTemp> audioTemps) {
        List<JSONObject> data = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(audioTemps)) {
            List<AudioTemp> list = audioTemps.stream()
                    .filter(at -> StringUtil.isNotEmpty(at.getMobile())
                            && CallStatus.SUCCESS.getRemark().equals(at.getCallStatus()))
                    .collect(Collectors.toList());
            if (ObjectUtil.isEmpty(list)) {
                return data;
            }
            String queryDate = list.get(0).getQueryDate();
            List<String> numbers;
            if (list.size() > 500) {
                for (int i=0; i < (list.size()/500) + 1; i++) {
                    List<AudioTemp> subList;
                    if ((i + 1) * 500 > list.size()) {
                        subList = list.subList(i * 500, list.size());
                    } else {
                        subList = list.subList(i * 500, (i + 1) * 500);
                    }
                    numbers = subList.stream().map(AudioTemp::getMobile).collect(Collectors.toList());
                    log.info("call 94ai number size:" + numbers.size());
                    List<JSONObject> dataList = call94Ai(queryDate, numbers);
                    subList.clear();
                    numbers.clear();
                    data.addAll(dataList);
                }
            } else {
                numbers = list.stream().map(AudioTemp::getMobile).collect(Collectors.toList());
                log.info("call 94ai number size:" + numbers.size());
                data = call94Ai(queryDate, numbers);
            }
        }
        return data;
    }

    public static List<JSONObject> fix94AiData(String queryDate, List<AudioRecord> audioRecords) {
        List<JSONObject> data = new ArrayList<>();

        if (ObjectUtil.isEmpty(audioRecords)) {
            return data;
        }

        List<String> numbers;
        if (audioRecords.size() > 500) {
            for (int i=0; i < (audioRecords.size()/500) + 1; i++) {
                List<AudioRecord> subList;
                if ((i + 1) * 500 > audioRecords.size()) {
                    subList = audioRecords.subList(i * 500, audioRecords.size());
                } else {
                    subList = audioRecords.subList(i * 500, (i + 1) * 500);
                }
                numbers = subList.stream().map(AudioRecord::getPhone).collect(Collectors.toList());
                log.info("call 94ai number size:" + numbers.size());
                List<JSONObject> dataList = call94Ai(queryDate, numbers);
                subList.clear();
                numbers.clear();
                data.addAll(dataList);
            }
        } else {
            numbers = audioRecords.stream().map(AudioRecord::getPhone).collect(Collectors.toList());
            log.info("call 94ai number size:" + numbers.size());
            data = call94Ai(queryDate, numbers);
        }

        return data;
    }

    public static List<JSONObject> call94Ai(String queryDate, List<String> numbers) {
        List<JSONObject> list = new ArrayList<>();
        Long timestamp = System.currentTimeMillis();
        String sign = ApiKit.getSign(ConfigUtil.config.getAppKey(), ConfigUtil.config.getAppSecret(), timestamp.toString());
        Map<String, String> header = new HashMap<>();
        header.put("appKey", ConfigUtil.config.getAppKey());
        header.put("timestamp", timestamp.toString());
        header.put("sign", sign);
        header.put("Content-Type", "application/json");
        header.put("Accept", "application/json");
        int[] taskIds = Arrays.stream(ConfigUtil.config.getTaskIds94Ai().split(","))
                .mapToInt(v -> Integer.parseInt(v))
                .toArray();
        String result;
        queryDate = queryDate + " 00:00:00";
        for (int taskId : taskIds) {
            JSONObject body = new JSONObject();
            body.put("taskId", taskId);
            body.put("page", 1);
            body.put("callDate", queryDate);
            body.put("endCallDate", DateUtil.format(DateUtil.format(queryDate, DateUtil.FORMAT_FULL_DATE).plusDays(1), null));
            body.put("numbers", numbers);
            try {
                log.info("call94Ai param: " + body + ", taskId:" + taskId);
                result = HttpClientUtil.send(ConfigUtil.config.getCall94AiUrl(), header, body.toJSONString());
                log.info("call94Ai result: " + result + ", taskId:" + taskId);
                if (ObjectUtil.isNotEmpty(result)) {
                    JSONObject resultObj = JSON.parseObject(result);
                    if(ObjectUtil.isNotEmpty(resultObj) && "200".equals(resultObj.getString("code"))) {
                        JSONObject data = resultObj.getJSONObject("data");
                        if (ObjectUtil.isNotEmpty(data) && data.containsKey("list")) {
                            list.addAll(data.getJSONArray("list").toJavaList(JSONObject.class));
                        }
                    }
                }
            } catch (IOException e) {
                log.error("call94Ai error: " + e.getMessage() + ", body: " + body);
            }
        }
        return list;
    }

    public static InputStream callLB(String url) throws IOException {
        long now = Instant.now().toEpochMilli();
        String nonce = StringUtil.random(9);
        String sign = Md5Util.md5(ConfigUtil.config.getLbAppId() + now + nonce + ConfigUtil.config.getLbAppKey());
        StringBuilder sb = new StringBuilder();
        sb.append(url)
                .append("?appid=")
                .append(ConfigUtil.config.getLbAppId())
                .append("&timestamp=")
                .append(now)
                .append("&nonce=")
                .append(nonce)
                .append("&signType=md5&signature=")
                .append(sign);
        return HttpClientUtil.sendWithReturnInputstream(sb.toString(), "GET", null, null);
    }

    /**
     * 按日期查询客服通话记录
     *
     * @param queryDate
     * @return
     */
    public static List<CollectionRecordVo> callCSRecord(String queryDate) {
        String url = ConfigUtil.config.getCallCSUrl() + "?queryDate=" + queryDate;
        List<CollectionRecordVo> list = new ArrayList<>();
        String result = HttpClientUtil.sendGet(url, null);
        if (StringUtil.isNotEmpty(result)) {
            JSONObject resultObj = JSON.parseObject(result);
            if (ObjectUtil.isNotEmpty(resultObj) && "0".equals(resultObj.getString("code"))) {
                JSONArray jsonArray = resultObj.getJSONArray("result");
                if (ObjectUtil.isNotEmpty(jsonArray)) {
                    list = jsonArray.toJavaList(CollectionRecordVo.class);
                }
            }
        }
        return list;
    }

    /**
     * 按日期查询催收通话记录
     *
     * @param queryDate
     * @return
     */
    public static List<CollectionRecordVo> callCollectionRecord(String queryDate) {
        String url = ConfigUtil.config.getCallCollectionUrl() + "?queryDate=" + queryDate;
        return queryRecord(url);
    }

    private static List<CollectionRecordVo> queryRecord(String url) {
        List<CollectionRecordVo> list = new ArrayList<>();
        String result = HttpClientUtil.sendGet(url, null);
        if (StringUtil.isNotEmpty(result)) {
            JSONObject resultObj = JSON.parseObject(result);
            if (ObjectUtil.isNotEmpty(resultObj) && "200".equals(resultObj.getString("status"))) {
                JSONArray jsonArray = resultObj.getJSONArray("data");
                if (ObjectUtil.isNotEmpty(jsonArray)) {
                    list = jsonArray.toJavaList(CollectionRecordVo.class);
                }
            }
        }
        return list;
    }

    public static Integer getDeptIdByDeptName(String name, String provider) {
        if ("审批".equals(name)) {
            return ConfigUtil.config.getApprovalDeptId();
        } else if ("反欺诈".equals(name)) {
            return ConfigUtil.config.getFraudDeptId();
        } else if ("催收".equals(name)) {
            if (SupplierType.ST_CTI.getKey().equals(provider)) {
                return ConfigUtil.config.getCollectionCtiDeptId();
            } else if (SupplierType.ST_94AI.getKey().equals(provider)) {
                return ConfigUtil.config.getCollection94DeptId();
            } else if (SupplierType.ST_LB.getKey().equals(provider)) {
                return ConfigUtil.config.getCollectionLBDeptId();
            }
        } else if ("客服".equals(name)) {
            return ConfigUtil.config.getCsDeptId();
        }
        return -1;
    }

    /**
     * 将音频文件转换为MP3格式
     * 支持的格式：.amr, .awb
     *
     * @param fileVo 文件对象，包含文件名和输入流
     */
    public static void changeAmrToMp3(FileVo fileVo) {
        String fileName = fileVo.getFileName().toLowerCase();

        // 检查是否为支持的音频格式
        if (!fileName.endsWith(".amr") && !fileName.endsWith(".awb")) {
            return;
        }

        // 清理文件名中的空格
        fileVo.setFileName(fileVo.getFileName().replace(" ", ""));

        // 转换音频文件为MP3格式
        fileVo.setInputStream(convertAudioToMP3(fileVo.getInputStream(), fileVo.getFileName()));

        // 更新文件扩展名为.mp3
        String originalFileName = fileVo.getFileName();
        String mp3FileName;
        if (originalFileName.toLowerCase().endsWith(".amr")) {
            mp3FileName = originalFileName.substring(0, originalFileName.length() - 4) + ".mp3";
        } else if (originalFileName.toLowerCase().endsWith(".awb")) {
            mp3FileName = originalFileName.substring(0, originalFileName.length() - 4) + ".mp3";
        } else {
            // 备用处理，理论上不会执行到这里
            mp3FileName = originalFileName + ".mp3";
        }
        fileVo.setFileName(mp3FileName);

        log.info("音频文件转换完成: {} -> {}", originalFileName, mp3FileName);
    }

    /**
     * 将音频文件输入流转为MP3格式
     * 支持的格式：.amr, .awb
     *
     * @param inputStream  音频文件的输入流
     * @param fileName  文件名（包含后缀）
     * @return MP3格式的输入流
     */
    public static InputStream convertAudioToMP3(InputStream inputStream, String fileName) {
        String ffmpegPath = ConfigUtil.config.getFfmpegPath();
        Runtime runtime = Runtime.getRuntime();

        // 获取文件扩展名
        String fileExtension = getFileExtension(fileName).toLowerCase();

        try {
            String filePath = copyFile(inputStream, fileName);
            String substring = filePath.substring(0, filePath.lastIndexOf("."));
            String mp3FilePath = substring + ".mp3";

            log.info("音频转换开始 - 源文件: {} ({}), 目标文件: {}", filePath, fileExtension, mp3FilePath);

            // 根据不同的音频格式构建ffmpeg命令
            String command = buildFfmpegCommand(ffmpegPath, filePath, mp3FilePath, fileExtension);

            log.info("执行转换命令: {}", command);
            Process p = runtime.exec(command);

            // 等待转换完成并释放进程资源
            int exitCode = p.waitFor();
            p.getOutputStream().close();
            p.getInputStream().close();
            p.getErrorStream().close();

            if (exitCode != 0) {
                log.error("音频转换失败，退出码: {}", exitCode);
                return null;
            }

            File file = new File("/data/MP3TempFile/", FilenameUtils.getName(mp3FilePath));
            if (!file.exists()) {
                log.error("转换后的MP3文件不存在: {}", file.getAbsolutePath());
                return null;
            }

            InputStream fileInputStream = new FileInputStream(file);
            log.info("音频转换成功: {} -> {}", fileName, mp3FilePath);
            return fileInputStream;

        } catch (InterruptedException e) {
            log.error("音频转换被中断: {}", e.getMessage());
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            log.error("音频转换异常: {}", e.getMessage(), e);
        } finally {
            runtime.freeMemory();
        }
        return null;
    }

    /**
     * 根据音频格式构建ffmpeg转换命令
     *
     * @param ffmpegPath ffmpeg可执行文件路径
     * @param inputPath 输入文件路径
     * @param outputPath 输出文件路径
     * @param fileExtension 文件扩展名
     * @return ffmpeg命令字符串
     */
    private static String buildFfmpegCommand(String ffmpegPath, String inputPath, String outputPath, String fileExtension) {
        StringBuilder command = new StringBuilder();
        command.append(ffmpegPath).append("ffmpeg -i ").append(inputPath);

        // 根据不同的音频格式设置特定的转换参数
        switch (fileExtension) {
            case "amr":
                // AMR格式转换参数
                command.append(" -acodec libmp3lame -ab 128k");
                break;
            case "awb":
                // AWB格式转换参数（AWB是AMR-WB的扩展）
                command.append(" -acodec libmp3lame -ab 128k -ar 16000");
                break;
            default:
                // 默认转换参数
                command.append(" -acodec libmp3lame -ab 128k");
                log.warn("未知的音频格式: {}，使用默认转换参数", fileExtension);
                break;
        }

        command.append(" ").append(outputPath);
        return command.toString();
    }

    /**
     * 获取文件扩展名
     *
     * @param fileName 文件名
     * @return 文件扩展名（不包含点号）
     */
    private static String getFileExtension(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "";
        }
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == fileName.length() - 1) {
            return "";
        }
        return fileName.substring(lastDotIndex + 1);
    }

    /**
     * 向后兼容方法：将AMR文件输入流转为MP3格式
     *
     * @deprecated 请使用 {@link #convertAudioToMP3(InputStream, String)} 方法
     * @param inputStream AMR文件的输入流
     * @param fileName 文件名（包含后缀）
     * @return MP3格式的输入流
     */
    @Deprecated
    public static InputStream amrToMP3(InputStream inputStream, String fileName) {
        return convertAudioToMP3(inputStream, fileName);
    }

    /**
     * 将用户输入的amr音频文件流转为音频文件并存入临时文件夹中
     * @param inputStream  输入流
     * @param fileName  文件姓名
     * @return  amr临时文件存放地址
     * @throws IOException
     */
    public static String copyFile(InputStream inputStream, String fileName) {
        String filePath = "/data/MP3TempFile/"; //创建临时目录
        File dir = new File(filePath);
        if (!dir.exists()) {
            dir.mkdir();
        }

        File outPutFile = new File(filePath, FilenameUtils.getName(fileName));

        try (OutputStream outputStream = new FileOutputStream(outPutFile)) {
            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
        } catch (IOException e) {
            log.error("copy file error: " + e.getMessage());
        }

        return outPutFile.getAbsolutePath();
    }

    /**
     * 删除音频转换过程中产生的临时文件
     * 支持删除 .amr, .awb, .mp3 格式的临时文件
     *
     * @param fileName 原始文件名（包含扩展名）
     */
    public static void deleteTempFile(String fileName) {
        if (StringUtil.isEmpty(fileName)) {
            log.warn("文件名为空，跳过删除操作");
            return;
        }

        String mp3TempPath = "/data/MP3TempFile/";
        String audioTempPath = "/data/audioTempFile/";

        // 获取不带扩展名的文件名
        String baseFileName = getBaseFileName(fileName);
        String cleanFileName = FilenameUtils.getName(baseFileName);

        // 定义需要删除的文件类型
        String[] extensions = {".amr", ".awb", ".mp3"};
        String[] tempPaths = {mp3TempPath, mp3TempPath, mp3TempPath, audioTempPath, audioTempPath, audioTempPath};

        int deletedCount = 0;

        // 删除MP3临时目录中的文件
        for (String ext : extensions) {
            File tempFile = new File(mp3TempPath, cleanFileName + ext);
            if (tempFile.exists()) {
                boolean deleted = tempFile.delete();
                if (deleted) {
                    deletedCount++;
                    log.info("删除MP3临时文件成功: {}{}", cleanFileName, ext);
                } else {
                    log.warn("删除MP3临时文件失败: {}{}", cleanFileName, ext);
                }
            }
        }

        // 删除音频临时目录中的文件
        for (String ext : extensions) {
            File tempFile = new File(audioTempPath, cleanFileName + ext);
            if (tempFile.exists()) {
                boolean deleted = tempFile.delete();
                if (deleted) {
                    deletedCount++;
                    log.info("删除音频临时文件成功: {}{}", cleanFileName, ext);
                } else {
                    log.warn("删除音频临时文件失败: {}{}", cleanFileName, ext);
                }
            }
        }

        // 删除原始文件名对应的临时文件
        deleteOriginalTempFile(fileName, audioTempPath);
        deleteOriginalTempFile(fileName, mp3TempPath);

        log.info("临时文件清理完成，共删除 {} 个文件，基础文件名: {}", deletedCount, cleanFileName);
    }

    /**
     * 删除指定路径下的原始文件
     */
    private static void deleteOriginalTempFile(String fileName, String tempPath) {
        try {
            File originalFile = new File(tempPath, FilenameUtils.getName(fileName));
            if (originalFile.exists()) {
                boolean deleted = originalFile.delete();
                if (deleted) {
                    log.info("删除原始临时文件成功: {}", fileName);
                } else {
                    log.warn("删除原始临时文件失败: {}", fileName);
                }
            }
        } catch (Exception e) {
            log.error("删除原始临时文件异常: {}", fileName, e);
        }
    }

    /**
     * 获取不带扩展名的文件名
     */
    private static String getBaseFileName(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "";
        }
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex == -1) {
            return fileName;
        }
        return fileName.substring(0, lastDotIndex);
    }

    /**
     * 查询客服通话记录uniqueId
     *
     * @param mobile 客户手机号
     * @param startTime 拨打时间
     * @return
     */
    public static String callCSUniqueId(String mobile, String startTime) {
        String uniqueId = null;
        JSONObject body = new JSONObject();
        body.put("callTime", startTime);
        body.put("mobile", mobile);

        try {
            String result = HttpClientUtil.send(ConfigUtil.config.getCallUniqueIdUrl(), body.toJSONString());
            log.info("callCSUniqueId result: " + result);
            if (StringUtil.isNotEmpty(result)) {
                JSONObject resultObj = JSON.parseObject(result);
                if (ObjectUtil.isNotEmpty(resultObj) && "200".equals(resultObj.getString("status"))) {
                    uniqueId = resultObj.getString("data");
                }
            }
        } catch (Exception e) {
            log.warn("callCSUniqueId error, ex: " + e.getMessage());
        }
        return uniqueId;
    }

    public static String getNullFileName(String region, String enterpriseId, String token, String uniqueId) {
        String fileName = null;
        Long timestamp = System.currentTimeMillis() / 1000;
        StringBuilder urlBuf = new StringBuilder();
        urlBuf.append(ConfigUtil.config.getCallCtiFileNameUrl().replace("region", region))
                .append("?")
                .append("validateType=2")
                .append("&enterpriseId=").append(enterpriseId)
                .append("&timestamp=").append(timestamp)
                .append("&sign=").append(Md5Util.md5(enterpriseId + timestamp.toString() + token))
                .append("&uniqueId=").append(uniqueId);
        String result = HttpClientUtil.sendGet(urlBuf.toString(), null);
        log.info("uniqueId: " + uniqueId + " result:" + result);
        if (ObjectUtil.isNotEmpty(result)) {
            JSONObject resultObj = JSON.parseObject(result);
            if (ObjectUtil.isNotEmpty(resultObj) && "0".equals(resultObj.getString("result"))) {
                JSONArray dataArr = resultObj.getJSONArray("data");
                if (dataArr != null && !dataArr.isEmpty()) {
                    JSONObject data = dataArr.getJSONObject(0);
                    fileName = data.getString("recordFile");
                }
            }
        }
        return fileName;
    }


    public static Integer getDuration(FileVo fileVo) {
        try {
            // 定义临时文件目录
            Path tempDir = Paths.get("/data/audioTempFile/");
            // 确保目录存在
            Files.createDirectories(tempDir);
            // 创建临时文件
            Path tempFile = Files.createFile(tempDir.resolve(fileVo.getFileName()));

            // 将输入流复制到临时文件
            try (InputStream inputStream = fileVo.getInputStream()) {
                Files.copy(inputStream, tempFile, StandardCopyOption.REPLACE_EXISTING);
            }

            // 尝试使用增强的JAVE配置获取音频时长
            Integer duration = getDurationWithEnhancedJave(tempFile.toFile());
            if (duration != null) {
                fileVo.setInputStream(Files.newInputStream(tempFile));
                return duration;
            }

            // 如果JAVE失败，尝试使用系统ffmpeg
            duration = getDurationWithSystemFfmpeg(tempFile.toString());
            if (duration != null) {
                fileVo.setInputStream(Files.newInputStream(tempFile));
                return duration;
            }

            log.warn("无法获取音频时长，返回默认值");
            fileVo.setInputStream(Files.newInputStream(tempFile));
            return 0; // 返回默认值而不是null

        } catch (Exception e) {
	        log.error("Error getting audio duration: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 使用增强配置的JAVE获取音频时长
     */
    private static Integer getDurationWithEnhancedJave(File audioFile) {
        try {
            // 设置JAVE临时目录
            setupJaveTempDirectory();

            // 尝试修复ffmpeg权限
            fixFfmpegPermissions();

            MultimediaObject multimediaObject = new MultimediaObject(audioFile);
            MultimediaInfo info = multimediaObject.getInfo();
            return Math.toIntExact(info.getDuration() / 1000);

        } catch (Exception e) {
            log.warn("JAVE获取音频时长失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 使用系统ffmpeg获取音频时长（备用方案）
     */
    private static Integer getDurationWithSystemFfmpeg(String audioFilePath) {
        try {
            String ffmpegPath = ConfigUtil.config.getFfmpegPath();
            if (StringUtil.isEmpty(ffmpegPath)) {
                ffmpegPath = "/usr/bin/"; // 默认路径
            }

            String command = ffmpegPath + "ffprobe -v quiet -show_entries format=duration -of csv=p=0 " + audioFilePath;
            log.info("使用系统ffmpeg获取时长: {}", command);

            Process process = Runtime.getRuntime().exec(command);
            process.waitFor();

            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String durationStr = reader.readLine();
                if (StringUtil.isNotEmpty(durationStr)) {
                    double duration = Double.parseDouble(durationStr.trim());
                    return (int) Math.round(duration);
                }
            }

        } catch (Exception e) {
            log.warn("系统ffmpeg获取音频时长失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 设置JAVE临时目录
     */
    private static void setupJaveTempDirectory() {
        try {
            // 优先使用环境变量指定的目录
            String javeTempDir = System.getenv("JAVE_TEMP_DIR");
            if (StringUtil.isEmpty(javeTempDir)) {
                javeTempDir = "/opt/ffmpeg-temp";
            }

            Path tempDir = Paths.get(javeTempDir);
            Files.createDirectories(tempDir);

            // 设置系统属性
            System.setProperty("java.io.tmpdir", javeTempDir);
            System.setProperty("jave.tmpdir", javeTempDir);

            log.debug("JAVE临时目录设置为: {}", javeTempDir);

        } catch (Exception e) {
            log.warn("设置JAVE临时目录失败: {}", e.getMessage());
        }
    }

    /**
     * 修复ffmpeg权限问题
     */
    private static void fixFfmpegPermissions() {
        try {
            // 检查常见的ffmpeg位置并修复权限
            String[] possiblePaths = {
                "/tmp/jave",
                "/opt/ffmpeg-temp",
                System.getProperty("java.io.tmpdir", "/tmp") + "/jave"
            };

            for (String path : possiblePaths) {
                Path dir = Paths.get(path);
                if (Files.exists(dir)) {
                    Files.walk(dir)
                        .filter(p -> p.getFileName().toString().startsWith("ffmpeg"))
                        .forEach(AudioHelper::makeExecutable);
                }
            }

        } catch (Exception e) {
            log.debug("修复ffmpeg权限时出现异常: {}", e.getMessage());
        }
    }

    /**
     * 使文件可执行
     */
    private static void makeExecutable(Path filePath) {
        try {
            if (Files.exists(filePath) && !Files.isExecutable(filePath)) {
                Set<PosixFilePermission> permissions = Files.getPosixFilePermissions(filePath);
                permissions.add(PosixFilePermission.OWNER_EXECUTE);
                permissions.add(PosixFilePermission.GROUP_EXECUTE);
                Files.setPosixFilePermissions(filePath, permissions);
                log.debug("设置文件可执行权限: {}", filePath);
            }
        } catch (Exception e) {
            log.debug("设置文件权限失败: {} - {}", filePath, e.getMessage());
        }
    }

    public static void deleteAudioTempFile(String fileName) {
        Path tempDir = Paths.get("/data/audioTempFile/");
	    try {
            boolean delete = Files.deleteIfExists(tempDir.resolve(fileName));
            if (delete) {
                log.info("Deleted audio temp file: {}", fileName);
            } else {
                log.warn("Audio temp file not found: {}", fileName);
            }
        } catch (IOException e) {
            log.error("Error deleting audio temp file: {}", e.getMessage());
	    }
    }
    
    

    private static String getExtension(String filename) {
        return filename.substring(filename.lastIndexOf(".") + 1);
    }
}
