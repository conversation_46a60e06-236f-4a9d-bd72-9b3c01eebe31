package com.welab.databridge.oss;

import cn.hutool.core.lang.UUID;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.OSS;
import com.aliyun.oss.model.ListObjectsRequest;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.ObjectListing;
import com.welab.databridge.util.DateUtil;
import com.welab.databridge.util.StringUtil;
import com.welab.databridge.vo.audio.OssMeta;
import com.welab.document.enums.OssStorageClassEnum;
import com.welab.document.interfaces.dto.UploadOssDTO;
import com.welab.document.interfaces.facade.OssClientServiceFacade;
import com.welab.document.util.FileUploader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.InputStream;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * oss上传管理Service实现类
 */
@Service
public class OssService {
    private static final Logger log = LoggerFactory.getLogger(OssService.class);
    @Reference
    private OssClientServiceFacade ossClientServiceFacade;

    @Value("${aliyun.oss.bucketName}")
    private String ALIYUN_OSS_BUCKET_NAME;
    @Value("${aliyun.oss.dir.prefix}")
    private String ALIYUN_OSS_DIR_PREFIX;
    @Value("${appId}")
    private String appId;
    @Value("${bucketName}")
    private String bucketName;

    @Resource
    private OSS ossClient;

    public void listFile(String prefix, List<OssMeta> files) {
        ListObjectsRequest listObjectsRequest = new ListObjectsRequest(bucketName);
        listObjectsRequest.setPrefix(prefix);
        listObjectsRequest.setMaxKeys(1000);
        // 遍历所有文件。
        // objectSummaries的列表中给出的是fun目录下的文件。
        String nextMarker = "xx";
        int i = 1;
        while (StringUtil.isNotEmpty(nextMarker)) {
            log.info(" get oss file:" + i);
            ObjectListing listing = ossClient.listObjects(listObjectsRequest);
            files.addAll(listing.getObjectSummaries().stream().map(v -> {
                OssMeta ossMeta = new OssMeta();
                ossMeta.setKey(v.getKey());
                ossMeta.setUpdateTime(DateUtil.format(v.getLastModified(), DateUtil.FORMAT_FULL_DATE));
                return ossMeta;
            }).collect(Collectors.toList()));
            nextMarker = listing.getNextMarker();
            listObjectsRequest.setMarker(nextMarker);
            listObjectsRequest.setPrefix(prefix);
            listObjectsRequest.setMaxKeys(1000);
            i++;
        }
        log.info("total file:" + files.size());
    }

    public void listZip(String prefix, List<OssMeta> files) {
        ListObjectsRequest listObjectsRequest = new ListObjectsRequest(bucketName);
        listObjectsRequest.setPrefix(prefix);
        listObjectsRequest.setMaxKeys(1000);
        // 遍历所有文件。
        // objectSummaries的列表中给出的是fun目录下的文件。
        String nextMarker = "xx";
        int i = 1;
        while (StringUtil.isNotEmpty(nextMarker)) {
            log.info(" get oss file:" + i);
            ObjectListing listing = ossClient.listObjects(listObjectsRequest);
            files.addAll(listing.getObjectSummaries().stream()
                                .filter(v -> v.getKey().endsWith(".zip")
                                            || v.getKey().endsWith(".rar")
                                            || v.getKey().endsWith(".tgz"))
                                .map(v -> {
                                    OssMeta ossMeta = new OssMeta();
                                    ossMeta.setKey(v.getKey());
                                    ossMeta.setUpdateTime(DateUtil.format(v.getLastModified(), DateUtil.FORMAT_FULL_DATE));
                                    return ossMeta;
                                }).collect(Collectors.toList()));
            nextMarker = listing.getNextMarker();
            listObjectsRequest.setMarker(nextMarker);
            listObjectsRequest.setPrefix(prefix);
            listObjectsRequest.setMaxKeys(1000);
            i++;
        }
        log.info("total zip:" + files.size());
    }

    public String getUpdateTime(String file) {
        OSSObject ossObject = ossClient.getObject(bucketName, file);
        return DateUtil.format(ossObject.getObjectMetadata().getLastModified(), DateUtil.FORMAT_FULL_DATE);
    }

    public OSSObject getFile(String path) {
        OSSObject ossObject = ossClient.getObject(bucketName, path);
        return ossObject;
    }

    /**
     * 列出目标目录：commonPrefix下的所有文件夹，并添加到结果集res中
     *
     * @param commonPrefix 目标目录
     * @param res 结果集
     */
    public void listDir(String commonPrefix, List<String> res) {
        //跳出循环后设置为false，即不是最深层，不添加
        boolean flag = true;
        // 构造ListObjectsRequest请求。
        ListObjectsRequest listObjectsRequest = new ListObjectsRequest(bucketName);

        // 设置正斜线（/）为文件夹的分隔符。
        listObjectsRequest.setDelimiter("/");

        // 列出主目录下的所有文件夹。
        listObjectsRequest.setPrefix(commonPrefix);
        ObjectListing listing = ossClient.listObjects(listObjectsRequest);
        int len = listing.getCommonPrefixes().size();
        if (len > 0){ //不是最深层则子目录个数必然大于0，继续向下遍历，并将flag设置为FALSE
            for (String prefix : listing.getCommonPrefixes()){
                System.out.println(prefix);
                listDir(prefix, res);
            }
            flag = false;
        }
        if (flag){
            res.add(commonPrefix);
        }
    }

/** 直接使用ossClient实现的上传、存储为归档模式及解冻，后改为通过消金文档服务实现 */
//    public String saveByteArray(String kind, String parentId, String fileName, InputStream inputStream) {
//        StringBuffer path = new StringBuffer(ALIYUN_OSS_DIR_PREFIX);
//        path.append(kind).append("/").append(parentId).append("/");
//        path.append(UUID.fastUUID().toString().replaceAll("-", ""));
//        path.append("/").append(fileName);
////        ObjectMetadata objectMetadata = new ObjectMetadata();
////        objectMetadata.setHeader("x-oss-storage-class", StorageClass.Archive);
////        ossClient.putObject(ALIYUN_OSS_BUCKET_NAME, path.toString(), inputStream, objectMetadata);
//        ossClient.putObject(ALIYUN_OSS_BUCKET_NAME, path.toString(), inputStream);
//        return path.toString();
//    }

//    public String getDocumentUrl(String path) {
//        Date expiration = new Date(System.currentTimeMillis() + 3600 * 1000 * 2);
//        return getDocumentUrl(path, expiration);
//    }
//
//    public String getDocumentUrl(String path, Date expiration) {
//        return ossClient.generatePresignedUrl(ALIYUN_OSS_BUCKET_NAME, path, expiration).toString();
//    }

//    public OSSObject getFile(String path) {
//        OSSObject ossObject = ossClient.getObject(ALIYUN_OSS_BUCKET_NAME, path);
//        return ossObject;
//    }

//    public boolean isArchiveObject(String ossKey) {
//        ObjectMetadata objectMetadata = ossClient.getObjectMetadata(ALIYUN_OSS_BUCKET_NAME, ossKey);
//
//        // 校验Object是否为归档类型Object。
//        StorageClass storageClass = objectMetadata.getObjectStorageClass();
//        if (storageClass == StorageClass.Archive) {
//            return true;
//        }
//        return false;
//    }

//    public void restoreObject(String ossKey) {
//        try {
//            ObjectMetadata objectMetadata = ossClient.getObjectMetadata(ALIYUN_OSS_BUCKET_NAME, ossKey);
//
//            // 校验Object是否为归档类型Object。
//            StorageClass storageClass = objectMetadata.getObjectStorageClass();
//            if (storageClass == StorageClass.Archive) {
//                // 解冻Object。
//                ossClient.restoreObject(ALIYUN_OSS_BUCKET_NAME, ossKey);
//                log.info("restoreObject: " + ossKey + " success");
//            }
//        } catch (Exception e) {
//            log.error("restoreObject: " + ossKey + " failed, ex: " + e.getMessage());
//        }
//    }

//    public void waitRestoreCompleted(String ossKey) {
//        ObjectMetadata objectMetadata = ossClient.getObjectMetadata(ALIYUN_OSS_BUCKET_NAME, ossKey);
//
//        // 等待解冻完成。
//        while (!objectMetadata.isRestoreCompleted()) {
//            try {
//                Thread.sleep(1000);
//            } catch (InterruptedException e) {
//                e.printStackTrace();
//            }
//            objectMetadata = ossClient.getObjectMetadata(ALIYUN_OSS_BUCKET_NAME, ossKey);
//        }
//    }
    /** 直接使用ossClient实现的上传、存储为归档模式及解冻，后改为通过消金文档服务实现 */

    /**
     * 上传文件
     *
     * @param kind
     * @param parentId
     * @param fileName
     * @param inputStream
     * @param isArchive  是否存储为归档模式
     * @return
     * @throws Exception
     */
    public String uploadByDocServer(String kind, String parentId, String fileName, InputStream inputStream, boolean isArchive) throws Exception {
        StringBuffer ossKey = new StringBuffer(ALIYUN_OSS_DIR_PREFIX);
        ossKey.append(kind).append("/").append(parentId).append("/");
        ossKey.append(UUID.fastUUID().toString().replaceAll("-", ""));
        ossKey.append("/").append(fileName);
        Map uploadParams = new HashMap();
        uploadParams.put("path", ossKey.toString());
        uploadParams.put("addPrefix", "no");
        uploadParams.put("bucketName", ALIYUN_OSS_BUCKET_NAME);
        if (isArchive) {
            uploadParams.put("storageClass", OssStorageClassEnum.ARCHIVE.getValue());
        } else {
            uploadParams.put("storageClass", OssStorageClassEnum.STANDARD.getValue());
        }
        uploadParams.put("applicationName", appId);
        UploadOssDTO uploadOssDTO = ossClientServiceFacade.getUploadOssParams(uploadParams);
        uploadOssDTO.setHost(ossClientServiceFacade.getInnerUrl(uploadOssDTO.getHost()));
        String result = FileUploader.fileUpload(uploadOssDTO, inputStream);
        JSONObject obj = JSON.parseObject(result);
        if (!"200".equals(obj.getString("status"))) {
            log.error("上传失败, ex: " + obj.getString("message"));
            throw new RuntimeException("上传失败, ex: " + obj.getString("message"));
        }
        log.info("上传成功");
        return ossKey.toString();
    }

    /**
     * 解冻归档模式的文件
     *
     * @param ossKey
     */
    public void restoreByDocServer(String ossKey) {
        try {
            log.info("restoreObject: " + ossKey + " start");
            long start = System.currentTimeMillis();
            ossClientServiceFacade.restoreObject(ALIYUN_OSS_BUCKET_NAME, ossKey);
            do {
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    log.error("restoreObject error: " + e.getMessage());
                    Thread.currentThread().interrupt();
                }
            } while (!ossClientServiceFacade.isRestoreCompleted(ALIYUN_OSS_BUCKET_NAME, ossKey));
            log.info("restoreObject: " + ossKey + " success, used: " + (System.currentTimeMillis() - start));
        } catch (Exception e) {
            log.error("restoreObject: " + ossKey + " failed, ex: " + e);
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 归档模式文件是否解冻完成
     *
     * @param ossKey
     * @return
     */
    public boolean isRestoreCompleted(String ossKey) {
        return ossClientServiceFacade.isRestoreCompleted(ALIYUN_OSS_BUCKET_NAME, ossKey);
    }

    /**
     * 获取文件临时访问地址
     *
     * @param ossKey
     * @param expiration
     * @return
     */
    public String getOssUrlByDocServer(String ossKey, long expiration) {
        return ossClientServiceFacade.getOssUrl(ALIYUN_OSS_BUCKET_NAME, ossKey, expiration);
    }

    public static void main(String[] args) {
        Date expiration = new Date(System.currentTimeMillis() + 3600 * 1000 * 2);
        log.info("" + expiration);
    }
}
