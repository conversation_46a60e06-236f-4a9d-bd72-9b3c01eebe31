package com.welab.databridge.health;

import com.sun.net.httpserver.HttpExchange;
import com.sun.net.httpserver.HttpHandler;
import com.sun.net.httpserver.HttpServer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.URI;

@SuppressWarnings("restriction")
@Component
public class ActuateServer {
    private static Logger log = LoggerFactory.getLogger(ActuateServer.class);

    public static final String METHOD_HEADER = "HEAD";
    @Value("${health.port:9090}")
    private Integer healthPort;
    @Value("${health.context-path:/manage}")
    private String healthContextPath;

    @PostConstruct
    public void init() throws IOException {
        // 创建 http服务器, 绑定本地端口
        HttpServer httpServer = HttpServer.create(new InetSocketAddress(healthPort), 10);
        final ActuateHandler handler = new ActuateHandler();
        log.info("开始启动健康检查服务，端口为{}，context-path为{}", healthPort,healthContextPath);
        // 创建上下文监听, "/" 表示匹配所有 URI 请求
        httpServer.createContext("/", new HttpHandler() {
            public void handle(HttpExchange httpExchange) throws IOException {
                String method = httpExchange.getRequestMethod();
                if (METHOD_HEADER.equalsIgnoreCase(method)) {
                    httpExchange.sendResponseHeaders(200, 0);
                } else {
                    URI uri = httpExchange.getRequestURI();
                    // 响应内容
                    byte[] respContents = handler.handlerMapping(uri, healthContextPath);
                    // 设置响应头
                    httpExchange.getResponseHeaders().add("Content-Type", "application/json;charset=UTF-8");
                    httpExchange.sendResponseHeaders(200, respContents.length);
                    httpExchange.getResponseBody().write(respContents);
                }
                httpExchange.close();
            }
        });
        // 启动服务
        httpServer.start();
        log.info("启动健康检查服务成功，端口为{}，context-path为{}", healthPort,healthContextPath);
    }
}
