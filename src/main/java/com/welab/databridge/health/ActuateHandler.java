package com.welab.databridge.health;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URI;

public class ActuateHandler {
    private static Logger log = LoggerFactory.getLogger(ActuateHandler.class);

    private JSONObject health = new JSONObject();
    private JSONObject info = new JSONObject();
    private JSONObject status = new JSONObject();
    private Class<?> routerCls = null;
    private String mapName;

    public void init() {
        buildHealth();
        buildInfo();
        buildStatus();
    }

    public byte[] handlerMapping(URI uri, String managementContext) {
        Object res = null;
        String path = uri.getPath();
        log.debug("收到监控检查服务请求，path={}", path);
        if (path.equals(managementContext + "/health")) {
            if (health == null || health.isEmpty()) {
                buildHealth();
            }
            res = health;
        } else if (path.equals(managementContext + "/status")) {
            if (status == null || status.isEmpty()) {
                buildStatus();
            }
            res = status;
        } else if (path.equals(managementContext + "/info")) {
            if (info == null || info.isEmpty()) {
                buildInfo();
            }
            log.info(info.toString());
            res = info;
        } else {
            throw new RuntimeException("not found");
        }
        return JSON.toJSONBytes(res);
    }

    private void buildHealth() {
        health.put("status", "UP");
    }

    private void buildInfo() {
        info = MappingIndicator.getInfo(routerCls, mapName);
    }

    private void buildStatus() {
        status.put("status", "UP");
        JSONObject diskSpace = SystemIndicator.getDiskInfo();
        if (diskSpace != null && !diskSpace.isEmpty()) {
            diskSpace.remove("percent");
            diskSpace.remove("detail");
            if (!diskSpace.isEmpty()) {
                status.put("diskSpace", diskSpace);
            }
        }
    }

    public Class<?> getRouterCls() {
        return routerCls;
    }

    public void setRouterCls(Class<?> routerCls) {
        this.routerCls = routerCls;
    }

    public String getMapName() {
        return mapName;
    }

    public void setMapName(String mapName) {
        this.mapName = mapName;
    }
}
