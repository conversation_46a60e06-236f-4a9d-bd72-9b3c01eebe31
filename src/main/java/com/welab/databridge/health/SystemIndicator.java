package com.welab.databridge.health;

import com.alibaba.fastjson.JSONObject;
import com.sun.management.OperatingSystemMXBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.lang.management.ManagementFactory;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;

@SuppressWarnings("restriction")
public class SystemIndicator {
	private static Logger log = LoggerFactory.getLogger(SystemIndicator.class);
	
	private static DecimalFormat DECIMALFORMAT = new DecimalFormat("#.##");

	public static JSONObject getDiskInfo(){
		JSONObject diskInfo = new JSONObject();
		List<JSONObject> list = new ArrayList<JSONObject>();
		long freeSpace = 0;
		long totalSpace = 0;
		long usableSpace = 0;
		// 1.获得所有分区--------静态方法listRoots()
		File[] roots = File.listRoots();
		for (File file : roots) {
			// 2.用isDirectory()判断是否为分区（有可能是设备文件）
			if (file.isDirectory()) {
				JSONObject subDisk = new JSONObject();
				// 3.用getTotalSpace()获得分区的总空间（单位：字节）
				subDisk.put("totalSpace", file.getTotalSpace() / (1024 * 1024 * 1024));
				totalSpace = totalSpace + subDisk.getLongValue("totalSpace");
				// 4.用getFreeSpace()获得分区的剩余空间（单位：字节）
				subDisk.put("freeSpace", file.getFreeSpace() / (1024 * 1024 * 1024));
				freeSpace = freeSpace + subDisk.getLongValue("freeSpace");
				// 5.用getUsableSpace()获得分区的已用空间（单位：字节）
				//subDisk.put("usableSpace", file.getUsableSpace() / (1024 * 1024 * 1024));
				subDisk.put("usableSpace", subDisk.getLongValue("totalSpace") - subDisk.getLongValue("freeSpace"));
				usableSpace = usableSpace + subDisk.getLongValue("usableSpace");
				// 6.计算使用百分比
				subDisk.put("percent", DECIMALFORMAT.format((subDisk.getDoubleValue("usableSpace")/subDisk.getDoubleValue("totalSpace"))*100)+"%");

				log.info(file.getAbsolutePath() + "\t总空间：" + subDisk.getLongValue("totalSpace") + "GB\t"
						+ "剩余空间：" + subDisk.getLongValue("freeSpace") + "GB\t"
								+ "已用空间：" + subDisk.getLongValue("usableSpace") + "GB");
				list.add(subDisk);
			}
		}
		
		diskInfo.put("status", "UP");
		diskInfo.put("total", totalSpace);
		diskInfo.put("free", freeSpace);
		diskInfo.put("threshold", usableSpace);
		double percent = 0d;
		if (totalSpace != 0d) {
			percent = ((double)usableSpace/(double)totalSpace);
		}
		diskInfo.put("percent", DECIMALFORMAT.format(percent*100)+"%");
		diskInfo.put("detail", list);
		log.info("总空间：" + totalSpace + "GB\t剩余空间：" + freeSpace + "GB\t已用空间：" + usableSpace + "GB");
		
		return diskInfo;
	}
	
	public static JSONObject getMemeryInfo(){
		JSONObject memeryInfo = new JSONObject();
		OperatingSystemMXBean osmxb = (OperatingSystemMXBean) ManagementFactory.getOperatingSystemMXBean();
		
		// 总的物理内存+虚拟内存
		//double totalvirtualMemory = osmxb.getTotalSwapSpaceSize() / (1024.0 * 1024.0 * 1024.0);
		// 物理内存（内存条）
		double  physicalMemorySize = osmxb.getTotalPhysicalMemorySize() / (1024.0 * 1024.0 * 1024.0);
		// 剩余的物理内存
		double  freePhysicalMemorySize = osmxb.getFreePhysicalMemorySize() / (1024.0 * 1024.0 * 1024.0);
		// 使用的物理内存
		double  usableMemorySize = physicalMemorySize - freePhysicalMemorySize;
		
		memeryInfo.put("total", DECIMALFORMAT.format(physicalMemorySize));
		memeryInfo.put("free", DECIMALFORMAT.format(freePhysicalMemorySize));
		memeryInfo.put("threshold", DECIMALFORMAT.format(usableMemorySize));
		memeryInfo.put("percent", DECIMALFORMAT.format(((double)usableMemorySize/(double)physicalMemorySize)*100)+"%");
		
		log.info("物理内存：" + physicalMemorySize + "GB\t剩余内存：" + freePhysicalMemorySize + "GB\t已用内存：" + usableMemorySize + "GB");
		
		return memeryInfo;
	}
}
