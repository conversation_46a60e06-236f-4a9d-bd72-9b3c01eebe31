package com.welab.databridge.health;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import java.lang.reflect.Field;
import java.net.InetAddress;
import java.util.*;

@Slf4j
public class MappingIndicator {
	
	public static JSONObject getInfo(Class<?> routerCls, String mapName){
		if (routerCls == null){
			return initSpringMapping();
		}
		
		return initNettyMapping(routerCls, mapName);
	}
	
    @SuppressWarnings("unchecked")
	public static JSONObject initNettyMapping(Class<?> routerCls, String mapName){
    	JSONObject info = new JSONObject();
    	String fieldName = "handlers";
    	if (StringUtil.isNotEmpty(mapName)){
    		fieldName = mapName;
    	}
    	
		try {
			Field mapHandler = routerCls.getDeclaredField(fieldName);

	    	mapHandler.setAccessible(true);
	    	Map<String, Object> handlers = (Map<String, Object>)mapHandler.get(routerCls.newInstance());
	    	if (!StringUtil.isEmpty(handlers)){
	    		List<JSONObject> urlLst = new ArrayList<JSONObject>();
	        	Iterator<String> it = handlers.keySet().iterator();
	        	String url = "";
	        	if (it.hasNext()){
	        		url = it.next();
	        		if (!url.endsWith("/manage/info") && !url.endsWith("/manage/health")){
	        			JSONObject urlJson = new JSONObject();
	        			urlJson.put("url",getPath(url));
	        			urlLst.add(urlJson);
	        		}
	        		String appId = getAppId(url);
	        		info.put("appId", appId);
	        		info.put("context", "/"+appId);
	        	}
	        	while(it.hasNext()){
	        		url = it.next();
	        		if (!url.endsWith("/manage/info") && !url.endsWith("/manage/health")){
	        			JSONObject urlJson = new JSONObject();
	        			urlJson.put("url",getPath(url));
	        			urlLst.add(urlJson);
	        		}
	        	}
	        	info.put("mappings", urlLst);
	    	}
		} catch (Exception e) {
			log.error("initNettyMapping error: {}", e.getMessage());
		}
    	
		String host = null;
		try {
			host = InetAddress.getLocalHost().getHostAddress();
		} catch (Exception e) {
			host = "Unknown";
		}
		info.put("status", "UP");
		info.put("host", host);
		
    	return info;
    }
    
    public static JSONObject initSpringMapping(){
    	JSONObject info = new JSONObject();
		String host = null;
		try {
			host = InetAddress.getLocalHost().getHostAddress();
		} catch (Exception e) {
			host = "Unknown";
		}
		info.put("status", "UP");
		info.put("host", host);
		info.put("appId", System.getProperty("appId"));
		info.put("context", "");
		info.put("mappings", getSpringMappingList());
		
    	return info;
    }
    
    private static List<JSONObject> getSpringMappingList(){
    	List<JSONObject> urls = new ArrayList<JSONObject>();
    	RequestMappingHandlerMapping mapping = SpringUtil.getApplicationContext().getBean(RequestMappingHandlerMapping.class);
    	// 获取url与类和方法的对应信息
    	Map<RequestMappingInfo, HandlerMethod> map = mapping.getHandlerMethods();
    	
    	for (RequestMappingInfo info : map.keySet()) {
    		// 获取url的Set集合，一个方法可能对应多个url
    		Set<String> patterns = info.getPatternsCondition().getPatterns();
    		for (String url : patterns) {
    			JSONObject urlJson = new JSONObject();
    			urlJson.put("url", url);
    			urls.add(urlJson);
    		}
    	}
    	return urls;
    }
    
    private static String getPath(String url){
    	String path = url;
		// 如果是以/开头的
		if (path.charAt(0) == '/') {
			// 获取后面的字符串
			path = path.substring(1);
		}
		int index = path.indexOf("/");
		return path.substring(index);
    }
    
    private static String getAppId(String url){
    	if (StringUtil.isEmpty(url)){
    		return "";
    	}
    	String[] uary = url.split("/");
    	if (uary.length > 1){
    		if (!StringUtil.isEmpty(uary[0])){
    			return uary[0];
    		}else{
    			return uary[1];
    		}
    	}
    	return "";
    }
}
