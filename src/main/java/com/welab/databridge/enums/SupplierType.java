package com.welab.databridge.enums;

public enum SupplierType {
    ST_CTI("TR", "天润"),
    ST_94AI("JS", "94Ai"),
    ST_LB("LB", "灵伴")
    ;

    SupplierType(String key, String value) {
        this.key = key;
        this.value = value;
    }

    private String key;
    private String value;

    public String getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static String getValueByKey(String key) {
        for (SupplierType supplierType : SupplierType.values()) {
            if (supplierType.getKey().equals(key)) {
                return supplierType.getValue();
            }
        }
        return key;
    }
}
