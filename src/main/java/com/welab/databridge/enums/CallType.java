package com.welab.databridge.enums;

public enum CallType {
    CALL(0, "呼出"),
    LISTEN(1, "来电"),
    LISTEN2(1, "呼入"),
    WEB_CALL(3, "webcall"),;

    CallType(int value, String remark) {
        this.value = value;
        this.remark = remark;
    }

    private int value;
    private String remark;

    public int getValue() {
        return value;
    }

    public String getRemark() {
        return remark;
    }

    public static Integer getValueByRemark(String remark) {
        for (CallType callType : CallType.values()) {
            if (callType.getRemark().equals(remark)) {
                return callType.getValue();
            }
        }
        return null;
    }
}
