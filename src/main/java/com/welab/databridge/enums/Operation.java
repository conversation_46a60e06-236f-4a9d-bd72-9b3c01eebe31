package com.welab.databridge.enums;

/**
 * 操作类型
 */
public enum Operation {

    DEFAULT("default", ""),

    ADD("add", "创建"),

    MODIFY("modify", "修改"),

    DELETE("delete", "删除"),

    ONLINE("online", "上架"),

    OFFLINE("offline", "下架"),

    DOWNLOAD("download", "下载"),

    ONLINE_APPLY("online_apply", "提交上架审核"),

    OFFLINE_APPLY("offline_apply", "提交下架审核"),

    DELETE_APPLY("delete_apply", "提交删除审核"),

    DOWNLOAD_APPLY("download_apply", "提交下载审核"),

    CANCEL("cancel", "撤回"),

    SUSPEND("suspend", "暂停");

    Operation(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    private String type;
    private String desc;

    public String getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDescByType(String type) {
        for (Operation operation : values()) {
            if (operation.getType().equals(type)) {
                return operation.getDesc();
            }
        }
        return type;
    }
}
