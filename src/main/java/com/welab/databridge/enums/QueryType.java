package com.welab.databridge.enums;

public enum QueryType {
    QUERY_APPROVAL("approval", "查询审批"),
    QUERY_FRAUD("fraud", "查询反欺诈"),
    QUERY_CS("cs", "查询客服"),
    QUERY_COLLECTION("collection", "查询催收"),
    QUERY_CTI("cti", "查询天润"),
    QUERY_94AI("94Ai", "查询94"),
    FIX_94AI("fix94Ai", "修复94"),
    QUERY_LB("lb", "查询灵伴"),
    PROCESS_DATA("processData", "处理数据"),
    PROCESS_CTI_DATA("processCtiData", "处理天润数据"),
    PULL_CTI_DATA("pullCtiData", "拉取天润数据"),
    PROCESS_EXISTS_AMR("processExistsAmr", "处理存量Amr文件"),
    PROCESS_NULL_FILE("processNullFile", "处理空文件"),
    DELETE_DATA("delete", "删除数据"),
    LIST_DIR("listDir", "列出文件夹"),
    LIST_FILE("listFile", "列出文件"),
    SYNC_FILE("syncFile", "同步文件"),
    PROCESS_ZIP("processZip", "处理zip文件"),
    PULL_LB_DAY_DATA("pullLBDayData", "拉取灵伴单日数据"),
    PROCESS_LB_DAY_DATA("processLBDayData", "处理灵伴单日数据"),
    PROCESS_LB_DATA("processLBData", "处理灵伴数据"),
    ;

    QueryType(String key, String value) {
        this.key = key;
        this.value = value;
    }

    private String key;
    private String value;

    public String getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }
}
