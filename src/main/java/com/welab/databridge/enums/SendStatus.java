package com.welab.databridge.enums;

public enum SendStatus {
    SUCCESS(0,"发送成功"),
    FAIL(1, "发送失败"),
    UNKNOWN(2, "未知");

    SendStatus(int value, String remark) {
        this.value = value;
        this.remark = remark;
    }

    private int value;
    private String remark;

    public int getValue() {
        return value;
    }

    public String getRemark() {
        return remark;
    }

    public static Integer getValueByRemark(String remark) {
        for (SendStatus sendStatus : SendStatus.values()) {
            if (sendStatus.getRemark().equals(remark)) {
                return sendStatus.getValue();
            }
        }
        return null;
    }

    public static String getRemarkByValue(int value) {
        for (SendStatus sendStatus : SendStatus.values()) {
            if (sendStatus.getValue() == value) {
                return sendStatus.getRemark();
            }
        }
        return null;
    }
}
