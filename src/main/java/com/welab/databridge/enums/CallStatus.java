package com.welab.databridge.enums;

public enum CallStatus {
    SUCCESS(0, "接听成功"),
    FAIL(1, "接听失败");

    CallStatus(int value, String remark) {
        this.value = value;
        this.remark = remark;
    }

    private int value;
    private String remark;

    public int getValue() {
        return value;
    }

    public String getRemark() {
        return remark;
    }

    public static Integer getValueByRemark(String remark) {
        for (CallStatus callStatus : CallStatus.values()) {
            if (callStatus.getRemark().equals(remark)) {
                return callStatus.getValue();
            }
        }
        return null;
    }
}
