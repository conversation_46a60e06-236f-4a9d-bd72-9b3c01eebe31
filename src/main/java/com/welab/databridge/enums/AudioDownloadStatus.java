package com.welab.databridge.enums;

public enum AudioDownloadStatus {
    DEFAULT("default", "初始状态"),
    PROCESSING("processing", "正在生成下载链接"),
    FINISH("finish", "下载链接已邮件发送"),
    FAIL("fail", "下载失败");

    AudioDownloadStatus(String status, String remark) {
        this.status = status;
        this.remark = remark;
    }

    private String status;
    private String remark;

    public String getStatus() {
        return status;
    }

    public String getRemark() {
        return remark;
    }
}
