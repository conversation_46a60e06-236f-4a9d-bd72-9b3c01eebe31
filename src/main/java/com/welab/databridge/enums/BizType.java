package com.welab.databridge.enums;

public enum BizType {
    AUDIO_FAIL("audioFail", "语音上传失败"),
    AUDIO_RECORD("audioRecord", "语音记录"),
    AUDIO_UPLOAD("audioUpload", "语音上传"),
    MESSAGE_UPLOAD("messageUpload", "短信上传"),
    MESSAGE_FAIL("messageFail", "短信上传失败"),
    MESSAGE_RECORD("messageRecord", "短信记录"),
    DEPARTMENT("department", "部门"),
    ;

    BizType(String key, String value) {
        this.key = key;
        this.value = value;
    }

    private String key;
    private String value;

    public String getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }
}
