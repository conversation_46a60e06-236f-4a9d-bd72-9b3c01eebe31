package com.welab.databridge.enums;

public enum DeptEnum {
    <PERSON><PERSON><PERSON>(45, "金禹"),
        <PERSON><PERSON><PERSON><PERSON><PERSON>(46, "鹏天华晟"),
        <PERSON><PERSON><PERSON>X<PERSON>(47, "爱仕兴"),
        <PERSON><PERSON><PERSON>(48, "泰然"),
        <PERSON><PERSON><PERSON><PERSON>(49, "鑫浩"),
        <PERSON><PERSON><PERSON><PERSON>(50, "浩洋"),
        <PERSON><PERSON><PERSON><PERSON>(51, "吴卓"),
        <PERSON><PERSON><PERSON><PERSON><PERSON>(52, "瑞念信"),
        <PERSON><PERSON><PERSON>(53, "凯信"),
        <PERSON><PERSON><PERSON>(54, "勤为"),
        <PERSON><PERSON><PERSON><PERSON>(55, "深弘君"),
        <PERSON><PERSON><PERSON>(56, "永辉"),
        <PERSON><PERSON><PERSON><PERSON>(57, "融臻"),
        <PERSON><PERSON><PERSON>(58, "骅富"),
        <PERSON><PERSON><PERSON><PERSON>(59, "鑫泽"),
        <PERSON><PERSON><PERSON>(60, "大地"),
        <PERSON><PERSON><PERSON>(61, "利银"),
        <PERSON><PERSON><PERSON>(62, "鸿会"),
        <PERSON><PERSON><PERSON>(63, "雁扬"),
        <PERSON><PERSON><PERSON><PERSON><PERSON>(64, "宏信达"),
        <PERSON><PERSON><PERSON><PERSON>(65, "中美"),
        <PERSON><PERSON><PERSON><PERSON>heng(66, "鸿越盛"),
        <PERSON><PERSON><PERSON><PERSON><PERSON>(67, "宏鑫通"),
        <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(68, "资产雷达"),
        <PERSON><PERSON><PERSON><PERSON>(69, "融缘"),
        <PERSON><PERSON><PERSON>(70, "维度"),
        <PERSON><PERSON><PERSON><PERSON>(71, "智富"),
        Z<PERSON>XR(78, "中和兴融"),

    ;

    private int id;
    private String name;

    DeptEnum(int id, String name) {
        this.id = id;
        this.name = name;
    }

    public int getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public static int getIdByName(String name) {
        for (DeptEnum dept : DeptEnum.values()) {
            if (name.contains(dept.getName())) {
                return dept.getId();
            }
        }
        return -1;
    }
}
