package com.welab.databridge.enums;

public enum ListenStatus {
    SUCCESS(0,"接听成功"),
    FAIL(1, "接听失败");

    ListenStatus(int value, String remark) {
        this.value = value;
        this.remark = remark;
    }

    private int value;
    private String remark;

    public int getValue() {
        return value;
    }

    public String getRemark() {
        return remark;
    }

    public static Integer getValueByRemark(String remark) {
        for (ListenStatus sendStatus : ListenStatus.values()) {
            if (sendStatus.getRemark().equals(remark)) {
                return sendStatus.getValue();
            }
        }
        return null;
    }

    public static String getRemarkByValue(int value) {
        for (ListenStatus sendStatus : ListenStatus.values()) {
            if (sendStatus.getValue() == value) {
                return sendStatus.getRemark();
            }
        }
        return null;
    }
}
