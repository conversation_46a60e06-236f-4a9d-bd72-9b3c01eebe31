package com.welab.databridge.enums;

/**
 * 回听状态
 */
public enum ListenedRemark {
    UN_LISTEN(0,"未听"),
    LISTENED(1, "已听");

    ListenedRemark(int value, String remark) {
        this.value = value;
        this.remark = remark;
    }

    private int value;
    private String remark;

    public int getValue() {
        return value;
    }

    public String getRemark() {
        return remark;
    }

    public static Integer getValueByRemark(String remark) {
        for (ListenedRemark sendStatus : ListenedRemark.values()) {
            if (sendStatus.getRemark().equals(remark)) {
                return sendStatus.getValue();
            }
        }
        return null;
    }

    public static String getRemarkByValue(int value) {
        for (ListenedRemark sendStatus : ListenedRemark.values()) {
            if (sendStatus.getValue() == value) {
                return sendStatus.getRemark();
            }
        }
        return null;
    }
}
