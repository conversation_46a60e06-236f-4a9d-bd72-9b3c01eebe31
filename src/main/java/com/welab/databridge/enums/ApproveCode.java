package com.welab.databridge.enums;

import com.welab.databridge.util.StringUtil;
import lombok.Getter;

/**
 * 审核配置Code
 */
@Getter
public enum ApproveCode {
    DEFAULT("default", "default", "初始审批类型", ""),
    MESSAGE_UPLOAD_DELETE("databridge-web-message_upload_delete", "delete", "删除短信上传记录", "短信上传"),
    AUDIO_UPLOAD_DELETE("databridge-web-audio_upload_delete", "delete", "删除音频上传记录", "音频上传"),
    AUDIO_RECORD_DOWNLOAD("databridge-web-audio_record_download", "download", "下载音频记录", "语音查询"),
    ;

    ApproveCode(String code, String type, String desc, String module) {
        this.approveCode = code;
        this.approveType = type;
        this.desc = desc;
        this.module = module;
    }

    public static ApproveCode getByCode(String code) {
        if (StringUtil.isEmpty(code)) {
            return null;
        }
        for (ApproveCode approveCode : values()) {
            if (approveCode.getApproveCode().equals(code)) {
                return approveCode;
            }
        }
        return null;
    }

    public static ApproveCode getByType(String type) {
        if (StringUtil.isEmpty(type)) {
            return null;
        }
        for (ApproveCode approveCode : values()) {
            if (approveCode.getApproveType().equals(type)) {
                return approveCode;
            }
        }
        return null;
    }

    public static String getDescByCode(String code) {
        for (ApproveCode approveCode : values()) {
            if (approveCode.getApproveCode().equals(code)) {
                return approveCode.getDesc();
            }
        }
        return "";
    }

    public static String getModuleByCode(String code) {
        for (ApproveCode approveCode : values()) {
            if (approveCode.getApproveCode().equals(code)) {
                return approveCode.getModule();
            }
        }
        return "";
    }

    private String approveCode;
    private String approveType;
    private String desc;
    private String module;
}
