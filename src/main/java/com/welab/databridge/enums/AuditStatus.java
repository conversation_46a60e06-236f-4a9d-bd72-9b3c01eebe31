package com.welab.databridge.enums;

/**
 * 审核状态
 */
public enum AuditStatus {
    DEFAULT("default", "init", "初始状态"),
    IN_REVIEW("in_review", "submit", "审核中"),
    APPROVED("approved", "approved", "审核通过"),
    REJECT("reject", "reject", "审核拒绝"),
    REVOKE("revoke", "revoke", "审核撤销"),
    ;

    AuditStatus(String value, String action, String desc) {
        this.value = value;
        this.action = action;
        this.desc = desc;
    }

    private String value;
    private String action;
    private String desc;

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public String getAction() {
        return action;
    }

    public static String getDescByValue(String value) {
        for (AuditStatus auditStatus : values()) {
            if (auditStatus.getValue().equals(value)) {
                return auditStatus.getDesc();
            }
        }
        return value;
    }

    public static String getValueByAction(String action) {
        for (AuditStatus auditStatus : values()) {
            if (auditStatus.getAction().equals(action)) {
                return auditStatus.getValue();
            }
        }
        return action;
    }
}
