package com.welab.databridge.enums;

public enum ProcessStatus {
    DEFAULT("default", "初始状态"),
    PROCESSING("processing", "处理中"),
    FINISH("finish", "处理完成"),
    FAIL("fail", "处理失败");

    ProcessStatus(String status, String remark) {
        this.status = status;
        this.remark = remark;
    }

    private String status;
    private String remark;

    public String getStatus() {
        return status;
    }

    public String getRemark() {
        return remark;
    }
}
