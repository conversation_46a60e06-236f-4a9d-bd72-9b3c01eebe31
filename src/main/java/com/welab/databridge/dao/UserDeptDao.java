package com.welab.databridge.dao;

import com.welab.databridge.dto.UserDeptDto;
import com.welab.databridge.entity.UserDept;

import java.util.List;
import java.util.Map;

/**
 * 用户部门关系表(UserDept)表数据库访问层
 * (代码生成器自动生成)
 *
 * <AUTHOR>
 * @date 2023-01-03 14:29:55
 */
public interface UserDeptDao {

    /**
     * 根据ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    UserDept getById(Integer id);

    /**
     * 列表条件查询
     *
     * @param dto
     * @return
     */
    List<UserDept> queryByCondition(UserDeptDto dto);

    /**
     * 用户列表查询
     *
     * @return
     */
    List<UserDept> listUser();

    /**
     * 新增数据
     *
     * @param userDept 实例对象
     * @return 是否成功
     */
    boolean insert(UserDept userDept);

    /**
     * 新增数据
     *
     * @param list 实例对象
     * @return 是否成功
     */
    boolean insertBatch(List<UserDept> list);

    /**
     * 修改数据
     *
     * @param userDept 实例对象
     * @return 影响行数
     */
    int update(UserDept userDept);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(Integer id);

    /**
     * 通过部门ID删除数据
     *
     * @param map 部门ID
     * @return 是否成功
     */
    boolean deleteByDeptIds(Map map);
}

