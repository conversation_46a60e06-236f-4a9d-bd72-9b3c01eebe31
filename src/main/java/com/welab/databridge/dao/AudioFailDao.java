package com.welab.databridge.dao;

import com.welab.databridge.dto.AudioFailDto;
import com.welab.databridge.entity.AudioFail;

import java.util.List;

/**
 * 音频上传失败记录记录(AudioFail)表数据库访问层
 * (代码生成器自动生成)
 *
 * <AUTHOR>
 * @date 2023-01-03 14:29:57
 */
public interface AudioFailDao {

    /**
     * 根据ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    AudioFail getById(Integer id);

    /**
     * 列表条件查询
     *
     * @param dto
     * @return
     */
    List<AudioFail> queryByCondition(AudioFailDto dto);

    /**
     * 新增数据
     *
     * @param audioFail 实例对象
     * @return 是否成功
     */
    boolean insert(AudioFail audioFail);

    /**
     * 新增数据
     *
     * @param list 实例对象
     * @return 是否成功
     */
    boolean insertBatch(List<AudioFail> list);

    /**
     * 修改数据
     *
     * @param audioFail 实例对象
     * @return 影响行数
     */
    int update(AudioFail audioFail);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(Integer id);

}

