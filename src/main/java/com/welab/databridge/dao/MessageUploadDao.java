package com.welab.databridge.dao;

import com.welab.databridge.dto.MessageUploadDto;
import com.welab.databridge.entity.MessageUpload;

import java.util.List;
import java.util.Map;

/**
 * 短信上传记录(MessageUpload)表数据库访问层
 * (代码生成器自动生成)
 *
 * <AUTHOR>
 * @date 2023-01-03 14:29:47
 */
public interface MessageUploadDao {

    /**
     * 根据ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    MessageUpload getById(Integer id);

    /**
     * 根据ID查询单条数据
     *
     * @param map 主键
     * @return 实例对象
     */
    List<MessageUpload> getByIds(Map map);

    /**
     * 列表条件查询
     *
     * @param dto
     * @return
     */
    List<MessageUpload> queryByCondition(MessageUploadDto dto);

    /**
     * 新增数据
     *
     * @param messageUpload 实例对象
     * @return 是否成功
     */
    boolean insert(MessageUpload messageUpload);

    /**
     * 修改数据
     *
     * @param messageUpload 实例对象
     * @return 影响行数
     */
    int update(MessageUpload messageUpload);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(Integer id);

}

