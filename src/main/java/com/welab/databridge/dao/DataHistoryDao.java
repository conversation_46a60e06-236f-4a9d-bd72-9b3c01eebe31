package com.welab.databridge.dao;


import com.welab.databridge.dto.DataHistoryDto;
import com.welab.databridge.entity.DataHistory;

import java.util.List;

/**
 * 历史版本(DataHistory)表数据库访问层
 * (代码生成器自动生成)
 *
 * <AUTHOR>
 * @date 2022-08-15 17:13:26
 */
public interface DataHistoryDao {

    /**
     * 根据ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    DataHistory getById(Integer id);

    /**
     * 查询单条数据
     *
     * @param dto
     * @return 实例对象
     */
    DataHistory getByCondition(DataHistoryDto dto);

    /**
     * 根据IDs查询数据
     *
     * @param ids 主键
     * @return 实例对象
     */
    List<DataHistory> getByIds(List<Integer> ids);

    /**
     * 列表条件查询
     *
     * @param dto
     * @return
     */
    List<DataHistory> queryByCondition(DataHistoryDto dto);

    /**
     * 新增数据
     *
     * @param dataHistory 实例对象
     * @return 是否成功
     */
    boolean insert(DataHistory dataHistory);

    /**
     * 修改数据
     *
     * @param dataHistory 实例对象
     * @return 影响行数
     */
    int update(DataHistory dataHistory);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(Integer id);

}

