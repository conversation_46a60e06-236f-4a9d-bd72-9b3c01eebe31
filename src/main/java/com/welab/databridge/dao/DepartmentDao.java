package com.welab.databridge.dao;

import com.welab.databridge.dto.DepartmentDto;
import com.welab.databridge.entity.Department;

import java.util.List;
import java.util.Map;

/**
 * 部门表(Department)表数据库访问层
 * (代码生成器自动生成)
 *
 * <AUTHOR>
 * @date 2023-01-03 14:29:44
 */
public interface DepartmentDao {

    /**
     * 根据ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    Department getById(Integer id);

    /**
     * 根据IDs查询数据
     *
     * @param map 主键
     * @return 实例对象
     */
    List<Department> getByIds(Map map);

    /**
     * 列表条件查询
     *
     * @param dto
     * @return
     */
    List<Department> queryByCondition(DepartmentDto dto);

    /**
     * 新增数据
     *
     * @param department 实例对象
     * @return 是否成功
     */
    boolean insert(Department department);

    /**
     * 修改数据
     *
     * @param department 实例对象
     * @return 影响行数
     */
    int update(Department department);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(Integer id);

}

