package com.welab.databridge.dao;

import com.welab.databridge.dto.AudioRecordDto;
import com.welab.databridge.entity.AudioRecord;

import java.util.List;
import java.util.Map;

/**
 * 音频记录(AudioRecord)表数据库访问层
 * (代码生成器自动生成)
 *
 * <AUTHOR>
 * @date 2023-01-03 14:29:52
 */
public interface AudioRecordDao {

    /**
     * 根据ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    AudioRecord getById(Integer id);

    /**
     * 根据ID查询数据
     *
     * @param map 主键
     * @return 实例对象
     */
    List<AudioRecord> getByIds(Map map);

    /**
     * 列表条件查询
     *
     * @param dto
     * @return
     */
    List<AudioRecord> queryByCondition(AudioRecordDto dto);

    /**
     * 新增数据
     *
     * @param audioRecord 实例对象
     * @return 是否成功
     */
    boolean insert(AudioRecord audioRecord);

    /**
     * 新增数据
     *
     * @param list 实例对象
     * @return 是否成功
     */
    boolean insertBatch(List<AudioRecord> list);

    /**
     * 修改数据
     *
     * @param audioRecord 实例对象
     * @return 影响行数
     */
    int update(AudioRecord audioRecord);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(Integer id);

    /**
     * 通过uploadId删除数据
     *
     * @param uploadId 上传主键
     * @return 是否成功
     */
    boolean deleteByUploadId(Integer uploadId);

    /**
     * 查询语音所属部门列表
     *
     * @return
     */
    List<AudioRecord> listDept();

    /**
     * 查询语音所属机构列表
     *
     * @return
     */
    List<AudioRecord> listOrg();

    List<AudioRecord> queryDeptAudio(Map map);

    boolean deleteByStartTime(Map map);

    List<AudioRecord> queryUploadAudio(Map map);

    /**
     * 查询94AI录音中接听状态为成功，而没有URL的记录
     *
     * @param dto
     * @return
     */
    List<AudioRecord> query94Empty(AudioRecordDto dto);
}

