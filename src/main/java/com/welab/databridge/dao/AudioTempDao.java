package com.welab.databridge.dao;

import com.welab.databridge.dto.AudioTempDto;
import com.welab.databridge.entity.AudioTemp;

import java.util.List;
import java.util.Map;

/**
 * (AudioTemp)表数据库访问层
 * (代码生成器自动生成)
 *
 * <AUTHOR>
 * @date 2023-03-17 14:46:34
 */
public interface AudioTempDao {

    /**
     * 根据ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    AudioTemp getById(Integer id);

    /**
     * 根据ID查询单条数据
     *
     * @param map 主键
     * @return 实例对象
     */
    List<AudioTemp> getByIds(Map map);

    /**
     * 列表条件查询
     *
     * @param dto
     * @return
     */
    List<AudioTemp> queryByCondition(AudioTempDto dto);

    /**
     * 新增数据
     *
     * @param audioTemp 实例对象
     * @return 是否成功
     */
    boolean insert(AudioTemp audioTemp);

    /**
     * 批量新增数据
     *
     * @param list 实例对象
     * @return 是否成功
     */
    boolean insertBatch(List<AudioTemp> list);

    /**
     * 修改数据
     *
     * @param audioTemp 实例对象
     * @return 影响行数
     */
    int update(AudioTemp audioTemp);

    /**
     * 更新数据状态
     * @param map
     * @return
     */
    int updateStatus(Map map);

    int updateStatusByDate(Map map);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(Integer id);

    List<AudioTemp> listNullFile();

    boolean deleteByQueryDate(Map map);
}

