package com.welab.databridge.dao;

import com.welab.databridge.dto.MessageRecordDto;
import com.welab.databridge.entity.MessageRecord;

import java.util.List;
import java.util.Map;

/**
 * 短信记录(MessageRecord)表数据库访问层
 * (代码生成器自动生成)
 *
 * <AUTHOR>
 * @date 2023-01-03 14:29:50
 */
public interface MessageRecordDao {

    /**
     * 根据ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    MessageRecord getById(Integer id);

    /**
     * 列表条件查询
     *
     * @param dto
     * @return
     */
    List<MessageRecord> queryByCondition(MessageRecordDto dto);

    /**
     * 新增数据
     *
     * @param messageRecord 实例对象
     * @return 是否成功
     */
    boolean insert(MessageRecord messageRecord);

    /**
     * 修改数据
     *
     * @param messageRecord 实例对象
     * @return 影响行数
     */
    int update(MessageRecord messageRecord);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(Integer id);

    /**
     * 通过uploadId删除数据
     *
     * @param uploadId 上传ID
     * @return 是否成功
     */
    boolean deleteByUploadId(Integer uploadId);

    List<MessageRecord> queryDeptMsg(Map map);
}

