package com.welab.databridge.dao;

import com.welab.databridge.dto.AudioApplyDto;
import com.welab.databridge.entity.AudioApply;

import java.util.List;

/**
 * (AudioApply)表数据库访问层
 * (代码生成器自动生成)
 *
 * <AUTHOR>
 * @date 2023-03-01 18:07:57
 */
public interface AudioApplyDao {

    /**
     * 根据ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    AudioApply getById(Integer id);

    /**
     * 列表条件查询
     *
     * @param dto
     * @return
     */
    List<AudioApply> queryByCondition(AudioApplyDto dto);

    /**
     * 新增数据
     *
     * @param audioApply 实例对象
     * @return 是否成功
     */
    boolean insert(AudioApply audioApply);

    /**
     * 修改数据
     *
     * @param audioApply 实例对象
     * @return 影响行数
     */
    int update(AudioApply audioApply);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(Integer id);

}

