package com.welab.databridge.dao;

import com.welab.databridge.dto.MessageFailDto;
import com.welab.databridge.entity.MessageFail;

import java.util.List;

/**
 * 短信上传失败记录(MessageFail)表数据库访问层
 * (代码生成器自动生成)
 *
 * <AUTHOR>
 * @date 2023-01-03 14:29:41
 */
public interface MessageFailDao {

    /**
     * 根据ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    MessageFail getById(Integer id);

    /**
     * 列表条件查询
     *
     * @param dto
     * @return
     */
    List<MessageFail> queryByCondition(MessageFailDto dto);

    /**
     * 新增数据
     *
     * @param messageFail 实例对象
     * @return 是否成功
     */
    boolean insert(MessageFail messageFail);

    /**
     * 修改数据
     *
     * @param messageFail 实例对象
     * @return 影响行数
     */
    int update(MessageFail messageFail);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(Integer id);

}

