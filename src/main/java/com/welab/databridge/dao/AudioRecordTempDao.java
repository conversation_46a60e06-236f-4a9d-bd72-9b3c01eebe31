package com.welab.databridge.dao;

import com.welab.databridge.dto.AudioRecordTempDto;
import com.welab.databridge.entity.AudioRecordTemp;

import java.util.List;

/**
 * 音频记录临时(AudioRecordTemp)表数据库访问层
 * (代码生成器自动生成)
 *
 * <AUTHOR>
 * @date 2024-03-14 10:50:15
 */
public interface AudioRecordTempDao {

    /**
     * 根据ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    AudioRecordTemp getById(Integer id);

    /**
     * 列表条件查询
     *
     * @param dto
     * @return
     */
    List<AudioRecordTemp> queryByCondition(AudioRecordTempDto dto);

    List<AudioRecordTemp> queryUnSync();

    /**
     * 新增数据
     *
     * @param audioRecordTemp 实例对象
     * @return 是否成功
     */
    boolean insert(AudioRecordTemp audioRecordTemp);

    /**
     * 修改数据
     *
     * @param audioRecordTemp 实例对象
     * @return 影响行数
     */
    int update(AudioRecordTemp audioRecordTemp);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(Integer id);

}

