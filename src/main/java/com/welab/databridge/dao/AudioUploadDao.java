package com.welab.databridge.dao;

import com.welab.databridge.dto.AudioUploadDto;
import com.welab.databridge.entity.AudioUpload;

import java.util.List;
import java.util.Map;

/**
 * 音频上传记录(AudioUpload)表数据库访问层
 * (代码生成器自动生成)
 *
 * <AUTHOR>
 * @date 2023-01-03 14:29:38
 */
public interface AudioUploadDao {

    /**
     * 根据ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    AudioUpload getById(Integer id);

    /**
     * 根据ID查询数据
     *
     * @param map 主键
     * @return 实例对象
     */
    List<AudioUpload> getByIds(Map map);

    /**
     * 列表条件查询
     *
     * @param dto
     * @return
     */
    List<AudioUpload> queryByCondition(AudioUploadDto dto);

    /**
     * 新增数据
     *
     * @param audioUpload 实例对象
     * @return 是否成功
     */
    boolean insert(AudioUpload audioUpload);

    /**
     * 修改数据
     *
     * @param audioUpload 实例对象
     * @return 影响行数
     */
    int update(AudioUpload audioUpload);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    boolean deleteById(Integer id);

}

