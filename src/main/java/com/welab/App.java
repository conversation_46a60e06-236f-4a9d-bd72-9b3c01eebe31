package com.welab;

import com.alibaba.dubbo.spring.boot.annotation.EnableDubboConfiguration;
import com.alibaba.dubbo.spring.boot.context.event.DubboBannerApplicationListener;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.Banner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.transaction.annotation.EnableTransactionManagement;


/**
 * Hello world!
 *
 */
@EnableDubboConfiguration
@SpringBootApplication
@EnableTransactionManagement
@MapperScan({"com.welab.databridge.dao"})
@Slf4j
@EnableApolloConfig
public class App 
{
    public static void main( String[] args )
    {
        DubboBannerApplicationListener.setBANNER_MODE(Banner.Mode.OFF);
    	SpringApplication.run(App.class, args);
        log.info("系统启动成功......");
    }
}
