<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
    (代码自动生成器生成)
    @auth Kawin赵健
-->
<mapper namespace="com.welab.databridge.dao.MessageFailDao">

    <resultMap type="com.welab.databridge.entity.MessageFail" id="MessageFailMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="uploadId" column="upload_id" jdbcType="INTEGER"/>
        <result property="deptId" column="dept_id" jdbcType="INTEGER"/>
        <result property="phone" column="phone" jdbcType="VARCHAR"/>
        <result property="content" column="content" jdbcType="VARCHAR"/>
        <result property="supplier" column="supplier" jdbcType="VARCHAR"/>
        <result property="sendTime" column="send_time" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="VARCHAR"/>
        <result property="failMsg" column="fail_msg" jdbcType="VARCHAR"/>
        <result property="operator" column="operator" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!--根据ID查询单个-->
    <select id="getById" resultMap="MessageFailMap">
        select id,
               upload_id,
               dept_id,
               phone,
               content,
               supplier,
               send_time,
               status,
               fail_msg,
               operator,
               create_time
        from message_fail
        where id = #{id}
    </select>

    <!--列表条件查询-->
    <select id="queryByCondition" resultMap="MessageFailMap">
        select
        id, upload_id, dept_id, phone, content, supplier, send_time, status, fail_msg, operator, create_time,
        (select name from department where id = message_fail.dept_id) as deptName
        from message_fail
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="uploadId != null">
                and upload_id = #{uploadId}
            </if>
            <if test="deptIds != null">
                and dept_id in
                <foreach collection="deptIds" item="deptId" index="index" open="(" close=")" separator=",">
                    #{deptId}
                </foreach>
            </if>
            <if test="content != null and content != ''">
                and content = #{content}
            </if>
            <if test="supplier != null and supplier != ''">
                and supplier = #{supplier}
            </if>
            <if test="sendTime != null and sendTime != ''">
                and send_time = #{sendTime}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="failMsg != null and failMsg != ''">
                and fail_msg = #{failMsg}
            </if>
            <if test="operator != null and operator != ''">
                and operator = #{operator}
            </if>
            order by create_time desc
        </where>

    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into message_fail(upload_id, dept_id, phone, content, supplier, send_time, status, fail_msg, operator,
                                 create_time)
        values (#{uploadId}, #{deptId}, #{phone}, #{content}, #{supplier}, #{sendTime}, #{status}, #{failMsg}, #{operator},
                #{createTime})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update message_fail
        <set>
            <if test="uploadId != null">
                upload_id = #{uploadId},
            </if>
            <if test="deptId != null">
                dept_id = #{deptId},
            </if>
            <if test="phone != null and phone != ''">
                phone = #{phone},
            </if>
            <if test="content != null and content != ''">
                content = #{content},
            </if>
            <if test="supplier != null and supplier != ''">
                supplier = #{supplier},
            </if>
            <if test="sendTime != null and sendTime != ''">
                send_time = #{sendTime},
            </if>
            <if test="status != null and status != ''">
                status = #{status},
            </if>
            <if test="failMsg != null and failMsg != ''">
                fail_msg = #{failMsg},
            </if>
            <if test="operator != null and operator != ''">
                operator = #{operator},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from message_fail
        where id = #{id}
    </delete>

</mapper>

