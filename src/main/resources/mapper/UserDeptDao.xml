<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
    (代码自动生成器生成)
    @auth Kawin赵健
-->
<mapper namespace="com.welab.databridge.dao.UserDeptDao">

    <resultMap type="com.welab.databridge.entity.UserDept" id="UserDeptMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="deptId" column="dept_id" jdbcType="INTEGER"/>
        <result property="userid" column="userid" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="phone" column="phone" jdbcType="VARCHAR"/>
        <result property="email" column="email" jdbcType="VARCHAR"/>
        <result property="operator" column="operator" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!--根据ID查询单个-->
    <select id="getById" resultMap="UserDeptMap">
        select id,
               dept_id,
               userid,
               name,
               phone,
               email,
               operator,
               create_time
        from user_dept
        where id = #{id}
    </select>

    <!--列表条件查询-->
    <select id="queryByCondition" resultMap="UserDeptMap">
        select
        id, dept_id, userid, name, phone, email, operator, create_time
        from user_dept
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="deptId != null">
                and dept_id = #{deptId}
            </if>
            <if test="userid != null and userid != ''">
                and userid = #{userid}
            </if>
            <if test="name != null and name != ''">
                and name = #{name}
            </if>
            <if test="phone != null and phone != ''">
                and phone = #{phone}
            </if>
            <if test="email != null and email != ''">
                and email = #{email}
            </if>
            <if test="operator != null and operator != ''">
                and operator = #{operator}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
        </where>

    </select>

    <!--用户列表查询-->
    <select id="listUser" resultMap="UserDeptMap">
        select
         distinct name
        from user_dept
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into user_dept(dept_id, userid, name, phone, email, operator, create_time)
        values (#{deptId}, #{userid}, #{name}, #{phone}, #{email}, #{operator}, #{createTime})
    </insert>

    <!--新增-->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into user_dept(dept_id, userid, name, phone, email, operator, create_time)
        values
        <foreach collection="list" separator="," item="item">
               (#{item.deptId}, #{item.userid}, #{item.name}, #{item.phone}, #{item.email}, #{item.operator}, #{item.createTime})
        </foreach>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update user_dept
        <set>
            <if test="deptId != null">
                dept_id = #{deptId},
            </if>
            <if test="userid != null and userid != ''">
                userid = #{userid},
            </if>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="phone != null and phone != ''">
                phone = #{phone},
            </if>
            <if test="email != null and email != ''">
                email = #{email},
            </if>
            <if test="operator != null and operator != ''">
                operator = #{operator},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from user_dept
        where id = #{id}
    </delete>

    <!--通过部门ID删除数据-->
    <delete id="deleteByDeptIds">
        delete
        from user_dept
        where dept_id in
        <foreach collection="deptIds" item="deptId" index="index" open="(" close=")" separator=",">
            #{deptId}
        </foreach>
    </delete>

</mapper>

