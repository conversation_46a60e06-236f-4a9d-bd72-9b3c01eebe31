<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
    (代码自动生成器生成)
    @auth Kawin赵健
-->
<mapper namespace="com.welab.databridge.dao.AudioFailDao">

    <resultMap type="com.welab.databridge.entity.AudioFail" id="AudioFailMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="uploadId" column="upload_id" jdbcType="INTEGER"/>
        <result property="deptId" column="dept_id" jdbcType="INTEGER"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="uploadMethod" column="upload_method" jdbcType="INTEGER"/>
        <result property="failMsg" column="fail_msg" jdbcType="VARCHAR"/>
        <result property="operator" column="operator" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!--根据ID查询单个-->
    <select id="getById" resultMap="AudioFailMap">
        select id,
               upload_id,
               dept_id,
               name,
               upload_method,
               fail_msg,
               operator,
               create_time
        from audio_fail
        where id = #{id}
    </select>

    <!--列表条件查询-->
    <select id="queryByCondition" resultMap="AudioFailMap">
        select
        id, upload_id, dept_id, name, upload_method, fail_msg, operator, create_time,
               (select name from department where id = audio_fail.dept_id) as deptName
        from audio_fail
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="uploadId != null">
                and upload_id = #{uploadId}
            </if>
            <if test="deptId != null">
                and dept_id = #{deptId}
            </if>
            <if test="name != null and name != ''">
                and name = #{name}
            </if>
            <if test="uploadMethod != null">
                and upload_method = #{uploadMethod}
            </if>
            <if test="failMsg != null and failMsg != ''">
                and fail_msg = #{failMsg}
            </if>
            <if test="operator != null and operator != ''">
                and operator = #{operator}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
        </where>

    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into audio_fail(upload_id, dept_id, name, upload_method, fail_msg, operator, create_time)
        values (#{uploadId}, #{deptId}, #{name}, #{uploadMethod}, #{failMsg}, #{operator}, #{createTime})
    </insert>

    <!--新增所有列-->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into audio_fail(upload_id, dept_id, name, upload_method, fail_msg, operator, create_time)
        values
        <foreach collection="list" separator="," item="item">
               (#{item.uploadId}, #{item.deptId}, #{item.name}, #{item.uploadMethod}, #{item.failMsg}, #{item.operator}, #{item.createTime})
        </foreach>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update audio_fail
        <set>
            <if test="uploadId != null">
                upload_id = #{uploadId},
            </if>
            <if test="deptId != null">
                dept_id = #{deptId},
            </if>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="uploadMethod != null">
                upload_method = #{uploadMethod},
            </if>
            <if test="failMsg != null and failMsg != ''">
                fail_msg = #{failMsg},
            </if>
            <if test="operator != null and operator != ''">
                operator = #{operator},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from audio_fail
        where id = #{id}
    </delete>

</mapper>

