<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
    (代码自动生成器生成)
    @auth Kawin赵健
-->
<mapper namespace="com.welab.databridge.dao.AudioApplyDao">

    <resultMap type="com.welab.databridge.entity.AudioApply" id="AudioApplyMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="audioIds" column="audio_ids" jdbcType="VARCHAR"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result property="reason" column="reason" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="VARCHAR"/>
        <result property="approveStatus" column="approve_status" jdbcType="VARCHAR"/>
        <result property="applier" column="applier" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!--根据ID查询单个-->
    <select id="getById" resultMap="AudioApplyMap">
        select id,
               audio_ids,
               reason,
               status,
               approve_status,
               applier,
               create_time
        from audio_apply
        where id = #{id}
    </select>

    <!--列表条件查询-->
    <select id="queryByCondition" resultMap="AudioApplyMap">
        select
        id, audio_ids, reason, status, approve_status, applier, create_time
        from audio_apply
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="reason != null and reason != ''">
                and reason = #{reason}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="approveStatus != null and approveStatus != ''">
                and approve_status = #{approveStatus}
            </if>
            <if test="applier != null and applier != ''">
                and applier = #{applier}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="startTime != null">
                and create_time >= #{startTime}
            </if>
            <if test="endTime != null">
                and create_time <![CDATA[ <= ]]> #{endTime}
            </if>
        </where>

    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into audio_apply(audio_ids, reason, status, approve_status, applier, create_time)
        values (#{audioIds, typeHandler=com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler},
                #{reason}, #{status}, #{approveStatus}, #{applier}, #{createTime})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update audio_apply
        <set>
            <if test="audioIds != null">
                audio_ids = #{audioIds, typeHandler=com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler},
            </if>
            <if test="reason != null and reason != ''">
                reason = #{reason},
            </if>
            <if test="status != null and status != ''">
                status = #{status},
            </if>
            <if test="approveStatus != null and approveStatus != ''">
                approve_status = #{approveStatus},
            </if>
            <if test="applier != null and applier != ''">
                applier = #{applier},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from audio_apply
        where id = #{id}
    </delete>

</mapper>

