<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
    (代码自动生成器生成)
    @auth Kawin赵健
-->
<mapper namespace="com.welab.databridge.dao.AudioUploadDao">

    <resultMap type="com.welab.databridge.entity.AudioUpload" id="AudioUploadMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="deptId" column="dept_id" jdbcType="INTEGER"/>
        <result property="batchNo" column="batch_no" jdbcType="VARCHAR"/>
        <result property="uploadNum" column="upload_num" jdbcType="INTEGER"/>
        <result property="successNum" column="success_num" jdbcType="INTEGER"/>
        <result property="failNum" column="fail_num" jdbcType="INTEGER"/>
        <result property="operation" column="operation" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="VARCHAR"/>
        <result property="failMsg" column="fail_msg" jdbcType="VARCHAR"/>
        <result property="approveStatus" column="approve_status" jdbcType="VARCHAR"/>
        <result property="approveType" column="approve_type" jdbcType="VARCHAR"/>
        <result property="operator" column="operator" jdbcType="VARCHAR"/>
        <result property="agentId" column="agent_id" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!--根据ID查询单个-->
    <select id="getById" resultMap="AudioUploadMap">
        select id,
               dept_id,
               batch_no,
               upload_num,
               success_num,
               fail_num,
               operation,
               status,
               fail_msg,
               approve_status,
               approve_type,
               operator,
               agent_id,
               create_time,
               update_time, (select name from department where id = audio_upload.dept_id) as deptName
        from audio_upload
        where id = #{id}
    </select>

    <!--根据ID查询-->
    <select id="getByIds" resultMap="AudioUploadMap">
        select id,
               dept_id,
               batch_no,
               upload_num,
               success_num,
               fail_num,
               operation,
               status,
               fail_msg,
               approve_status,
               approve_type,
               operator,
               agent_id,
               create_time,
               update_time, (select name from department where id = audio_upload.dept_id) as deptName
        from audio_upload
        where id in
        <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <!--列表条件查询-->
    <select id="queryByCondition" resultMap="AudioUploadMap">
        select
        id, dept_id, batch_no, upload_num, success_num, fail_num, operation, status, fail_msg, approve_status, approve_type,
        operator, agent_id, create_time, update_time, (select name from department where id = audio_upload.dept_id) as deptName
        from audio_upload
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="deptIds != null">
                and dept_id in
                <foreach collection="deptIds" item="deptId" index="index" open="(" close=")" separator=",">
                    #{deptId}
                </foreach>
            </if>
            <if test="batchNo != null and batchNo != ''">
                and batch_no = #{batchNo}
            </if>
            <if test="uploadNum != null">
                and upload_num = #{uploadNum}
            </if>
            <if test="successNum != null">
                and success_num = #{successNum}
            </if>
            <if test="failNum != null">
                and fail_num = #{failNum}
            </if>
            <if test="operation != null and operation != ''">
                and operation = #{operation}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="approveStatus != null and approveStatus != ''">
                and approve_status = #{approveStatus}
            </if>
            <if test="approveType != null and approveType != ''">
                and approve_type = #{approveType}
            </if>
            <if test="operator != null and operator != ''">
                and operator = #{operator}
            </if>
            <if test="startTime != null and startTime != ''">
                and create_time >= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                and create_time <![CDATA[<=]]> #{endTime}
            </if>
        </where>
        order by create_time desc
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into audio_upload(dept_id, batch_no, upload_num, success_num, fail_num, operation, status, fail_msg,
                                 approve_status, approve_type, operator, agent_id, create_time, update_time)
        values (#{deptId}, #{batchNo}, #{uploadNum}, #{successNum}, #{failNum}, #{operation}, #{status}, #{failMsg},
                #{approveStatus}, #{approveType}, #{operator}, #{agentId}, #{createTime}, #{updateTime})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update audio_upload
        <set>
            <if test="deptId != null">
                dept_id = #{deptId},
            </if>
            <if test="batchNo != null and batchNo != ''">
                batch_no = #{batchNo},
            </if>
            <if test="uploadNum != null">
                upload_num = #{uploadNum},
            </if>
            <if test="successNum != null">
                success_num = #{successNum},
            </if>
            <if test="failNum != null">
                fail_num = #{failNum},
            </if>
            <if test="operation != null and operation != ''">
                operation = #{operation},
            </if>
            <if test="status != null and status != ''">
                status = #{status},
            </if>
            <if test="failMsg != null and failMsg != ''">
                fail_msg = #{failMsg},
            </if>
            <if test="approveStatus != null and approveStatus != ''">
                approve_status = #{approveStatus},
            </if>
            <if test="approveType != null and approveType != ''">
                approve_type = #{approveType},
            </if>
            <if test="operator != null and operator != ''">
                operator = #{operator},
            </if>
            <if test="agentId != null and agentId != ''">
                agent_id = #{agentId},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from audio_upload
        where id = #{id}
    </delete>

</mapper>

