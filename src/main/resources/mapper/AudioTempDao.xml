<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
    (代码自动生成器生成)
    @auth Kawin赵健
-->
<mapper namespace="com.welab.databridge.dao.AudioTempDao">

    <resultMap type="com.welab.databridge.entity.AudioTemp" id="AudioTempMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="cno" column="cno" jdbcType="VARCHAR"/>
        <result property="mobile" column="mobile" jdbcType="VARCHAR"/>
        <result property="sign" column="sign" jdbcType="VARCHAR"/>
        <result property="recordFile" column="recordFile" jdbcType="VARCHAR"/>
        <result property="version" column="version" jdbcType="VARCHAR"/>
        <result property="callType" column="callType" jdbcType="VARCHAR"/>
        <result property="provider" column="provider" jdbcType="VARCHAR"/>
        <result property="callStatus" column="callStatus" jdbcType="VARCHAR"/>
        <result property="staffName" column="staffName" jdbcType="VARCHAR"/>
        <result property="startTime" column="startTime" jdbcType="VARCHAR"/>
        <result property="endTime" column="endTime" jdbcType="VARCHAR"/>
        <result property="enterpriseId" column="enterpriseId" jdbcType="VARCHAR"/>
        <result property="region" column="region" jdbcType="VARCHAR"/>
        <result property="enterpriseName" column="enterpriseName" jdbcType="VARCHAR"/>
        <result property="groupCode" column="groupCode" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="VARCHAR"/>
        <result property="queryDate" column="queryDate" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!--根据ID查询单个-->
    <select id="getById" resultMap="AudioTempMap">
        select id,
               cno,
               mobile,
               sign,
               recordFile,
               version,
               callType,
               provider,
               callStatus,
               staffName,
               startTime,
               endTime,
               enterpriseId,
               region,
               enterpriseName,
               groupCode,
               status,
               queryDate,
               create_time
        from audio_temp
        where id = #{id}
    </select>

    <!--根据ID查询-->
    <select id="getByIds" resultMap="AudioTempMap">
        select id,
               cno,
               mobile,
               sign,
               recordFile,
               version,
               callType,
               provider,
               callStatus,
               staffName,
               startTime,
               endTime,
               enterpriseId,
               region,
               enterpriseName,
               groupCode,
               status,
               queryDate,
               create_time
        from audio_temp
        where id in
        <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <!--列表条件查询-->
    <select id="queryByCondition" resultMap="AudioTempMap">
        select
        id, cno, mobile, sign, recordFile, version, callType, provider, callStatus, staffName, startTime, endTime,
        enterpriseId, region, enterpriseName, groupCode, status, queryDate, create_time
        from audio_temp
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="cno != null and cno != ''">
                and cno = #{cno}
            </if>
            <if test="mobile != null and mobile != ''">
                and mobile = #{mobile}
            </if>
            <if test="sign != null and sign != ''">
                and sign = #{sign}
            </if>
            <if test="recordFile != null and recordFile != ''">
                and recordFile = #{recordFile}
            </if>
            <if test="version != null and version != ''">
                and version = #{version}
            </if>
            <if test="callType != null and callType != ''">
                and callType = #{callType}
            </if>
            <if test="provider != null and provider != ''">
                and provider = #{provider}
            </if>
            <if test="callStatus != null and callStatus != ''">
                and callStatus = #{callStatus}
            </if>
            <if test="staffName != null and staffName != ''">
                and staffName = #{staffName}
            </if>
            <if test="startTime != null and startTime != ''">
                and startTime = #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                and endTime = #{endTime}
            </if>
            <if test="enterpriseId != null and enterpriseId != ''">
                and enterpriseId = #{enterpriseId}
            </if>
            <if test="region != null and region != ''">
                and region = #{region}
            </if>
            <if test="enterpriseName != null and enterpriseName != ''">
                and enterpriseName = #{enterpriseName}
            </if>
            <if test="groupCode != null and groupCode != ''">
                and groupCode = #{groupCode}
            </if>
            <if test="queryDate != null and queryDate != ''">
                and queryDate = #{queryDate}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
        </where>
        <if test="sort != null and sort != ''">
            order by queryDate ${sort}
        </if>
        <if test="limit != null and limit != ''">
            limit #{limit}
        </if>
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into audio_temp(cno, mobile, sign, recordFile, version, callType, provider, callStatus, staffName,
                               startTime, endTime, enterpriseId, region, enterpriseName, groupCode, status, queryDate, create_time)
        values (#{cno}, #{mobile}, #{sign}, #{recordFile}, #{version}, #{callType}, #{provider}, #{callStatus},
                #{staffName}, #{startTime}, #{endTime}, #{enterpriseId}, #{region}, #{enterpriseName}, #{groupCode},
                #{status}, #{queryDate}, #{createTime})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into audio_temp(cno, mobile, sign, recordFile, version, callType, provider, callStatus, staffName,
                               startTime, endTime, enterpriseId, region, enterpriseName, groupCode, status, queryDate, create_time)
        values
        <foreach collection="list" separator="," item="item">
               (#{item.cno}, #{item.mobile}, #{item.sign}, #{item.recordFile}, #{item.version}, #{item.callType}, #{item.provider}, #{item.callStatus},
                #{item.staffName}, #{item.startTime}, #{item.endTime}, #{item.enterpriseId}, #{item.region}, #{item.enterpriseName}, #{item.groupCode},
                #{item.status}, #{item.queryDate}, #{item.createTime})
        </foreach>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update audio_temp
        <set>
            <if test="cno != null and cno != ''">
                cno = #{cno},
            </if>
            <if test="mobile != null and mobile != ''">
                mobile = #{mobile},
            </if>
            <if test="sign != null and sign != ''">
                sign = #{sign},
            </if>
            <if test="recordFile != null and recordFile != ''">
                recordFile = #{recordFile},
            </if>
            <if test="version != null and version != ''">
                version = #{version},
            </if>
            <if test="callType != null and callType != ''">
                callType = #{callType},
            </if>
            <if test="provider != null and provider != ''">
                provider = #{provider},
            </if>
            <if test="callStatus != null and callStatus != ''">
                callStatus = #{callStatus},
            </if>
            <if test="staffName != null and staffName != ''">
                staffName = #{staffName},
            </if>
            <if test="startTime != null and startTime != ''">
                startTime = #{startTime},
            </if>
            <if test="endTime != null and endTime != ''">
                endTime = #{endTime},
            </if>
            <if test="enterpriseId != null and enterpriseId != ''">
                enterpriseId = #{enterpriseId},
            </if>
            <if test="region != null and region != ''">
                region = #{region},
            </if>
            <if test="enterpriseName != null and enterpriseName != ''">
                enterpriseName = #{enterpriseName},
            </if>
            <if test="groupCode != null and groupCode != ''">
                groupCode = #{groupCode},
            </if>
            <if test="status != null and status != ''">
                status = #{status},
            </if>
            <if test="queryDate != null and queryDate != ''">
                queryDate = #{queryDate},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键修改数据-->
    <update id="updateStatus">
        update audio_temp
        set status = #{status}
        where id in
        <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>

    <update id="updateStatusByDate">
        update audio_temp
        set status = #{status}
        where queryDate = #{queryDate}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from audio_temp
        where id = #{id}
    </delete>

    <select id="listNullFile" resultMap="AudioTempMap">
        select
        id, cno, mobile, sign, recordFile, version, callType, provider, callStatus, staffName, startTime, endTime,
        enterpriseId, region, enterpriseName, groupCode, status, queryDate, create_time
        from audio_temp
        where provider = 'TR'
        and callStatus = '接听成功'
        and recordFile is null
    </select>

    <delete id="deleteByQueryDate">
        delete
        from audio_temp
        where queryDate >= #{startDate}
        and queryDate <![CDATA[ < ]]> #{endDate}
        <if test="enterpriseId != null">
            and enterpriseId = #{enterpriseId}
        </if>
    </delete>
</mapper>

