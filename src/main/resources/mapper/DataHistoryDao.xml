<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
    (代码自动生成器生成)
    @auth Kawin赵健
-->
<mapper namespace="com.welab.databridge.dao.DataHistoryDao">

    <resultMap type="com.welab.databridge.entity.DataHistory" id="DataHistoryMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="bizType" column="biz_type" jdbcType="VARCHAR"/>
        <result property="bizId" column="biz_id" jdbcType="VARCHAR"/>
        <result property="operation" column="operation" jdbcType="VARCHAR"/>
        <result property="version" column="version" jdbcType="VARCHAR"/>
        <result property="content" column="content" jdbcType="VARCHAR"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler"/>
        <result property="operatorId" column="operator_id" jdbcType="VARCHAR"/>
        <result property="operatorName" column="operator_name" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!--根据ID查询单个-->
    <select id="getById" resultMap="DataHistoryMap">
        select id,
               biz_type,
               biz_id,
               operation,
               version,
               content,
               operator_id,
               operator_name,
               create_time,
               update_time
        from data_history
        where id = #{id}
    </select>

    <!--单条条件查询-->
    <select id="getByCondition" resultMap="DataHistoryMap">
        select
        id, biz_type, biz_id, operation, version, content, operator_id, operator_name, create_time, update_time
        from data_history
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="bizType != null and bizType != ''">
                and biz_type = #{bizType}
            </if>
            <if test="bizId != null and bizId != ''">
                and biz_id = #{bizId}
            </if>
            <if test="operation != null and operation != ''">
                and operation = #{operation}
            </if>
            <if test="version != null and version != ''">
                and version = #{version}
            </if>
            <if test="content != null">
                and content = #{content}
            </if>
        </where>
        order by id desc
        limit 1
    </select>

    <!--根据IDs查询-->
    <select id="getByIds" resultMap="DataHistoryMap">
        select id,
        biz_type,
        biz_id,
        operation,
        version,
        content,
        operator_id,
        operator_name,
        create_time,
        update_time
        from data_history
        where id in
        <foreach collection="list" index="index" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <!--列表条件查询-->
    <select id="queryByCondition" resultMap="DataHistoryMap">
        select
        id, biz_type, biz_id, operation, version, content, operator_id, operator_name, create_time, update_time
        from data_history
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="bizType != null and bizType != ''">
                and biz_type = #{bizType}
            </if>
            <if test="bizId != null and bizId != ''">
                and biz_id = #{bizId}
            </if>
            <if test="operation != null and operation != ''">
                and operation = #{operation}
            </if>
            <if test="version != null and version != ''">
                and version = #{version}
            </if>
            <if test="content != null">
                and content = #{content}
            </if>
        </where>

    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into data_history(biz_type, biz_id, operation, version, content, operator_id, operator_name, create_time,
                                 update_time)
        values (#{bizType}, #{bizId}, #{operation}, #{version}, #{content, typeHandler=com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler},
                #{operatorId}, #{operatorName}, #{createTime}, #{updateTime})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update data_history
        <set>
            <if test="bizType != null and bizType != ''">
                biz_type = #{bizType},
            </if>
            <if test="bizId != null and bizId != ''">
                biz_id = #{bizId},
            </if>
            <if test="operation != null and operation != ''">
                operation = #{operation},
            </if>
            <if test="version != null and version != ''">
                version = #{version},
            </if>
            <if test="content != null">
                content = #{content, typeHandler=com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler},
            </if>
            <if test="operatorId != null and operatorId != ''">
                operator_id = #{operatorId},
            </if>
            <if test="operatorName != null and operatorName != ''">
                operator_name = #{operatorName},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from data_history
        where id = #{id}
    </delete>

</mapper>

