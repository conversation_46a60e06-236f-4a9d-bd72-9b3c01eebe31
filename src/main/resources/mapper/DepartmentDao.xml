<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
    (代码自动生成器生成)
    @auth Kawin赵健
-->
<mapper namespace="com.welab.databridge.dao.DepartmentDao">

    <resultMap type="com.welab.databridge.entity.Department" id="DepartmentMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="parentId" column="parent_id" jdbcType="INTEGER"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="operator" column="operator" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!--根据ID查询单个-->
    <select id="getById" resultMap="DepartmentMap">
        select id,
               parent_id,
               name,
               operator,
               create_time,
               update_time
        from department
        where id = #{id}
    </select>

    <select id="getByIds" resultMap="DepartmentMap">
        select id,
               parent_id,
               name,
               operator,
               create_time,
               update_time
        from department
        where id in
        <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <!--列表条件查询-->
    <select id="queryByCondition" resultMap="DepartmentMap">
        select
        id, parent_id, name, operator, create_time, update_time
        from department
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="parentId != null">
                and parent_id = #{parentId}
            </if>
            <if test="name != null and name != ''">
                and name = #{name}
            </if>
        </where>

    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into department(parent_id, name, operator, create_time, update_time)
        values (#{parentId}, #{name}, #{operator}, #{createTime}, #{updateTime})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update department
        <set>
            <if test="parentId != null">
                parent_id = #{parentId},
            </if>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="operator != null and operator != ''">
                operator = #{operator},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from department
        where id = #{id}
    </delete>

</mapper>

