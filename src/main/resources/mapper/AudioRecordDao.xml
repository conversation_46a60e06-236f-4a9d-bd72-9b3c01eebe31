<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
    (代码自动生成器生成)
    @auth Kawin赵健
-->
<mapper namespace="com.welab.databridge.dao.AudioRecordDao">

    <resultMap type="com.welab.databridge.entity.AudioRecord" id="AudioRecordMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="uploadId" column="upload_id" jdbcType="INTEGER"/>
        <result property="deptId" column="dept_id" jdbcType="INTEGER"/>
        <result property="userid" column="userid" jdbcType="VARCHAR"/>
        <result property="phone" column="phone" jdbcType="VARCHAR"/>
        <result property="cnid" column="cnid" jdbcType="VARCHAR"/>
        <result property="url" column="url" jdbcType="VARCHAR"/>
        <result property="startTime" column="start_time" jdbcType="TIMESTAMP"/>
        <result property="endTime" column="end_time" jdbcType="TIMESTAMP"/>
        <result property="callType" column="call_type" jdbcType="INTEGER"/>
        <result property="uploadTime" column="upload_time" jdbcType="TIMESTAMP"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="uploadMethod" column="upload_method" jdbcType="INTEGER"/>
        <result property="supplier" column="supplier" jdbcType="VARCHAR"/>
        <result property="dept" column="dept" jdbcType="VARCHAR"/>
        <result property="org" column="org" jdbcType="VARCHAR"/>
        <result property="listened" column="listened" jdbcType="INTEGER"/>
        <result property="listenTime" column="listen_time" jdbcType="TIMESTAMP"/>
        <result property="listener" column="listener" jdbcType="VARCHAR"/>
        <result property="comment" column="comment" jdbcType="VARCHAR"/>
        <result property="duration" column="duration" jdbcType="INTEGER"/>
        <result property="deleted" column="deleted" jdbcType="INTEGER"/>
        <result property="operator" column="operator" jdbcType="VARCHAR"/>
        <result property="agentId" column="agent_id" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!--根据ID查询单个-->
    <select id="getById" resultMap="AudioRecordMap">
        select id,
               upload_id,
               dept_id,
               userid,
               phone,
               cnid,
               url,
               start_time,
               end_time,
               call_type,
               upload_time,
               status,
               upload_method,
               supplier,
               dept,
               org,
               listened,
               comment,
               deleted,
               operator,
               agent_id,
               listener,
               create_time, (select name from department where id = audio_record.dept_id) as deptName
        from audio_record
        where id = #{id}
    </select>

    <select id="getByIds" resultMap="AudioRecordMap">
        select id,
               upload_id,
               dept_id,
               userid,
               phone,
               cnid,
               url,
               start_time,
               end_time,
               call_type,
               upload_time,
               status,
               upload_method,
               supplier,
               dept,
               org,
               listened,
               listen_time,
               listener,
               comment,
               duration,
               deleted,
               operator,
               agent_id,
               create_time, (select name from department where id = audio_record.dept_id) as deptName
        from audio_record
        where id in
        <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <!--列表条件查询-->
    <select id="queryByCondition" resultMap="AudioRecordMap">
        select
        id, upload_id, dept_id, userid, phone, cnid, start_time, end_time, call_type, upload_time, status,
        upload_method, supplier, dept, org, listened, deleted, operator, agent_id, create_time,
        listen_time, listener, duration, comment,
        (select name from department where id = audio_record.dept_id) as deptName
        from audio_record
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="uploadId != null">
                and upload_id = #{uploadId}
            </if>
            <if test="deptIds != null">
                and dept_id in
                <foreach collection="deptIds" item="deptId" index="index" open="(" close=")" separator=",">
                    #{deptId}
                </foreach>
            </if>
            <if test="userids != null">
                and userid in
                <foreach collection="userids" item="userid" index="index" open="(" close=")" separator=",">
                    #{userid}
                </foreach>
            </if>
            <if test="phones != null">
                and phone in
                <foreach collection="phones" item="phone" index="index" open="(" close=")" separator=",">
                    #{phone}
                </foreach>
            </if>
            <if test="cnids != null">
                and cnid in
                <foreach collection="cnids" item="cnid" index="index" open="(" close=")" separator=",">
                    #{cnid}
                </foreach>
            </if>
            <if test="startTime != null">
                and start_time >= #{startTime}
            </if>
            <if test="endTime != null">
                and start_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="callType != null">
                and call_type = #{callType}
            </if>
            <if test="uploadTime != null">
                and upload_time = #{uploadTime}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="uploadMethod != null">
                and upload_method = #{uploadMethod}
            </if>
            <if test="supplier != null and supplier != ''">
                and supplier = #{supplier}
            </if>
            <if test="dept != null and dept != ''">
                and dept = #{dept}
            </if>
            <if test="org != null and org != ''">
                and org = #{org}
            </if>
            <if test="listened != null">
                and listened = #{listened}
            </if>
            <if test="listenStartTime != null">
                and listen_time >= #{listenStartTime}
            </if>
            <if test="listenEndTime != null">
                and listen_time <![CDATA[<=]]> #{listenEndTime}
            </if>
            <if test="listener != null and listener != ''">
                and listener = #{listener}
            </if>
            <if test="deleted != null">
                and deleted = #{deleted}
            </if>
            <if test="deleted == null">
                and deleted = 0
            </if>
            <if test="operator != null and operator != ''">
                and operator = #{operator}
            </if>
            <if test="durationMin != null and durationMin != ''">
                and duration >= #{durationMin}
            </if>
            <if test="durationMax != null and durationMax != ''">
                and duration <![CDATA[<=]]> #{durationMax}
            </if>
        
        </where>
        order by start_time desc
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into audio_record(upload_id, dept_id, userid, phone, cnid, url, start_time, end_time, call_type, upload_time,
                                 status, upload_method, supplier, dept, org, operator, agent_id, create_time, listen_time, listener, duration, comment)
        values (#{uploadId}, #{deptId}, #{userid}, #{phone}, #{cnid}, #{url}, #{startTime}, #{endTime}, #{callType},
                #{uploadTime}, #{status}, #{uploadMethod}, #{supplier}, #{dept}, #{org}, #{operator}, #{agentId}, #{createTime}, #{listenTime}, #{listener}, #{duration}, #{comment})
    </insert>

    <!--新增所有列-->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into audio_record(upload_id, dept_id, userid, phone, cnid, url, start_time, end_time, call_type, upload_time,
                                 status, upload_method, supplier, dept, org, operator, agent_id, create_time, listen_time, listener, duration)
        values
        <foreach collection="list" separator="," item="item">
               (#{item.uploadId}, #{item.deptId}, #{item.userid}, #{item.phone}, #{item.cnid}, #{item.url}, #{item.startTime}, #{item.endTime}, #{item.callType},
                #{item.uploadTime}, #{item.status}, #{item.uploadMethod}, #{item.supplier}, #{item.dept}, #{item.org}, #{item.operator}, #{item.agentId}, #{item.createTime}, #{item.listenTime}, #{item.listener}, #{item.duration})
        </foreach>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update audio_record
        <set>
            <if test="uploadId != null">
                upload_id = #{uploadId},
            </if>
            <if test="deptId != null">
                dept_id = #{deptId},
            </if>
            <if test="userid != null and userid != ''">
                userid = #{userid},
            </if>
            <if test="phone != null and phone != ''">
                phone = #{phone},
            </if>
            <if test="cnid != null and cnid != ''">
                cnid = #{cnid},
            </if>
            <if test="url != null and url != ''">
                url = #{url},
            </if>
            <if test="startTime != null">
                start_time = #{startTime},
            </if>
            <if test="endTime != null">
                end_time = #{endTime},
            </if>
            <if test="callType != null">
                call_type = #{callType},
            </if>
            <if test="uploadTime != null">
                upload_time = #{uploadTime},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="uploadMethod != null">
                upload_method = #{uploadMethod},
            </if>
            <if test="supplier != null and supplier != ''">
                supplier = #{supplier},
            </if>
            <if test="dept != null and dept != ''">
                dept = #{dept},
            </if>
            <if test="org != null and org != ''">
                org = #{org},
            </if>
            <if test="listened != null">
                listened = #{listened},
            </if>
            <if test="listenTime != null and listenTime != ''">
                listen_time = #{listenTime},
            </if>
            <if test="listener != null and listener != ''">
                listener = #{listener},
            </if>
            <if test="comment != null">
                comment = #{comment},
            </if>
            <if test="duration != null">
                duration = #{duration},
            </if>
            <if test="deleted != null">
                deleted = #{deleted},
            </if>
            <if test="operator != null and operator != ''">
                operator = #{operator},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from audio_record
        where id = #{id}
    </delete>

    <!--通过uploadId删除-->
    <delete id="deleteByUploadId">
        update audio_record
        set deleted = 1
        where upload_id = #{uploadId}
    </delete>

    <!--语音所属部门列表查询-->
    <select id="listDept" resultMap="AudioRecordMap">
        select
            distinct dept
        from audio_record
    </select>

    <!--语音所属机构列表查询-->
    <select id="listOrg" resultMap="AudioRecordMap">
        select
            distinct org
        from audio_record
    </select>

    <select id="queryDeptAudio" resultMap="AudioRecordMap">
        select
        id, upload_id, dept_id, userid, phone, cnid, start_time, end_time, call_type, upload_time, status,
        upload_method, supplier, dept, org, listened, deleted, operator, create_time
        from audio_record
        where dept_id = #{deptId}
        and deleted = 0
        limit #{limit}
    </select>

    <delete id="deleteByStartTime">
        delete
        from audio_record
        where start_time >= #{startDate}
        and start_time <![CDATA[ < ]]> #{endDate}
        and upload_method = 0
        <if test="deptId != null">
            and dept_id = #{deptId}
        </if>
    </delete>

    <select id="queryUploadAudio" resultMap="AudioRecordMap">
        select
            id, upload_id, dept_id, userid, phone, cnid, url, start_time, end_time, call_type, upload_time, status,
            upload_method, supplier, dept, org, listened, deleted, operator, agent_id, create_time
        from audio_record
        WHERE upload_method = 1
        <if test="deptIds != null">
            and dept_id in
            <foreach collection="deptIds" item="deptId" index="index" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
    </select>

    <select id="query94Empty" resultMap="AudioRecordMap">
        select
        id, phone, start_time, operator
        from audio_record
        where dept_id = #{deptId}
        and start_time >= #{startTime}
        and start_time <![CDATA[<=]]> #{endTime}
        and status = #{status}
        and url = ''
        and deleted = 0
    </select>
</mapper>

