<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
    (代码自动生成器生成)
    @auth Kawin赵健
-->
<mapper namespace="com.welab.databridge.dao.AudioRecordTempDao">

    <resultMap type="com.welab.databridge.entity.AudioRecordTemp" id="AudioRecordTempMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="deptId" column="dept_id" jdbcType="INTEGER"/>
        <result property="userid" column="userid" jdbcType="VARCHAR"/>
        <result property="phone" column="phone" jdbcType="VARCHAR"/>
        <result property="cnid" column="cnid" jdbcType="VARCHAR"/>
        <result property="url" column="url" jdbcType="VARCHAR"/>
        <result property="startTime" column="start_time" jdbcType="TIMESTAMP"/>
        <result property="uploadTime" column="upload_time" jdbcType="TIMESTAMP"/>
        <result property="uploadMethod" column="upload_method" jdbcType="INTEGER"/>
        <result property="supplier" column="supplier" jdbcType="VARCHAR"/>
        <result property="flag" column="flag" jdbcType="INTEGER"/>
        <result property="dir" column="dir" jdbcType="VARCHAR"/>
        <result property="operator" column="operator" jdbcType="VARCHAR"/>
    </resultMap>

    <!--根据ID查询单个-->
    <select id="getById" resultMap="AudioRecordTempMap">
        select id,
               dept_id,
               userid,
               phone,
               cnid,
               url,
               start_time,
               upload_time,
               upload_method,
               supplier,
               flag,
               dir,
               operator
        from audio_record_temp
        where id = #{id}
    </select>

    <!--列表条件查询-->
    <select id="queryByCondition" resultMap="AudioRecordTempMap">
        select
        id, dept_id, userid, phone, cnid, url, start_time, upload_time, upload_method, supplier, flag, dir, operator
        from audio_record_temp
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="deptId != null">
                and dept_id = #{deptId}
            </if>
            <if test="userid != null and userid != ''">
                and userid = #{userid}
            </if>
            <if test="phone != null and phone != ''">
                and phone = #{phone}
            </if>
            <if test="cnid != null and cnid != ''">
                and cnid = #{cnid}
            </if>
            <if test="url != null and url != ''">
                and url = #{url}
            </if>
            <if test="startTime != null">
                and start_time = #{startTime}
            </if>
            <if test="uploadTime != null">
                and upload_time = #{uploadTime}
            </if>
            <if test="uploadMethod != null">
                and upload_method = #{uploadMethod}
            </if>
            <if test="supplier != null and supplier != ''">
                and supplier = #{supplier}
            </if>
            <if test="flag != null">
                and flag = #{flag}
            </if>
            <if test="dir != null and dir != ''">
                and dir = #{dir}
            </if>
            <if test="operator != null and operator != ''">
                and operator = #{operator}
            </if>
        </where>

    </select>

    <select id="queryUnSync" resultMap="AudioRecordTempMap">
        select
        id, dept_id, userid, phone, cnid, url, start_time, upload_time, upload_method, supplier, flag, dir, operator
        from audio_record_temp
        where phone is not null
        and flag = 0
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into audio_record_temp(dept_id, userid, phone, cnid, url, start_time, upload_time, upload_method,
                                      supplier, flag, dir, operator)
        values (#{deptId}, #{userid}, #{phone}, #{cnid}, #{url}, #{startTime}, #{uploadTime}, #{uploadMethod},
                #{supplier}, #{flag}, #{dir}, #{operator})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update audio_record_temp
        <set>
            <if test="deptId != null">
                dept_id = #{deptId},
            </if>
            <if test="userid != null and userid != ''">
                userid = #{userid},
            </if>
            <if test="phone != null and phone != ''">
                phone = #{phone},
            </if>
            <if test="cnid != null and cnid != ''">
                cnid = #{cnid},
            </if>
            <if test="url != null and url != ''">
                url = #{url},
            </if>
            <if test="startTime != null">
                start_time = #{startTime},
            </if>
            <if test="uploadTime != null">
                upload_time = #{uploadTime},
            </if>
            <if test="uploadMethod != null">
                upload_method = #{uploadMethod},
            </if>
            <if test="supplier != null and supplier != ''">
                supplier = #{supplier},
            </if>
            <if test="flag != null">
                flag = #{flag},
            </if>
            <if test="dir != null and dir != ''">
                dir = #{dir},
            </if>
            <if test="operator != null and operator != ''">
                operator = #{operator},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from audio_record_temp
        where id = #{id}
    </delete>

</mapper>

