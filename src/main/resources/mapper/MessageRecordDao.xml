<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
    (代码自动生成器生成)
    @auth Kawin赵健
-->
<mapper namespace="com.welab.databridge.dao.MessageRecordDao">

    <resultMap type="com.welab.databridge.entity.MessageRecord" id="MessageRecordMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="uploadId" column="upload_id" jdbcType="INTEGER"/>
        <result property="deptId" column="dept_id" jdbcType="INTEGER"/>
        <result property="userid" column="userid" jdbcType="VARCHAR"/>
        <result property="phone" column="phone" jdbcType="VARCHAR"/>
        <result property="cnid" column="cnid" jdbcType="VARCHAR"/>
        <result property="content" column="content" jdbcType="VARCHAR"/>
        <result property="supplier" column="supplier" jdbcType="VARCHAR"/>
        <result property="sendTime" column="send_time" jdbcType="TIMESTAMP"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="uploadMethod" column="upload_method" jdbcType="INTEGER"/>
        <result property="deleted" column="deleted" jdbcType="INTEGER"/>
        <result property="operator" column="operator" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!--根据ID查询单个-->
    <select id="getById" resultMap="MessageRecordMap">
        select id,
               upload_id,
               dept_id,
               userid,
               phone,
               cnid,
               content,
               supplier,
               send_time,
               status,
               upload_method,
               deleted,
               operator,
               create_time, (select name from department where id = message_record.dept_id) as deptName
        from message_record
        where id = #{id}
    </select>

    <!--列表条件查询-->
    <select id="queryByCondition" resultMap="MessageRecordMap">
        select
        id, upload_id, dept_id, userid, phone, cnid, content, supplier, send_time, status, upload_method, deleted, operator,
        create_time, (select name from department where id = message_record.dept_id) as deptName
        from message_record
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="uploadId != null">
                and upload_id = #{uploadId}
            </if>
            <if test="deptIds != null">
                and dept_id in
                <foreach collection="deptIds" item="deptId" index="index" open="(" close=")" separator=",">
                    #{deptId}
                </foreach>
            </if>
            <if test="userids != null">
                and userid in
                <foreach collection="userids" item="userid" index="index" open="(" close=")" separator=",">
                    #{userid}
                </foreach>
            </if>
            <if test="phones != null">
                and phone in
                <foreach collection="phones" item="phone" index="index" open="(" close=")" separator=",">
                    #{phone}
                </foreach>
            </if>
            <if test="cnids != null">
                and cnid in
                <foreach collection="cnids" item="cnid" index="index" open="(" close=")" separator=",">
                    #{cnid}
                </foreach>
            </if>
            <if test="content != null and content != ''">
                and content = #{content}
            </if>
            <if test="supplier != null and supplier != ''">
                and supplier = #{supplier}
            </if>
            <if test="startTime != null and startTime != ''">
                and send_time >= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                and send_time <![CDATA[<=]]> #{endTime}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="uploadMethod != null">
                and upload_method = #{uploadMethod}
            </if>
            <if test="deleted != null">
                and deleted = #{deleted}
            </if>
            <if test="deleted == null">
                and deleted = 0
            </if>
            <if test="operator != null and operator != ''">
                and operator = #{operator}
            </if>
        </where>
        order by send_time desc
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into message_record(upload_id, dept_id, userid, phone, cnid, content, supplier, send_time, status,
                                   upload_method, operator, create_time)
        values (#{uploadId}, #{deptId}, #{userid}, #{phone}, #{cnid}, #{content}, #{supplier}, #{sendTime}, #{status},
                #{uploadMethod}, #{operator}, #{createTime})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update message_record
        <set>
            <if test="uploadId != null">
                upload_id = #{uploadId},
            </if>
            <if test="deptId != null">
                dept_id = #{deptId},
            </if>
            <if test="userid != null and userid != ''">
                userid = #{userid},
            </if>
            <if test="phone != null and phone != ''">
                phone = #{phone},
            </if>
            <if test="cnid != null and cnid != ''">
                cnid = #{cnid},
            </if>
            <if test="content != null and content != ''">
                content = #{content},
            </if>
            <if test="supplier != null and supplier != ''">
                supplier = #{supplier},
            </if>
            <if test="sendTime != null">
                send_time = #{sendTime},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="uploadMethod != null">
                upload_method = #{uploadMethod},
            </if>
            <if test="deleted != null">
                deleted = #{deleted},
            </if>
            <if test="operator != null and operator != ''">
                operator = #{operator},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from message_record
        where id = #{id}
    </delete>

    <!--通过uploadId删除-->
    <delete id="deleteByUploadId">
        update message_record
        set deleted = 1
        where upload_id = #{uploadId}
    </delete>

    <select id="queryDeptMsg" resultMap="MessageRecordMap">
        select
        id, upload_id, dept_id, userid, phone, cnid, content, supplier, send_time, status, upload_method, deleted, operator,
        create_time
        from message_record
        where dept_id = #{deptId}
        and deleted = 0
        limit #{limit}
    </select>
</mapper>

