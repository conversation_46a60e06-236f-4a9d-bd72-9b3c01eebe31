package com.welab.databridge.help;

import com.alibaba.fastjson.JSON;
import com.welab.databridge.vo.audio.AgentAudioVo;
import org.junit.Test;
import static org.junit.Assert.*;

/**
 * 坐席音频解析助手测试类
 */
public class AgentAudioHelperTest {

    @Test
    public void testParseAgentFileName() {
        // Test Format 1: M2DD-035 (字母数字组合-数字格式)
        AgentAudioVo result1 = AgentAudioHelper.parseAgentFileName("M2DD-035 15768330944 2023-10-16 11.24.11.mp3");
        assertEquals("M2DD-035", result1.getAgentId());
        assertEquals("15768330944", result1.getMobile());
        assertNotNull(result1.getStartTime());
        assertEquals("filename", result1.getSource());

        // Test Format 2: M1YY-003 (字母数字组合-数字格式)
        AgentAudioVo result2 = AgentAudioHelper.parseAgentFileName("M1YY-003 13303401113-20250201154900.mp3");
        assertEquals("M1YY-003", result2.getAgentId());
        assertEquals("13303401113", result2.getMobile());
        assertNotNull(result2.getStartTime());

        // Test Format 7: WWJY-001 (纯字母-数字格式)
        AgentAudioVo result7 = AgentAudioHelper.parseAgentFileName("WWJY-001-13345698752-20250708-142552-13345698752.MP3");
        assertEquals("WWJY-001", result7.getAgentId());
        assertEquals("13345698752", result7.getMobile());
        assertNotNull(result7.getStartTime());

        // Test Format 13: WWZM-213 (纯字母-数字格式)
        AgentAudioVo result13 = AgentAudioHelper.parseAgentFileName("WWZM-213 15861171031-20231017 121240.wav");
        assertEquals("WWZM-213", result13.getAgentId());
        assertEquals("15861171031", result13.getMobile());
        assertNotNull(result13.getStartTime());

        // Test Format 14: WWZM-213 (纯字母-数字格式)
        AgentAudioVo result14 = AgentAudioHelper.parseAgentFileName("M2JY-009-13261124696-20250716-134723.wav");
        assertEquals("M2JY-009", result14.getAgentId());
        assertEquals("13261124696", result14.getMobile());
        assertNotNull(result13.getStartTime());

        // Test invalid filename
        AgentAudioVo resultInvalid = AgentAudioHelper.parseAgentFileName("invalid_filename.mp3");
        assertNull(resultInvalid.getAgentId());
        assertNull(resultInvalid.getMobile());
        assertNull(resultInvalid.getStartTime());
        assertEquals("filename", resultInvalid.getSource());
    }

    @Test
    public void testParseAgentFileNameWithNullInput() {
        AgentAudioVo result = AgentAudioHelper.parseAgentFileName(null);
        assertNull(result.getAgentId());
        assertNull(result.getMobile());
        assertNull(result.getStartTime());
        assertEquals("filename", result.getSource());
    }

    @Test
    public void testParseAgentFileNameWithEmptyInput() {
        AgentAudioVo result = AgentAudioHelper.parseAgentFileName("");
        assertNull(result.getAgentId());
        assertNull(result.getMobile());
        assertNull(result.getStartTime());
        assertEquals("filename", result.getSource());
    }

    @Test
    public void testAgentIdFormats() {
        // 测试字母数字组合-数字格式 (如: M2DD-035, M1YY-003, M2ZM-006)
        AgentAudioVo result1 = AgentAudioHelper.parseAgentFileName("M2DD-035 15768330944 2023-10-16 11.24.11.mp3");
        assertEquals("M2DD-035", result1.getAgentId());

        AgentAudioVo result2 = AgentAudioHelper.parseAgentFileName("M1YY-003 13303401113-20250201154900.mp3");
        assertEquals("M1YY-003", result2.getAgentId());

        AgentAudioVo result3 = AgentAudioHelper.parseAgentFileName("M2ZM-006_13962472850_20250707-110609.amr");
        assertEquals("M2ZM-006", result3.getAgentId());

        // 测试纯字母-数字格式 (如: WWJY-001, WWZM-213, WWYXTJ-003)
        AgentAudioVo result4 = AgentAudioHelper.parseAgentFileName("WWJY-001-13345698752-20250708-142552-13345698752.MP3");
        assertEquals("WWJY-001", result4.getAgentId());

        AgentAudioVo result5 = AgentAudioHelper.parseAgentFileName("WWZM-213 15861171031-20231017 121240.wav");
        assertEquals("WWZM-213", result5.getAgentId());

        AgentAudioVo result6 = AgentAudioHelper.parseAgentFileName("WWYXTJ-003 17665271076 2023-10-17-14-41-25");
        assertEquals("WWYXTJ-003", result6.getAgentId());

        // 测试复杂字母组合-数字格式 (如: WWJLXTJ-003, M2JLXTJ-003)
        AgentAudioVo result7 = AgentAudioHelper.parseAgentFileName("WWJLXTJ-003 1948-20250707-182046-18185266018-45110022");
        assertEquals("WWJLXTJ-003", result7.getAgentId());

        AgentAudioVo result8 = AgentAudioHelper.parseAgentFileName("M2JLXTJ-003 15684099986-2507051644.awb");
        assertEquals("M2JLXTJ-003", result8.getAgentId());
    }
}
