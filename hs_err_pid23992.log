#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 185936 bytes for Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (allocation.cpp:389), pid=23992, tid=0x0000000000005ca0
#
# JRE version: Java(TM) SE Runtime Environment (8.0_301-b09) (build 1.8.0_301-b09)
# Java VM: Java HotSpot(TM) 64-Bit Server VM (25.301-b09 mixed mode windows-amd64 compressed oops)
# Failed to write core dump. Minidumps are not enabled by default on client versions of Windows
#

---------------  T H R E A D  ---------------

Current thread (0x0000018a5aeb8800):  JavaThread "C2 CompilerThread2" daemon [_thread_in_native, id=23712, stack(0x0000006429100000,0x0000006429200000)]

Stack: [0x0000006429100000,0x0000006429200000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x32ed79]
V  [jvm.dll+0x275722]
V  [jvm.dll+0x2763cd]
V  [jvm.dll+0x26ce05]
V  [jvm.dll+0xd551c]
V  [jvm.dll+0xd5d1c]
V  [jvm.dll+0x470143]
V  [jvm.dll+0x437c50]
V  [jvm.dll+0x440c55]
V  [jvm.dll+0x440015]
V  [jvm.dll+0x42ae57]
V  [jvm.dll+0xac1bb]
V  [jvm.dll+0xaa81b]
V  [jvm.dll+0x23ddc2]
V  [jvm.dll+0x2923dc]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17374]
C  [ntdll.dll+0x4cc91]


Current CompileTask:
C2:   1913  258  s!   4       sun.misc.URLClassPath::getLoader (243 bytes)


---------------  P R O C E S S  ---------------

Java Threads: ( => current thread )
  0x0000018a5af2d000 JavaThread "Service Thread" daemon [_thread_blocked, id=22152, stack(0x0000006429300000,0x0000006429400000)]
  0x0000018a5aec2000 JavaThread "C1 CompilerThread3" daemon [_thread_blocked, id=25340, stack(0x0000006429200000,0x0000006429300000)]
=>0x0000018a5aeb8800 JavaThread "C2 CompilerThread2" daemon [_thread_in_native, id=23712, stack(0x0000006429100000,0x0000006429200000)]
  0x0000018a5aea4000 JavaThread "C2 CompilerThread1" daemon [_thread_in_native, id=616, stack(0x0000006429000000,0x0000006429100000)]
  0x0000018a5aeb1800 JavaThread "C2 CompilerThread0" daemon [_thread_blocked, id=2876, stack(0x0000006428f00000,0x0000006429000000)]
  0x0000018a5ae6b000 JavaThread "IntelliJ Suspend Helper" daemon [_thread_blocked, id=24228, stack(0x0000006428e00000,0x0000006428f00000)]
  0x0000018a5ae3a000 JavaThread "JDWP Command Reader" daemon [_thread_in_native, id=2528, stack(0x0000006428d00000,0x0000006428e00000)]
  0x0000018a5ae39000 JavaThread "JDWP Event Helper Thread" daemon [_thread_blocked, id=22724, stack(0x0000006428c00000,0x0000006428d00000)]
  0x0000018a5ae24800 JavaThread "JDWP Transport Listener: dt_socket" daemon [_thread_blocked, id=25820, stack(0x0000006428b00000,0x0000006428c00000)]
  0x0000018a591f6800 JavaThread "Attach Listener" daemon [_thread_blocked, id=13148, stack(0x0000006428a00000,0x0000006428b00000)]
  0x0000018a591f5800 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=25308, stack(0x0000006428900000,0x0000006428a00000)]
  0x0000018a591cd000 JavaThread "Finalizer" daemon [_thread_blocked, id=16964, stack(0x0000006428800000,0x0000006428900000)]
  0x0000018a591c6000 JavaThread "Reference Handler" daemon [_thread_blocked, id=2824, stack(0x0000006428700000,0x0000006428800000)]
  0x0000018a3e086800 JavaThread "main" [_thread_in_vm, id=2520, stack(0x0000006427d00000,0x0000006427e00000)]

Other Threads:
  0x0000018a591a0000 VMThread [stack: 0x0000006428600000,0x0000006428700000] [id=24872]
  0x0000018a5af54800 WatcherThread [stack: 0x0000006429400000,0x0000006429500000] [id=9836]

VM state:not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

heap address: 0x00000006c3200000, size: 4046 MB, Compressed Oops mode: Zero based, Oop shift amount: 3
Narrow klass base: 0x0000000000000000, Narrow klass shift: 3
Compressed class space size: 1073741824 Address: 0x00000007c0000000

Heap:
 PSYoungGen      total 75776K, used 23658K [0x000000076bb80000, 0x0000000771000000, 0x00000007c0000000)
  eden space 65024K, 36% used [0x000000076bb80000,0x000000076d29abf0,0x000000076fb00000)
  from space 10752K, 0% used [0x0000000770580000,0x0000000770580000,0x0000000771000000)
  to   space 10752K, 0% used [0x000000076fb00000,0x000000076fb00000,0x0000000770580000)
 ParOldGen       total 173568K, used 0K [0x00000006c3200000, 0x00000006cdb80000, 0x000000076bb80000)
  object space 173568K, 0% used [0x00000006c3200000,0x00000006c3200000,0x00000006cdb80000)
 Metaspace       used 5296K, capacity 5460K, committed 5504K, reserved 1056768K
  class space    used 582K, capacity 627K, committed 640K, reserved 1048576K

Card table byte_map: [0x0000018a4f170000,0x0000018a4f960000] byte_map_base: 0x0000018a4bb57000

Marking Bits: (ParMarkBitMap*) 0x0000000055c97fe0
 Begin Bits: [0x0000018a4feb0000, 0x0000018a53de8000)
 End Bits:   [0x0000018a53de8000, 0x0000018a57d20000)

Polling page: 0x0000018a3e290000

CodeCache: size=245760Kb used=2019Kb max_used=2026Kb free=243740Kb
 bounds [0x0000018a3fdb0000, 0x0000018a40020000, 0x0000018a4edb0000]
 total_blobs=613 nmethods=330 adapters=204
 compilation: enabled

Compilation events (10 events):
Event: 1.886 Thread 0x0000018a5aec2000  268       3       sun.misc.PerfCounter::addElapsedTimeFrom (10 bytes)
Event: 1.886 Thread 0x0000018a5aec2000 nmethod 268 0x0000018a3ff7a610 code [0x0000018a3ff7a7c0, 0x0000018a3ff7af40]
Event: 1.886 Thread 0x0000018a5aec2000  272   !   3       com.intellij.rt.debugger.agent.SpilledVariablesTransformer$SpillingTransformer::transform (83 bytes)
Event: 1.887 Thread 0x0000018a5aec2000 nmethod 272 0x0000018a3ff84ad0 code [0x0000018a3ff84da0, 0x0000018a3ff860d0]
Event: 1.887 Thread 0x0000018a5aec2000  273   !   3       java.util.zip.Inflater::inflate (113 bytes)
Event: 1.887 Thread 0x0000018a5aec2000 nmethod 273 0x0000018a3ff79550 code [0x0000018a3ff79720, 0x0000018a3ff79e00]
Event: 1.887 Thread 0x0000018a5aec2000  275       3       java.util.BitSet::get (69 bytes)
Event: 1.887 Thread 0x0000018a5aec2000 nmethod 275 0x0000018a3ff86e10 code [0x0000018a3ff87020, 0x0000018a3ff879b0]
Event: 1.888 Thread 0x0000018a5aec2000  276       3       java.lang.ref.SoftReference::get (29 bytes)
Event: 1.888 Thread 0x0000018a5aec2000 nmethod 276 0x0000018a3ff78f50 code [0x0000018a3ff790a0, 0x0000018a3ff79298]

GC Heap History (0 events):
No events

Deoptimization events (0 events):
No events

Classes redefined (1 events):
Event: 0.451 Thread 0x0000018a591a0000 redefined class name=java.lang.Throwable, count=1

Internal exceptions (7 events):
Event: 0.078 Thread 0x0000018a3e086800 Exception <a 'java/lang/NoSuchMethodError': Method sun.misc.Unsafe.defineClass(Ljava/lang/String;[BII)Ljava/lang/Class; name or signature does not match> (0x000000076bb87cc0) thrown at [C:\jenkins\workspace\8-2-build-windows-amd64-cygwin\jdk8u301\1513\hot
Event: 0.078 Thread 0x0000018a3e086800 Exception <a 'java/lang/NoSuchMethodError': Method sun.misc.Unsafe.prefetchRead(Ljava/lang/Object;J)V name or signature does not match> (0x000000076bb87fa8) thrown at [C:\jenkins\workspace\8-2-build-windows-amd64-cygwin\jdk8u301\1513\hotspot\src\share\vm\
Event: 1.702 Thread 0x0000018a3e086800 Exception <a 'java/io/FileNotFoundException'> (0x000000076cb6d558) thrown at [C:\jenkins\workspace\8-2-build-windows-amd64-cygwin\jdk8u301\1513\hotspot\src\share\vm\prims\jni.cpp, line 710]
Event: 1.724 Thread 0x0000018a3e086800 Exception <a 'java/security/PrivilegedActionException'> (0x000000076cc5ccf8) thrown at [C:\jenkins\workspace\8-2-build-windows-amd64-cygwin\jdk8u301\1513\hotspot\src\share\vm\prims\jvm.cpp, line 1523]
Event: 1.725 Thread 0x0000018a3e086800 Exception <a 'java/security/PrivilegedActionException'> (0x000000076cc5d110) thrown at [C:\jenkins\workspace\8-2-build-windows-amd64-cygwin\jdk8u301\1513\hotspot\src\share\vm\prims\jvm.cpp, line 1523]
Event: 1.725 Thread 0x0000018a3e086800 Exception <a 'java/security/PrivilegedActionException'> (0x000000076cc5fa10) thrown at [C:\jenkins\workspace\8-2-build-windows-amd64-cygwin\jdk8u301\1513\hotspot\src\share\vm\prims\jvm.cpp, line 1523]
Event: 1.725 Thread 0x0000018a3e086800 Exception <a 'java/security/PrivilegedActionException'> (0x000000076cc5fe28) thrown at [C:\jenkins\workspace\8-2-build-windows-amd64-cygwin\jdk8u301\1513\hotspot\src\share\vm\prims\jvm.cpp, line 1523]

Events (10 events):
Event: 1.887 loading class org/junit/FixMethodOrder
Event: 1.887 loading class org/junit/FixMethodOrder done
Event: 1.887 loading class java/util/Arrays$LegacyMergeSort
Event: 1.887 loading class java/util/Arrays$LegacyMergeSort done
Event: 1.887 loading class java/util/TimSort
Event: 1.887 loading class java/util/TimSort done
Event: 1.888 loading class java/util/LinkedHashMap$LinkedValues
Event: 1.888 loading class java/util/LinkedHashMap$LinkedValues done
Event: 1.888 loading class java/util/LinkedHashMap$LinkedValueIterator
Event: 1.888 loading class java/util/LinkedHashMap$LinkedValueIterator done


Dynamic libraries:
0x00007ff7b9c30000 - 0x00007ff7b9c77000 	D:\software\jdk\bin\java.exe
0x00007fff7af30000 - 0x00007fff7b128000 	C:\windows\SYSTEM32\ntdll.dll
0x00007fff7adc0000 - 0x00007fff7ae82000 	C:\windows\System32\KERNEL32.DLL
0x00007fff78940000 - 0x00007fff78c36000 	C:\windows\System32\KERNELBASE.dll
0x00007fff78ff0000 - 0x00007fff790a1000 	C:\windows\System32\ADVAPI32.dll
0x00007fff78f50000 - 0x00007fff78fee000 	C:\windows\System32\msvcrt.dll
0x00007fff790b0000 - 0x00007fff7914f000 	C:\windows\System32\sechost.dll
0x00007fff7a620000 - 0x00007fff7a743000 	C:\windows\System32\RPCRT4.dll
0x00007fff78ef0000 - 0x00007fff78f17000 	C:\windows\System32\bcrypt.dll
0x00007fff792c0000 - 0x00007fff7945d000 	C:\windows\System32\USER32.dll
0x00007fff78f20000 - 0x00007fff78f42000 	C:\windows\System32\win32u.dll
0x00007fff7a4c0000 - 0x00007fff7a4eb000 	C:\windows\System32\GDI32.dll
0x00007fff785c0000 - 0x00007fff786d9000 	C:\windows\System32\gdi32full.dll
0x00007fff786e0000 - 0x00007fff7877d000 	C:\windows\System32\msvcp_win.dll
0x00007fff78df0000 - 0x00007fff78ef0000 	C:\windows\System32\ucrtbase.dll
0x00007fff5d890000 - 0x00007fff5db2a000 	C:\windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7\COMCTL32.dll
0x00007fff79460000 - 0x00007fff7948f000 	C:\windows\System32\IMM32.DLL
0x00007fff38db0000 - 0x00007fff38e61000 	C:\Program Files\McAfee\Endpoint Security\Threat Prevention\Ips\EpMPApi.dll
0x00007fff78810000 - 0x00007fff78883000 	C:\windows\System32\WINTRUST.dll
0x00007fff78c90000 - 0x00007fff78ded000 	C:\windows\System32\CRYPT32.dll
0x00007fff78180000 - 0x00007fff78192000 	C:\windows\SYSTEM32\MSASN1.dll
0x0000000055d30000 - 0x0000000055d3c000 	C:\Program Files\McAfee\Endpoint Security\Threat Prevention\Ips\EpMPThe.dll
0x00007fff38d50000 - 0x00007fff38dab000 	C:\Program Files\McAfee\Endpoint Security\Threat Prevention\Ips\HIPHandlers64.dll
0x00007fff799e0000 - 0x00007fff7a14e000 	C:\windows\System32\SHELL32.dll
0x00007fff79490000 - 0x00007fff794eb000 	C:\windows\System32\SHLWAPI.dll
0x00007fff779a0000 - 0x00007fff779db000 	C:\windows\SYSTEM32\IPHLPAPI.DLL
0x00007fff740d0000 - 0x00007fff742d1000 	C:\windows\SYSTEM32\dbghelp.dll
0x00007fff5d770000 - 0x00007fff5d785000 	D:\software\jdk\jre\bin\vcruntime140.dll
0x00007fff3c780000 - 0x00007fff3c81b000 	D:\software\jdk\jre\bin\msvcp140.dll
0x00000000554b0000 - 0x0000000055d10000 	D:\software\jdk\jre\bin\server\jvm.dll
0x00007fff79200000 - 0x00007fff79208000 	C:\windows\System32\PSAPI.DLL
0x00007fff709e0000 - 0x00007fff709ea000 	C:\windows\SYSTEM32\VERSION.dll
0x00007fff5dba0000 - 0x00007fff5dba9000 	C:\windows\SYSTEM32\WSOCK32.dll
0x00007fff521a0000 - 0x00007fff521c7000 	C:\windows\SYSTEM32\WINMM.dll
0x00007fff794f0000 - 0x00007fff7955b000 	C:\windows\System32\WS2_32.dll
0x00007fff76200000 - 0x00007fff76212000 	C:\windows\SYSTEM32\kernel.appcore.dll
0x00007fff5d760000 - 0x00007fff5d770000 	D:\software\jdk\jre\bin\verify.dll
0x00007fff53170000 - 0x00007fff5319b000 	D:\software\jdk\jre\bin\java.dll
0x00007fff4b600000 - 0x00007fff4b636000 	D:\software\jdk\jre\bin\jdwp.dll
0x00007fff5ccc0000 - 0x00007fff5ccc9000 	D:\software\jdk\jre\bin\npt.dll
0x00007fff4b650000 - 0x00007fff4b680000 	D:\software\jdk\jre\bin\instrument.dll
0x00007fff50180000 - 0x00007fff50198000 	D:\software\jdk\jre\bin\zip.dll
0x00007fff76500000 - 0x00007fff76ca4000 	C:\windows\SYSTEM32\windows.storage.dll
0x00007fff7a150000 - 0x00007fff7a4a3000 	C:\windows\System32\combase.dll
0x00007fff77f10000 - 0x00007fff77f3b000 	C:\windows\SYSTEM32\Wldp.dll
0x00007fff7aa80000 - 0x00007fff7ab4d000 	C:\windows\System32\OLEAUT32.dll
0x00007fff79210000 - 0x00007fff792bd000 	C:\windows\System32\SHCORE.dll
0x00007fff784f0000 - 0x00007fff78514000 	C:\windows\SYSTEM32\profapi.dll
0x00007fff54100000 - 0x00007fff5410a000 	D:\software\jdk\jre\bin\dt_socket.dll
0x00007fff77cf0000 - 0x00007fff77d5a000 	C:\windows\system32\mswsock.dll
0x00007fff78780000 - 0x00007fff78802000 	C:\windows\System32\bcryptPrimitives.dll

VM Arguments:
jvm_args: -agentlib:jdwp=transport=dt_socket,address=127.0.0.1:7615,suspend=y,server=n -javaagent:D:\software\idea\ideaIU-2024.1.4.win\plugins\java\lib\rt\debugger-agent.jar=file:///C:/Users/<USER>/AppData/Local/Temp/capture10206673368382441454.props -ea -Didea.test.cyclic.buffer.size=1048576 -Dkotlinx.coroutines.debug.enable.creation.stack.trace=false -Ddebugger.agent.enable.coroutines=true -Dkotlinx.coroutines.debug.enable.flows.stack.trace=true -Dkotlinx.coroutines.debug.enable.mutable.state.flows.stack.trace=true -Dfile.encoding=UTF-8 
java_command: com.intellij.rt.junit.JUnitStarter -ideVersion5 -junit4 com.welab.databridge.help.AgentAudioHelperTest,testParseAgentFileName
java_class_path (initial): C:\Users\<USER>\AppData\Local\Temp\classpath131136325.jar;D:\software\idea\ideaIU-2024.1.4.win\plugins\java\lib\rt\debugger-agent.jar
Launcher Type: SUN_STANDARD

Environment Variables:
JAVA_HOME=D:\software\jdk
PATH=D:\software\shadowbot;C:\Python312\Scripts\;C:\Python312\;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\windows\system32;C:\windows;C:\windows\System32\Wbem;C:\windows\System32\WindowsPowerShell\v1.0\;C:\windows\System32\OpenSSH\;C:\Program Files\TortoiseGit\bin;D:\software\maven\apache-maven-3.8.6-bin\apache-maven-3.8.6\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310;D:\download\ffmpeg-master-latest-win64-gpl-shared\ffmpeg-master-latest-win64-gpl-shared\bin;C:\Program Files\dotnet\;D:\software\Git\cmd;D:\software\nodejs\;D:\software\pandoc\;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;D:\nodejs\node_global\;D:\software\shadowbot;C:\Users\<USER>\AppData\Local\UniGetUI\Chocolatey\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\download\OneCommander3.23.0.0;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe;C:\Users\<USER>\AppData\Roaming\npm;
USERNAME=leon.li
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 140 Stepping 1, GenuineIntel



---------------  S Y S T E M  ---------------

OS: Windows 10.0 , 64 bit Build 19041 (10.0.19041.5915)

CPU:total 8 (initial active 8) (4 cores per cpu, 2 threads per core) family 6 model 140 stepping 1, cmov, cx8, fxsr, mmx, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, avx, avx2, aes, clmul, erms, 3dnowpref, lzcnt, ht, tsc, tscinvbit, bmi1, bmi2, adx

Memory: 4k page, physical 16571704k(1072856k free), swap 27057464k(2072k free)

vm_info: Java HotSpot(TM) 64-Bit Server VM (25.301-b09) for windows-amd64 JRE (1.8.0_301-b09), built on Jun  9 2021 06:46:21 by "java_re" with MS VC++ 15.9 (VS2017)

time: Tue Aug  5 17:34:17 2025
timezone: �й���׼ʱ��
elapsed time: 1.916604 seconds (0d 0h 0m 1s)

